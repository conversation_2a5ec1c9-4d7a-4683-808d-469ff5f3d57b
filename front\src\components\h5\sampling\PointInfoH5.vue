<template>
  <div class="point-info-h5">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 点位信息 -->
    <div v-else class="point-content">
      <!-- 基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>点位名称：</label>
            <el-input 
              v-model="pointInfo.pointName" 
              placeholder="请输入点位名称"
              @blur="savePointInfo"
            />
          </div>
          <div class="info-item">
            <label>采样时间：</label>
            <el-date-picker
              v-model="pointInfo.samplingTime"
              type="datetime"
              placeholder="选择采样时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="savePointInfo"
              style="width: 100%;"
            />
          </div>
        </div>
      </div>

      <!-- 位置信息 -->
      <div class="section">
        <h3 class="section-title">
          位置信息
          <el-button
            type="primary"
            size="small"
            @click="getCurrentLocation"
            :loading="locationLoading"
          >
            <el-icon v-if="!locationLoading"><Location /></el-icon>
            {{ locationLoading ? '获取中...' : '获取位置' }}
          </el-button>
        </h3>
        <div class="info-grid">
          <!-- 位置获取提示 -->
          <div class="location-tips">
            <el-alert
              title="位置获取说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="tips-content">
                  <p>• 点击"获取位置"按钮自动获取当前坐标</p>
                  <p>• 需要允许浏览器访问位置权限</p>
                  <p>• 也可以手动输入经纬度坐标</p>
                </div>
              </template>
            </el-alert>
          </div>

          <div class="info-item">
            <label>经度：</label>
            <el-input
              v-model="pointInfo.longitude"
              placeholder="如：116.397428"
              @blur="savePointInfo"
            >
              <template #suffix>
                <span class="coordinate-unit">°</span>
              </template>
            </el-input>
          </div>
          <div class="info-item">
            <label>纬度：</label>
            <el-input
              v-model="pointInfo.latitude"
              placeholder="如：39.90923"
              @blur="savePointInfo"
            >
              <template #suffix>
                <span class="coordinate-unit">°</span>
              </template>
            </el-input>
          </div>
          <div class="info-item">
            <label>海拔高度：</label>
            <el-input
              v-model="pointInfo.altitude"
              placeholder="请输入海拔高度"
              type="number"
              @blur="savePointInfo"
            >
              <template #suffix>
                <span class="coordinate-unit">米</span>
              </template>
            </el-input>
          </div>

          <!-- 坐标显示 -->
          <div v-if="pointInfo.longitude && pointInfo.latitude" class="coordinate-display">
            <el-tag type="success" size="small">
              <el-icon><Location /></el-icon>
              坐标: {{ pointInfo.longitude }}, {{ pointInfo.latitude }}
              <span v-if="pointInfo.altitude"> (海拔: {{ pointInfo.altitude }}米)</span>
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 环境信息 -->
      <div class="section">
        <h3 class="section-title">环境信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>天气状况：</label>
            <el-select
              v-model="pointInfo.weatherCondition"
              placeholder="选择天气状况"
              @change="savePointInfo"
              style="width: 100%;"
            >
              <el-option label="晴" value="晴" />
              <el-option label="多云" value="多云" />
              <el-option label="阴" value="阴" />
              <el-option label="小雨" value="小雨" />
              <el-option label="中雨" value="中雨" />
              <el-option label="大雨" value="大雨" />
              <el-option label="雪" value="雪" />
              <el-option label="雾" value="雾" />
            </el-select>
          </div>
          <div class="info-item">
            <label>温度(°C)：</label>
            <el-input 
              v-model="pointInfo.temperature" 
              placeholder="请输入温度"
              type="number"
              @blur="savePointInfo"
            />
          </div>
          <div class="info-item">
            <label>湿度(%)：</label>
            <el-input 
              v-model="pointInfo.humidity" 
              placeholder="请输入湿度"
              type="number"
              @blur="savePointInfo"
            />
          </div>
          <div class="info-item">
            <label>风速(m/s)：</label>
            <el-input 
              v-model="pointInfo.windSpeed" 
              placeholder="请输入风速"
              type="number"
              @blur="savePointInfo"
            />
          </div>
          <div class="info-item">
            <label>风向：</label>
            <el-select
              v-model="pointInfo.windDirection"
              placeholder="选择风向"
              @change="savePointInfo"
              style="width: 100%;"
            >
              <el-option label="北" value="北" />
              <el-option label="东北" value="东北" />
              <el-option label="东" value="东" />
              <el-option label="东南" value="东南" />
              <el-option label="南" value="南" />
              <el-option label="西南" value="西南" />
              <el-option label="西" value="西" />
              <el-option label="西北" value="西北" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 点位照片 -->
      <div class="section">
        <h3 class="section-title">点位照片</h3>
        <div class="photo-upload">
          <el-upload
            :action="uploadAction"
            :headers="uploadHeaders"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handlePointPhotoSuccess"
            accept="image/*"
            :disabled="uploading"
          >
            <div class="upload-area">
              <img v-if="pointInfo.pointPhotoUrl" :src="getImageUrl(pointInfo.pointPhotoUrl)" class="uploaded-image" />
              <div v-else class="upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div class="upload-text">点击上传点位照片</div>
              </div>
              <div v-if="uploading" class="upload-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
              </div>
            </div>
          </el-upload>
        </div>
      </div>

      <!-- 环境照片 -->
      <div class="section">
        <h3 class="section-title">环境照片</h3>
        <div class="environment-photos">
          <div class="photo-grid">
            <!-- 东侧环境 -->
            <div class="photo-item">
              <div class="photo-label">东侧环境</div>
              <el-upload
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="(response) => handleEnvironmentPhotoSuccess(response, 'east')"
                accept="image/*"
              >
                <div class="upload-area small">
                  <img v-if="pointInfo.eastPhotoUrl" :src="getImageUrl(pointInfo.eastPhotoUrl)" class="uploaded-image" />
                  <div v-else class="upload-placeholder">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- 南侧环境 -->
            <div class="photo-item">
              <div class="photo-label">南侧环境</div>
              <el-upload
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="(response) => handleEnvironmentPhotoSuccess(response, 'south')"
                accept="image/*"
              >
                <div class="upload-area small">
                  <img v-if="pointInfo.southPhotoUrl" :src="getImageUrl(pointInfo.southPhotoUrl)" class="uploaded-image" />
                  <div v-else class="upload-placeholder">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- 西侧环境 -->
            <div class="photo-item">
              <div class="photo-label">西侧环境</div>
              <el-upload
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="(response) => handleEnvironmentPhotoSuccess(response, 'west')"
                accept="image/*"
              >
                <div class="upload-area small">
                  <img v-if="pointInfo.westPhotoUrl" :src="getImageUrl(pointInfo.westPhotoUrl)" class="uploaded-image" />
                  <div v-else class="upload-placeholder">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- 北侧环境 -->
            <div class="photo-item">
              <div class="photo-label">北侧环境</div>
              <el-upload
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="(response) => handleEnvironmentPhotoSuccess(response, 'north')"
                accept="image/*"
              >
                <div class="upload-area small">
                  <img v-if="pointInfo.northPhotoUrl" :src="getImageUrl(pointInfo.northPhotoUrl)" class="uploaded-image" />
                  <div v-else class="upload-placeholder">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="section">
        <h3 class="section-title">备注信息</h3>
        <div class="info-grid">
          <div class="info-item full-width">
            <el-input
              v-model="pointInfo.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              @blur="savePointInfo"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { Loading, Plus, Location } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getPointInfoByGroupId, updatePointInfo, createPointInfo } from '@/api/sampling/pointInfo'
import { getToken } from '@/utils/auth'

// Props
const props = defineProps({
  groupId: {
    type: Number,
    required: true
  },
  taskData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['refresh'])

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(true)
const locationLoading = ref(false)
const uploading = ref(false)
const pointInfo = ref({
  pointName: '',
  samplingTime: '',
  longitude: '',
  latitude: '',
  altitude: '',
  weatherCondition: '',
  temperature: '',
  humidity: '',
  windSpeed: '',
  windDirection: '',
  pointPhotoUrl: '',
  eastPhotoUrl: '',
  southPhotoUrl: '',
  westPhotoUrl: '',
  northPhotoUrl: '',
  remarks: ''
})

// 上传配置
const uploadAction = ref(`${import.meta.env.VITE_APP_BASE_API}/common/upload`)
const uploadHeaders = ref({
  Authorization: 'Bearer ' + getToken()
})

// 方法
const loadPointInfo = async () => {
  try {
    loading.value = true
    const response = await getPointInfoByGroupId(props.groupId)
    if (response.code === 200 && response.data) {
      pointInfo.value = { ...pointInfo.value, ...response.data }
    }
  } catch (error) {
    console.error('加载点位信息失败:', error)
    // 保持空状态，让用户可以手动输入信息
  } finally {
    loading.value = false
  }
}

const savePointInfo = async () => {
  try {
    const data = {
      ...pointInfo.value,
      samplingTaskGroupId: props.groupId  // 修正字段名
    }

    if (pointInfo.value.id) {
      // 更新现有点位信息
      await updatePointInfo(pointInfo.value.id, data)
    } else {
      // 创建新的点位信息
      const response = await createPointInfo(data)
      if (response.code === 200 && response.data) {
        pointInfo.value.id = response.data.id
      }
    }
    emit('refresh')
  } catch (error) {
    console.error('保存点位信息失败:', error)
    ElMessage.error('保存失败')
  }
}

const getCurrentLocation = () => {
  if (!navigator.geolocation) {
    ElMessage.error('浏览器不支持地理位置获取，请手动输入坐标')
    return
  }

  // 检查是否为HTTPS或localhost
  const isSecureContext = window.location.protocol === 'https:' ||
                          window.location.hostname === 'localhost' ||
                          window.location.hostname === '127.0.0.1'

  if (!isSecureContext) {
    ElMessage.warning('位置获取需要HTTPS协议，请手动输入坐标')
    return
  }

  locationLoading.value = true
  ElMessage.info('正在获取位置信息，请允许浏览器访问您的位置...')

  navigator.geolocation.getCurrentPosition(
    (position) => {
      pointInfo.value.longitude = position.coords.longitude.toFixed(6)
      pointInfo.value.latitude = position.coords.latitude.toFixed(6)
      if (position.coords.altitude && position.coords.altitude !== null) {
        pointInfo.value.altitude = Math.round(position.coords.altitude)
      }

      // 显示获取到的坐标信息
      const accuracy = position.coords.accuracy ? `(精度: ${Math.round(position.coords.accuracy)}米)` : ''
      ElMessage.success(`位置获取成功 ${accuracy}`)

      savePointInfo()
      locationLoading.value = false
    },
    (error) => {
      locationLoading.value = false
      let errorMsg = '位置获取失败'
      let suggestion = '请手动输入坐标'

      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMsg = '位置权限被拒绝'
          suggestion = '请在浏览器设置中允许位置访问，或手动输入坐标'
          break
        case error.POSITION_UNAVAILABLE:
          errorMsg = '位置信息不可用'
          suggestion = '请检查设备定位服务是否开启，或手动输入坐标'
          break
        case error.TIMEOUT:
          errorMsg = '位置请求超时'
          suggestion = '请检查网络连接或手动输入坐标'
          break
      }

      ElMessage({
        message: `${errorMsg}，${suggestion}`,
        type: 'warning',
        duration: 5000,
        showClose: true
      })
    },
    {
      enableHighAccuracy: false,  // 降低精度要求，提高成功率
      timeout: 15000,             // 增加超时时间
      maximumAge: 300000          // 5分钟内的缓存位置可用
    }
  )
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }

  uploading.value = true
  return true
}

const handlePointPhotoSuccess = (response) => {
  uploading.value = false
  if (response.code === 200) {
    pointInfo.value.pointPhotoUrl = response.url
    savePointInfo()
    ElMessage.success('点位照片上传成功')
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

const handleEnvironmentPhotoSuccess = (response, direction) => {
  if (response.code === 200) {
    pointInfo.value[`${direction}PhotoUrl`] = response.url
    savePointInfo()
    ElMessage.success('环境照片上传成功')
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

const getImageUrl = (url) => {
  if (!url) return ''
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  return `${import.meta.env.VITE_APP_BASE_URL || 'http://127.0.0.1:9099'}${url}`
}

// 生命周期
onMounted(() => {
  loadPointInfo()
})
</script>

<style scoped>
.point-info-h5 {
  padding: 15px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.section {
  background: white;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-grid {
  padding: 15px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item.full-width {
  width: 100%;
}

.info-item label {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.photo-upload {
  padding: 15px;
}

.upload-area {
  width: 100%;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-area.small {
  height: 120px;
}

.upload-area:hover {
  border-color: #409EFF;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  text-align: center;
  color: #999;
}

.upload-placeholder .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.environment-photos {
  padding: 15px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.photo-item {
  text-align: center;
}

.photo-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.location-tips {
  margin-bottom: 15px;
}

.tips-content {
  font-size: 13px;
  line-height: 1.4;
}

.tips-content p {
  margin: 2px 0;
}

.coordinate-unit {
  color: #999;
  font-size: 12px;
}

.coordinate-display {
  margin-top: 10px;
  text-align: center;
}

.coordinate-display .el-tag {
  padding: 8px 12px;
}

.coordinate-display .el-icon {
  margin-right: 4px;
}
</style>
