<template>
  <!-- H5打印预览弹窗 -->
  <el-dialog
    v-model="visible"
    title="样品标签打印"
    width="95%"
    :before-close="handleClose"
    destroy-on-close
    class="h5-print-dialog"
  >
    <div class="h5-print-container">
      <!-- 打印预览区域 -->
      <div class="print-preview" ref="printRef">
        <div class="label-container" v-for="(label, index) in labels" :key="index">
          <div class="label-content">
            <!-- 左侧信息 -->
            <div class="label-left">
              <div class="label-row">
                <span class="label-title">样品类别：</span>
                <span class="label-value">{{ label.sampleCategory }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">样品编号：</span>
                <span class="label-value">{{ label.sampleNumber }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">采样日期：</span>
                <span class="label-value">{{ label.samplingDate }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">采样点位：</span>
                <span class="label-value">{{ label.samplingPoint }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">检测项目：</span>
                <span class="label-value">{{ label.testItems }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">保存容器：</span>
                <span class="label-value">{{ label.container }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">保存方式：</span>
                <span class="label-value">{{ label.storageMethod }}</span>
              </div>
              <div class="label-row status-row">
                <span class="label-title">样品状态：</span>
                <div class="status-checkboxes">
                  <span class="status-item">
                    <span class="checkbox">☐</span> 待测
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 在测
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 测毕
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 留样
                  </span>
                </div>
              </div>
            </div>

            <!-- 右侧二维码 -->
            <div class="label-right">
              <div class="qrcode-container">
                <canvas :data-ref="`qrcode-${index}`" class="qrcode"></canvas>
              </div>
            </div>
          </div>

          <!-- 底部信息 -->
          <div class="label-footer">
            <div class="company-name">{{ label.companyName }}</div>
            <div class="page-number">{{ label.pageNumber }}</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="print-actions">
        <el-button @click="handleClose" size="large">取消</el-button>
        <el-button @click="showConfigGuide" size="large">打印设置</el-button>
        <el-button type="primary" @click="handlePrint" size="large">打印</el-button>
      </div>
    </div>

    <!-- 打印机配置指南 -->
    <PrinterConfigGuide
      v-model="configGuideVisible"
      @test-print="handlePrint"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import QRCode from 'qrcode'
import { parseTime } from '@/utils/ruoyi'
import { ElMessage } from 'element-plus'
import PrinterConfigGuide from '@/components/common/PrinterConfigGuide.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  bottleGroup: {
    type: Object,
    default: () => ({})
  },
  taskInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const printRef = ref(null)
const configGuideVisible = ref(false)

// 计算标签数据
const labels = computed(() => {
  if (!props.bottleGroup || !props.taskInfo) return []

  const bottleGroup = props.bottleGroup
  const taskInfo = props.taskInfo

  // 构建样品编号：任务编号（样品序号/样品总数量）
  const taskCode = taskInfo.taskCode || '************'
  // 从瓶组的排序序号中获取样品序号
  const sampleIndex = bottleGroup.sortOrder || 1
  // 从任务信息中获取总样品数量
  const totalSamples = taskInfo.totalSamples || bottleGroup.totalCount || 4
  const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`

  // 构建采样点位：点位名称 （周期{周期数}）
  const pointName = taskInfo.pointName || '厂界下风向3'
  const cycleNumber = taskInfo.cycleNumber || bottleGroup.cycleNumber || 1
  const samplingPoint = `${pointName} （周期${cycleNumber}）`

  // 生成单个标签（每个瓶组一个标签）
  const labelsData = [{
    sampleCategory: taskInfo.detectionCategory || '无组织废气',
    sampleNumber: sampleNumber,
    samplingDate: parseTime(bottleGroup.samplingDate || taskInfo.samplingDate || new Date(), '{y}-{m}-{d}') || '2024-12-31',
    samplingPoint: samplingPoint,
    testItems: bottleGroup.testItems || taskInfo.testItems || '臭气浓度',
    container: bottleGroup.containerInfo || bottleGroup.bottleType || '10L 气袋 10L',
    storageMethod: bottleGroup.storageMethod || '17-25°C常温保存 密封 避光 无',
    companyName: '浙江求实环境监测有限公司',
    pageNumber: '1/1'
  }]

  return labelsData
})

// 方法
const configurePrinter = async () => {
  try {
    // 设置打印机配置
    const printOptions = {
      // 纸张设置
      paperSize: {
        width: 75, // 75mm
        height: 60 // 60mm
      },
      // 边距设置
      margins: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0
      },
      // 打印方向
      orientation: 'portrait',
      // 缩放
      scale: 1,
      // 背景图形
      printBackground: true,
      // 页眉页脚
      headerFooter: false
    }

    // 如果支持Web Print API
    if ('print' in window && typeof window.print === 'function') {
      // 设置CSS打印样式
      const style = document.createElement('style')
      style.textContent = `
        @media print {
          @page {
            size: 75mm 60mm;
            margin: 0;
          }
          body {
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
        }
      `
      document.head.appendChild(style)

      // 清理函数
      setTimeout(() => {
        document.head.removeChild(style)
      }, 1000)
    }

    console.log('打印机配置已设置:', printOptions)
  } catch (error) {
    console.warn('设置打印机配置失败:', error)
  }
}

const generateQRCode = async (text, canvasRef) => {
  try {
    await QRCode.toCanvas(canvasRef, text, {
      width: 80,
      height: 80,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
  }
}

const generateAllQRCodes = async () => {
  await nextTick()

  for (let index = 0; index < labels.value.length; index++) {
    const label = labels.value[index]
    const canvasRef = document.querySelector(`[data-ref="qrcode-${index}"]`)
    if (canvasRef) {
      await generateQRCode(label.sampleNumber, canvasRef)
    }
  }
}

const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
}

const showConfigGuide = () => {
  configGuideVisible.value = true
}

const handlePrint = async () => {
  try {
    // 检查是否支持新的打印API
    if ('print' in window && window.print.length === 0) {
      // 使用现代打印API设置打印机配置
      await configurePrinter()
    }

    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    const printContent = printRef.value.innerHTML
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>样品标签打印</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          @media print {
            @page {
              size: 75mm 60mm;
              margin: 0;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
            body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
            * {
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
          }

          @media screen {
            body {
              background: #f0f0f0;
              padding: 10px;
            }
          }

          .label-container {
            width: 75mm;
            height: 60mm;
            border: 1px solid #000;
            page-break-after: always;
            display: flex;
            flex-direction: column;
            font-size: 10px;
            line-height: 1.2;
          }
          
          .label-content {
            display: flex;
            flex: 1;
            padding: 2mm;
          }
          
          .label-left {
            flex: 1;
            margin-right: 2mm;
          }
          
          .label-row {
            display: flex;
            margin-bottom: 1mm;
            align-items: flex-start;
          }
          
          .label-title {
            font-weight: bold;
            min-width: 16mm;
            font-size: 9px;
          }
          
          .label-value {
            flex: 1;
            word-break: break-all;
            font-size: 9px;
          }
          
          .status-checkboxes {
            display: flex;
            flex-wrap: wrap;
            gap: 2mm;
          }
          
          .status-item {
            font-size: 8px;
            white-space: nowrap;
          }
          
          .checkbox {
            margin-right: 1mm;
          }
          
          .label-right {
            width: 18mm;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .qrcode-container {
            width: 16mm;
            height: 16mm;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ccc;
          }

          .qrcode {
            max-width: 14mm;
            max-height: 14mm;
          }
          
          .label-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1mm 2mm;
            border-top: 1px solid #ccc;
            font-size: 8px;
          }
          
          .company-name {
            font-weight: bold;
          }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `)
    
    printWindow.document.close()
    
    // 等待内容加载完成后打印
    setTimeout(() => {
      // 在打印前显示配置提示
      printWindow.focus()

      // 添加打印前的事件监听
      printWindow.addEventListener('beforeprint', () => {
        console.log('准备打印，纸张尺寸: 75mm × 60mm')
      })

      printWindow.addEventListener('afterprint', () => {
        console.log('打印完成')
        printWindow.close()
      })

      printWindow.print()
    }, 800)

    ElMessage.success('打印任务已发送，请确保打印机纸张设置为75mm×60mm')
    handleClose()
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败')
  }
}

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    nextTick(() => {
      generateAllQRCodes()
    })
  }
})

watch(visible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.h5-print-dialog {
  --el-dialog-border-radius: 8px;
}

.h5-print-container {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.print-preview {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.label-container {
  width: 100%;
  max-width: 320px;
  margin: 0 auto 20px;
  border: 2px solid #409EFF;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.label-content {
  display: flex;
  padding: 12px;
  min-height: 200px;
}

.label-left {
  flex: 1;
  margin-right: 12px;
}

.label-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
  font-size: 12px;
}

.label-row:last-child {
  margin-bottom: 0;
}

.label-title {
  font-weight: 600;
  color: #333;
  min-width: 70px;
  margin-right: 8px;
}

.label-value {
  flex: 1;
  color: #666;
  word-break: break-all;
  line-height: 1.4;
}

.status-row {
  align-items: flex-start;
}

.status-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-item {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.checkbox {
  margin-right: 4px;
  font-weight: bold;
}

.label-right {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-container {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f9f9f9;
}

.qrcode {
  max-width: 60px;
  max-height: 60px;
}

.label-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  font-size: 11px;
}

.company-name {
  font-weight: 600;
  color: #333;
}

.page-number {
  color: #666;
}

.print-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 0 20px;
}

.print-actions .el-button {
  flex: 1;
  max-width: 120px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .label-container {
    max-width: 100%;
  }
  
  .label-content {
    padding: 10px;
    min-height: 180px;
  }
  
  .label-row {
    font-size: 11px;
    margin-bottom: 6px;
  }
  
  .label-title {
    min-width: 60px;
  }
  
  .label-right {
    width: 70px;
  }
  
  .qrcode-container {
    width: 60px;
    height: 60px;
  }
  
  .qrcode {
    max-width: 50px;
    max-height: 50px;
  }
}
</style>
