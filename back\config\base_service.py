from typing import TypeVar, Generic, Type, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from config.async_orm_client import AsyncORMClient
from exceptions.exception import ServiceException
from utils.log_util import logger

# 定义泛型类型变量
T = TypeVar('T', bound=DeclarativeBase)


class BaseService(Generic[T]):
    """
    基础服务类，提供通用的事务管理和异常处理

    使用示例:
    ```python
    class UserService(BaseService[User]):
        def __init__(self, db: AsyncSession):
            super().__init__(User, db)

        async def create_user(self, user_data: dict):
            try:
                # 创建用户
                user = await self.orm_client.create(user_data)
                # 提交事务
                await self.commit()
                return user
            except Exception as e:
                # 回滚事务
                await self.rollback()
                # 记录错误并重新抛出
                self.log_error("创建用户失败", e)
                raise ServiceException(message=f'创建用户失败: {str(e)}')
    ```
    """

    def __init__(self, model_class: Type[T], db: AsyncSession):
        """
        初始化基础服务类

        :param model_class: 模型类
        :param db: 数据库会话
        """
        self.model_class = model_class
        self.db = db
        self.orm_client = AsyncORMClient(model_class, db)

    async def commit(self):
        """
        提交事务
        """
        await self.orm_client.commit()

    async def rollback(self):
        """
        回滚事务
        """
        await self.orm_client.rollback()

    async def get_by_id(self, id: Any) -> Optional[T]:
        """
        根据ID获取实体

        :param id: 实体ID
        :return: 实体对象，如果不存在则返回None
        """
        return await self.orm_client.get_by_id(id)

    def log_error(self, message: str, exception: Exception):
        """
        记录错误日志

        :param message: 错误消息
        :param exception: 异常对象
        """
        logger.error(f"{message}: {str(exception)}")
