#!/usr/bin/env python3
"""
测试采样任务创建功能
"""
import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests

def test_create_sampling_task():
    """测试创建采样任务"""
    
    # 测试数据 - 使用可用的周期条目ID
    test_data = {
        "projectQuotationId": 21,
        "taskName": "测试采样任务",
        "taskDescription": "这是一个测试任务",
        "selectedCycleItemIds": [6, 7, 8],  # 使用状态为0的可用周期条目
        "plannedStartTime": "2025-01-08T09:00:00",
        "plannedEndTime": "2025-01-15T18:00:00",
        "responsibleUserId": 1,
        "remark": "测试备注"
    }
    
    # API端点
    url = "http://localhost:9099/sampling/task-creation/create-task"
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"  # 使用测试token跳过认证
    }
    
    try:
        print("🚀 开始测试采样任务创建...")
        print(f"📋 测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送POST请求
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 采样任务创建成功!")
                data = result.get("data", {})
                print(f"   任务ID: {data.get('taskId')}")
                print(f"   任务编号: {data.get('taskNumber')}")
                print(f"   任务名称: {data.get('taskName')}")
                print(f"   周期条目数量: {data.get('cycleItemCount')}")
                print(f"   消息: {data.get('message')}")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")


def test_get_quotation_cycle_items():
    """测试获取项目报价的检测周期条目"""
    
    quotation_id = 21
    url = f"http://localhost:9099/sampling/task-creation/quotation/{quotation_id}/cycle-items"
    
    headers = {
        "Authorization": "Bearer test_token"
    }
    
    try:
        print(f"🔍 测试获取项目报价 {quotation_id} 的检测周期条目...")
        
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 获取检测周期条目成功!")
                data = result.get("data", [])
                print(f"   共找到 {len(data)} 个检测项目")
                for i, item in enumerate(data[:3]):  # 只显示前3个
                    print(f"   项目 {i+1}: {item.get('parameter')} - {item.get('method')}")
            else:
                print(f"❌ 获取失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")


if __name__ == "__main__":
    print("🧪 开始采样任务创建功能测试")
    print("=" * 50)
    
    # 测试1: 获取检测周期条目
    test_get_quotation_cycle_items()
    print()
    
    # 测试2: 创建采样任务
    test_create_sampling_task()
    
    print("=" * 50)
    print("🏁 测试完成")
