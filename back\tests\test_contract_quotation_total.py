"""
测试合同报价单总金额计算功能
"""

import pytest
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from config.database import get_async_db
from module_contract.service.contract_service import ContractService
from module_quotation.service.project_quotation_fee_calculation_service import ProjectQuotationFeeCalculationService


class TestContractQuotationTotal:
    """合同报价单总金额计算测试"""

    @pytest.fixture
    async def db_session(self):
        """获取数据库会话"""
        async_db = get_async_db()
        async with async_db() as db:
            yield db

    async def test_calculate_quotation_total_amount(self, db_session: AsyncSession):
        """测试计算报价单总金额"""
        # 这里可以添加具体的测试逻辑
        # 由于需要真实的数据库数据，这里只是一个框架
        
        contract_service = ContractService(db_session)
        
        # 假设有一个测试合同ID
        test_contract_id = 1
        
        try:
            result = await contract_service.calculate_and_update_quotation_total_amount(test_contract_id)
            assert "quotation_total_amount" in result
            assert isinstance(result["quotation_total_amount"], (int, float))
            print(f"计算结果: {result}")
        except Exception as e:
            print(f"测试失败: {str(e)}")
            # 在测试环境中，可能没有相关数据，这是正常的


if __name__ == "__main__":
    # 运行测试
    async def run_test():
        test_instance = TestContractQuotationTotal()
        async_db = get_async_db()
        async with async_db() as db:
            await test_instance.test_calculate_quotation_total_amount(db)
    
    asyncio.run(run_test())
