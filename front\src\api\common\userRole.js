import request from '@/utils/request'
import { getUsersByRoleName, getUsersByRoleKey } from '@/api/system/role'
import { listUser } from '@/api/system/user'

/**
 * 获取客服选项列表
 * @param {string} roleName - 角色名称，默认为'客服'
 * @returns {Promise} 返回格式化的客服选项列表
 */
export function getCustomerSupportOptions(roleName = '客服') {
  return new Promise((resolve, reject) => {
    // 从角色中获取客服列表
    getUsersByRoleName(roleName).then(response => {
      const customerSupportOptions = response.data.map(user => ({
        value: user.userId,
        label: `${user.userName}(${user.nickName || '无昵称'})`
      }))
      resolve(customerSupportOptions)
    }).catch(error => {
      console.error('获取客服列表失败:', error)
      // 如果获取客服角色失败，则获取所有用户作为备选
      listUser({ pageSize: 100000 }).then(response => {
        const customerSupportOptions = response.data.rows.map(user => ({
          value: user.userId,
          label: `${user.userName}(${user.nickName || '无昵称'})`
        }))
        resolve(customerSupportOptions)
      }).catch(err => {
        console.error('获取用户列表失败:', err)
        reject(err)
      })
    })
  })
}

/**
 * 根据角色权限key获取客服选项列表
 * @param {string} roleKey - 角色权限key，默认为'customer-support'
 * @returns {Promise} 返回格式化的客服选项列表
 */
export function getCustomerSupportOptionsByKey(roleKey = 'customer-support') {
  return new Promise((resolve, reject) => {
    // 根据角色权限key获取客服列表
    getUsersByRoleKey(roleKey).then(response => {
      const customerSupportOptions = response.data.map(user => ({
        value: user.userId,
        label: `${user.userName}(${user.nickName || '无昵称'})`
      }))
      resolve(customerSupportOptions)
    }).catch(error => {
      console.error('根据权限key获取客服列表失败:', error)
      // 如果获取客服角色失败，则获取所有用户作为备选
      listUser({ pageSize: 100000 }).then(response => {
        const customerSupportOptions = response.data.rows.map(user => ({
          value: user.userId,
          label: `${user.userName}(${user.nickName || '无昵称'})`
        }))
        resolve(customerSupportOptions)
      }).catch(err => {
        console.error('获取用户列表失败:', err)
        reject(err)
      })
    })
  })
}

/**
 * 获取指定角色的用户列表（原始数据格式）
 * @param {string} roleName - 角色名称
 * @returns {Promise} 返回用户列表原始数据
 */
export function getUsersByRole(roleName) {
  return getUsersByRoleName(roleName)
}

/**
 * 获取所有用户列表
 * @param {Object} query - 查询参数
 * @returns {Promise} 返回用户列表
 */
export function getAllUsers(query = {}) {
  return listUser(query)
}