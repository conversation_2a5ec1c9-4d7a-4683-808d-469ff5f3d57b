-- 更新技术手册表的类目编码字段为JSON数组格式
-- 执行时间：2024-01-XX

-- 1. 备份现有数据
CREATE TABLE technical_manual_category_code_backup AS 
SELECT id, category_code 
FROM technical_manual 
WHERE category_code IS NOT NULL AND category_code != '';

-- 2. 添加新的JSON字段
ALTER TABLE technical_manual 
ADD COLUMN category_codes JSON COMMENT '类目编码列表（JSON数组格式）' AFTER category_code;

-- 3. 迁移现有类目编码数据到JSON格式
UPDATE technical_manual 
SET category_codes = JSON_ARRAY(category_code) 
WHERE category_code IS NOT NULL AND category_code != '';

-- 4. 验证数据迁移
SELECT 
    id,
    category_code as old_category_code,
    category_codes as new_category_codes,
    JSON_LENGTH(category_codes) as category_count
FROM technical_manual 
WHERE category_codes IS NOT NULL
LIMIT 10;

-- 注意：执行完成后，可以考虑删除 category_code 字段
-- ALTER TABLE technical_manual DROP COLUMN category_code;
