"""
合同商务信息服务
"""

from datetime import datetime
from typing import Dict
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.dept_do import SysDept
from module_contract.entity.do.contract_business_do import ContractBusinessDepartment, ContractBusinessTask
from module_contract.entity.vo.contract_business_vo import ContractBusinessInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_total_fee_do import ProjectQuotationTotalFee


class ContractBusinessService:
    """
    合同商务信息服务类
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def _get_dept_names_by_ids(self, dept_ids: list) -> Dict[int, str]:
        """根据部门ID获取部门名称"""
        if not dept_ids:
            return {}

        query = select(SysDept.dept_id, SysDept.dept_name).where(SysDept.dept_id.in_(dept_ids), SysDept.del_flag == "0")
        result = await self.db.execute(query)
        depts = result.fetchall()

        return {dept.dept_id: dept.dept_name for dept in depts}

    async def _validate_business_amounts(self, business_data: ContractBusinessInfoModel) -> None:
        """校验商务信息金额不能超过报价单总金额"""
        # 收集所有项目编号
        project_codes = set()
        for dept in business_data.departments:
            if dept.project_code:
                project_codes.add(dept.project_code)
        for task in business_data.tasks:
            if task.project_code:
                project_codes.add(task.project_code)

        # 校验每个项目编号的金额
        for project_code in project_codes:
            # 获取报价单总金额
            quotation_query = select(ProjectQuotation.id).where(ProjectQuotation.project_code == project_code)
            quotation_result = await self.db.execute(quotation_query)
            quotation = quotation_result.scalar_one_or_none()

            if not quotation:
                raise ValueError(f"项目编号 {project_code} 对应的报价单不存在")

            # 获取报价单总金额
            total_fee_query = select(ProjectQuotationTotalFee.final_amount).where(
                ProjectQuotationTotalFee.project_quotation_id == quotation
            )
            total_fee_result = await self.db.execute(total_fee_query)
            quotation_final_amount = total_fee_result.scalar_one_or_none()

            if not quotation_final_amount:
                raise ValueError(f"项目编号 {project_code} 的报价单总金额不存在")

            # 计算该项目的分摊金额和任务金额总和
            dept_total = sum(
                dept.allocation_amount for dept in business_data.departments if dept.project_code == project_code
            )
            task_total = sum(task.task_amount for task in business_data.tasks if task.project_code == project_code)

            business_total = dept_total + task_total

            if business_total > quotation_final_amount:
                raise ValueError(
                    f"项目编号 {project_code} 的分摊金额和任务金额总和 {business_total} "
                    f"不能超过报价单总金额 {quotation_final_amount}"
                )

    async def get_contract_business(self, contract_id: int) -> Dict:
        """
        获取合同商务信息
        """
        # 查询部门分摊数据
        dept_query = select(ContractBusinessDepartment).where(ContractBusinessDepartment.contract_id == contract_id)
        dept_result = await self.db.execute(dept_query)
        dept_businesses = dept_result.scalars().all()

        # 查询任务数据
        task_query = select(ContractBusinessTask).where(ContractBusinessTask.contract_id == contract_id)
        task_result = await self.db.execute(task_query)
        task_businesses = task_result.scalars().all()

        # 获取部门名称
        dept_ids = [business.dept_id for business in dept_businesses if business.dept_id]
        dept_names = await self._get_dept_names_by_ids(dept_ids)

        # 转换部门分摊数据
        dept_list = []
        for business in dept_businesses:
            business_dict = {
                "id": business.id,
                "contractId": business.contract_id,
                "projectCode": business.project_code,
                "deptId": business.dept_id,
                "deptName": dept_names.get(business.dept_id, f"部门{business.dept_id}") if business.dept_id else None,
                "allocationAmount": float(business.allocation_amount) if business.allocation_amount else 0,
            }
            dept_list.append(business_dict)

        # 转换任务数据
        task_list = []
        for business in task_businesses:
            business_dict = {
                "id": business.id,
                "contractId": business.contract_id,
                "projectCode": business.project_code,
                "taskCode": business.task_code,
                "taskAmount": float(business.task_amount) if business.task_amount else 0,
            }
            task_list.append(business_dict)

        return {"departments": dept_list, "tasks": task_list}

    async def save_contract_business(self, business_data: ContractBusinessInfoModel, current_user) -> Dict:
        """
        保存合同商务信息（先删除旧的，再添加新的）
        """
        try:
            current_time = datetime.now()

            # 从第一个部门或任务中获取contract_id
            contract_id = None
            if business_data.departments and len(business_data.departments) > 0:
                contract_id = business_data.departments[0].contract_id
            elif business_data.tasks and len(business_data.tasks) > 0:
                contract_id = business_data.tasks[0].contract_id

            if not contract_id:
                raise ValueError("合同ID不能为空，请确保部门分摊或任务拆解中至少有一条记录包含合同ID")

            # 校验金额不能超过报价单总金额
            await self._validate_business_amounts(business_data)

            # 删除旧的部门分摊数据
            await self.db.execute(
                delete(ContractBusinessDepartment).where(ContractBusinessDepartment.contract_id == contract_id)
            )

            # 删除旧的任务数据
            await self.db.execute(delete(ContractBusinessTask).where(ContractBusinessTask.contract_id == contract_id))

            # 添加新的部门分摊数据
            for dept_data in business_data.departments:
                new_dept = ContractBusinessDepartment(
                    contract_id=dept_data.contract_id,
                    project_code=dept_data.project_code,
                    dept_id=dept_data.dept_id,
                    allocation_amount=dept_data.allocation_amount,
                    create_by=current_user.user.user_name,
                    create_time=current_time,
                    update_by=current_user.user.user_name,
                    update_time=current_time,
                )
                self.db.add(new_dept)

            # 添加新的任务数据
            for task_data in business_data.tasks:
                new_task = ContractBusinessTask(
                    contract_id=task_data.contract_id,
                    project_code=task_data.project_code,
                    task_code=task_data.task_code,
                    task_amount=task_data.task_amount,
                    create_by=current_user.user.user_name,
                    create_time=current_time,
                    update_by=current_user.user.user_name,
                    update_time=current_time,
                )
                self.db.add(new_task)

            await self.db.commit()

            # 返回保存后的数据
            return await self.get_contract_business(contract_id)

        except Exception as e:
            await self.db.rollback()
            raise e
