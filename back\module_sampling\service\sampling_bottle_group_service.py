"""
采样瓶组服务
"""

import json
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, text

from module_sampling.dao.sampling_bottle_group_dao import SamplingBottleGroupDAO
from module_sampling.dao.sampling_bottle_group_sample_dao import SamplingBottleGroupSampleDAO
from module_bottle_maintenance.service.bottle_maintenance_service import BottleMaintenanceService

from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_sample_do import SamplingBottleGroupSample
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance

from module_sampling.entity.vo.sampling_bottle_group_vo import (
    SamplingBottleGroupDetailModel,
    BottleGroupGenerateResponseModel,
    SampleRecordWithDetectionModel
)
from exceptions.exception import ServiceException
from utils.log_util import logger


class SamplingBottleGroupService:
    """采样瓶组服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.bottle_group_dao = SamplingBottleGroupDAO(db)
        self.bottle_group_sample_dao = SamplingBottleGroupSampleDAO(db)
        self.bottle_maintenance_service = BottleMaintenanceService(db)
    
    async def generate_bottle_groups_for_task(self, task_id: int, create_by: int) -> BottleGroupGenerateResponseModel:
        """为采样任务生成瓶组"""
        try:
            logger.info(f"开始为任务 {task_id} 生成瓶组")

            # 检查是否已经生成过瓶组
            existing_groups = await self.bottle_group_dao.get_bottle_groups_by_task_id(task_id)
            if existing_groups:
                logger.info(f"任务 {task_id} 已存在 {len(existing_groups)} 个瓶组，检查样品关联")

                # 即使瓶组已存在，也要确保所有样品记录都有正确的瓶组关联
                await self._ensure_sample_bottle_relations(task_id)

                bottle_groups_detail = await self.bottle_group_dao.get_bottle_groups_with_details_by_task_id(task_id)
                return BottleGroupGenerateResponseModel(
                    total_groups=len(existing_groups),
                    default_groups=len([g for g in existing_groups if g.bottle_maintenance_id is None]),
                    matched_groups=len([g for g in existing_groups if g.bottle_maintenance_id is not None]),
                    bottle_groups=[SamplingBottleGroupDetailModel(**group) for group in bottle_groups_detail]
                )
            
            # 获取样品记录及检测方法
            sample_records_with_detection = await self._get_sample_records_with_detection(task_id)
            
            if not sample_records_with_detection:
                logger.warning(f"任务 {task_id} 没有找到样品记录")
                return BottleGroupGenerateResponseModel(
                    total_groups=0,
                    default_groups=0,
                    matched_groups=0,
                    bottle_groups=[]
                )
            
            logger.info(f"找到 {len(sample_records_with_detection)} 个样品记录")
            
            # 按瓶组分组样品
            bottle_group_map = await self._group_samples_by_bottle(sample_records_with_detection)
            
            # 获取任务编号用于生成瓶组编号
            task_code = await self._get_task_code(task_id)
            
            # 创建瓶组记录
            bottle_groups = []
            bottle_sequence = 1
            default_groups_count = 0
            matched_groups_count = 0
            
            for group_key, group_data in bottle_group_map.items():
                # 生成瓶组编号
                bottle_code = f"{task_code}-B{bottle_sequence:03d}"
                
                # 创建瓶组记录
                bottle_group = SamplingBottleGroup(
                    sampling_task_id=task_id,
                    bottle_group_code=bottle_code,
                    bottle_maintenance_id=group_data['bottle_maintenance_id'],
                    detection_method=group_data['detection_method'],
                    sample_count=len(group_data['samples']),
                    create_by=create_by
                )
                
                created_group = await self.bottle_group_dao.create_bottle_group(bottle_group)
                
                # 创建样品关联记录（避免重复插入）
                sample_relations = []
                for sample in group_data['samples']:
                    # 检查是否已存在关联记录
                    existing_relation = await self.bottle_group_sample_dao.get_bottle_group_sample_by_ids(
                        created_group.id, sample['sample_id']
                    )

                    if not existing_relation:
                        sample_relation = SamplingBottleGroupSample(
                            bottle_group_id=created_group.id,
                            sample_record_id=sample['sample_id']
                        )
                        sample_relations.append(sample_relation)

                if sample_relations:
                    await self.bottle_group_sample_dao.batch_create_bottle_group_samples(sample_relations)
                
                # 构建返回数据
                bottle_group_detail = SamplingBottleGroupDetailModel(
                    id=created_group.id,
                    sampling_task_id=created_group.sampling_task_id,
                    bottle_group_code=created_group.bottle_group_code,
                    bottle_maintenance_id=created_group.bottle_maintenance_id,
                    detection_method=created_group.detection_method,
                    sample_count=created_group.sample_count,
                    status=created_group.status,
                    bottle_type=group_data['bottle_maintenance'].bottle_type if group_data['bottle_maintenance'] else '默认瓶组',
                    bottle_volume=group_data['bottle_maintenance'].bottle_volume if group_data['bottle_maintenance'] else '-',
                    storage_styles=group_data['bottle_maintenance'].storage_styles if group_data['bottle_maintenance'] else [],
                    fix_styles=group_data['bottle_maintenance'].fix_styles if group_data['bottle_maintenance'] else [],
                    sample_age=group_data['bottle_maintenance'].sample_age if group_data['bottle_maintenance'] else None,
                    sample_age_unit=group_data['bottle_maintenance'].sample_age_unit if group_data['bottle_maintenance'] else None,
                    task_code=task_code,
                    samples=[{'id': s['sample_id'], 'sample_number': s['sample_number']} for s in group_data['samples']],
                    create_time=created_group.create_time,
                    update_time=created_group.update_time
                )
                
                bottle_groups.append(bottle_group_detail)
                
                # 统计计数
                if created_group.bottle_maintenance_id is None:
                    default_groups_count += 1
                else:
                    matched_groups_count += 1
                
                bottle_sequence += 1
            
            await self.db.commit()
            
            logger.info(f"任务 {task_id} 瓶组生成完成，共生成 {len(bottle_groups)} 个瓶组（默认瓶组：{default_groups_count}，匹配瓶组：{matched_groups_count}）")
            
            return BottleGroupGenerateResponseModel(
                total_groups=len(bottle_groups),
                default_groups=default_groups_count,
                matched_groups=matched_groups_count,
                bottle_groups=bottle_groups
            )
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"为任务 {task_id} 生成瓶组失败: {str(e)}")
            raise ServiceException(message=f"生成瓶组失败: {str(e)}")
    
    async def get_bottle_groups_by_task_id(self, task_id: int) -> List[SamplingBottleGroupDetailModel]:
        """获取任务的瓶组列表"""
        try:
            bottle_groups_detail = await self.bottle_group_dao.get_bottle_groups_with_details_by_task_id(task_id)
            
            # 为每个瓶组获取关联的样品信息
            result = []
            for group_data in bottle_groups_detail:
                samples = await self.bottle_group_sample_dao.get_samples_by_bottle_group_id(group_data['id'])
                group_data['samples'] = [{'id': s.id, 'sample_number': s.sample_number} for s in samples]
                result.append(SamplingBottleGroupDetailModel(**group_data))
            
            return result
            
        except Exception as e:
            logger.error(f"获取任务 {task_id} 的瓶组列表失败: {str(e)}")
            raise ServiceException(message=f"获取瓶组列表失败: {str(e)}")
    
    async def get_bottle_group_detail(self, bottle_group_id: int) -> Optional[SamplingBottleGroupDetailModel]:
        """获取瓶组详情"""
        try:
            bottle_group = await self.bottle_group_dao.get_bottle_group_by_id(bottle_group_id)
            if not bottle_group:
                return None
            
            # 获取瓶组详情信息
            bottle_groups_detail = await self.bottle_group_dao.get_bottle_groups_with_details_by_task_id(bottle_group.sampling_task_id)
            group_detail = next((g for g in bottle_groups_detail if g['id'] == bottle_group_id), None)
            
            if not group_detail:
                return None
            
            # 获取关联的样品信息
            samples = await self.bottle_group_sample_dao.get_samples_by_bottle_group_id(bottle_group_id)
            group_detail['samples'] = [{'id': s.id, 'sample_number': s.sample_number} for s in samples]
            
            return SamplingBottleGroupDetailModel(**group_detail)
            
        except Exception as e:
            logger.error(f"获取瓶组 {bottle_group_id} 详情失败: {str(e)}")
            raise ServiceException(message=f"获取瓶组详情失败: {str(e)}")
    
    async def update_bottle_group_status(self, bottle_group_id: int, status: int, update_by: int) -> bool:
        """更新瓶组状态"""
        try:
            result = await self.bottle_group_dao.update_bottle_group_status(bottle_group_id, status, update_by)
            if result:
                await self.db.commit()
            return result
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新瓶组 {bottle_group_id} 状态失败: {str(e)}")
            raise ServiceException(message=f"更新瓶组状态失败: {str(e)}")
    
    async def delete_bottle_group(self, bottle_group_id: int) -> bool:
        """删除瓶组"""
        try:
            # 先删除样品关联记录
            await self.bottle_group_sample_dao.delete_bottle_group_samples_by_bottle_group_id(bottle_group_id)
            
            # 删除瓶组记录
            result = await self.bottle_group_dao.delete_bottle_group(bottle_group_id)
            
            if result:
                await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除瓶组 {bottle_group_id} 失败: {str(e)}")
            raise ServiceException(message=f"删除瓶组失败: {str(e)}")

    async def _get_sample_records_with_detection(self, task_id: int) -> List[SampleRecordWithDetectionModel]:
        """获取任务下样品记录及其检测方法信息（按周期条目拆分）"""
        try:
            # 使用原生SQL查询，按周期条目拆分样品记录
            sql = """
            SELECT
                sr.id as sample_id,
                sr.sample_number,
                sr.sample_type,
                sr.sample_source,
                sr.point_name,
                sr.cycle_number,
                sr.cycle_type,
                pqi.method as detection_method,
                pqi.category as detection_category,
                pqi.parameter as detection_parameter,
                dci.id as cycle_item_id
            FROM sample_record sr
            JOIN sampling_task_group stg ON sr.sampling_task_group_id = stg.id
            JOIN sampling_task_cycle_item stci ON stci.sampling_task_id = stg.sampling_task_id
            JOIN detection_cycle_item dci ON stci.detection_cycle_item_id = dci.id
                AND dci.cycle_number = sr.cycle_number
            JOIN project_quotation_item pqi ON dci.project_quotation_item_id = pqi.id
            WHERE stg.sampling_task_id = :task_id
            ORDER BY sr.id, dci.id
            """

            result = await self.db.execute(text(sql), {"task_id": task_id})
            records = result.fetchall()

            if not records:
                logger.warning(f"任务 {task_id} 没有找到样品记录和周期条目关联")
                return []

            # 转换为VO模型（每个样品-周期条目组合生成一条记录）
            sample_models = []
            for record in records:
                sample_model = SampleRecordWithDetectionModel(
                    sample_id=record.sample_id,
                    sample_number=record.sample_number,
                    sample_type=record.sample_type or "未知类型",
                    sample_source=record.sample_source or "未知来源",
                    point_name=record.point_name or "未知点位",
                    detection_method=record.detection_method or "未知方法",
                    detection_category=record.detection_category or "未知类别",
                    detection_parameter=record.detection_parameter or "未知参数"
                )
                sample_models.append(sample_model)

            logger.info(f"任务 {task_id} 获取到 {len(sample_models)} 个样品-检测项组合记录")
            return sample_models

        except Exception as e:
            import traceback
            error_detail = f"获取任务 {task_id} 的样品记录及检测信息失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_detail)
            raise ServiceException(message=f"获取样品记录失败: {str(e)}")

    async def get_bottle_groups_by_sample(self, sample_id: int) -> List[SamplingBottleGroupDetailModel]:
        """获取样品记录关联的瓶组列表"""
        try:
            # 通过样品关联表查找瓶组
            sql = """
            SELECT
                bg.id,
                bg.sampling_task_id,
                bg.bottle_group_code,
                bg.bottle_maintenance_id,
                bg.detection_method,
                bg.sample_count,
                bg.status,
                bg.create_time,
                bg.update_time,
                bm.bottle_type,
                bm.bottle_volume,
                bm.storage_styles,
                bm.fix_styles,
                bm.sample_age,
                bm.sample_age_unit,
                st.task_code
            FROM sampling_bottle_group bg
            JOIN sampling_bottle_group_sample bgs ON bg.id = bgs.bottle_group_id
            LEFT JOIN bottle_maintenance bm ON bg.bottle_maintenance_id = bm.id
            LEFT JOIN sampling_task st ON bg.sampling_task_id = st.id
            WHERE bgs.sample_record_id = :sample_id
            ORDER BY bg.bottle_group_code
            """

            result = await self.db.execute(text(sql), {"sample_id": sample_id})
            records = result.fetchall()

            bottle_groups = []
            for record in records:
                # 解析JSON字符串字段
                storage_styles = []
                fix_styles = []

                if record.storage_styles:
                    try:
                        import json
                        if isinstance(record.storage_styles, str):
                            storage_styles = json.loads(record.storage_styles)
                        elif isinstance(record.storage_styles, list):
                            storage_styles = record.storage_styles
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"解析storage_styles失败: {record.storage_styles}, 错误: {e}")
                        storage_styles = []

                if record.fix_styles:
                    try:
                        import json
                        if isinstance(record.fix_styles, str):
                            fix_styles = json.loads(record.fix_styles)
                        elif isinstance(record.fix_styles, list):
                            fix_styles = record.fix_styles
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"解析fix_styles失败: {record.fix_styles}, 错误: {e}")
                        fix_styles = []

                bottle_group = SamplingBottleGroupDetailModel(
                    id=record.id,
                    sampling_task_id=record.sampling_task_id,
                    bottle_group_code=record.bottle_group_code,
                    bottle_maintenance_id=record.bottle_maintenance_id,
                    detection_method=record.detection_method or "",
                    sample_count=record.sample_count,
                    status=record.status,
                    bottle_type=record.bottle_type or "默认瓶组",
                    bottle_volume=record.bottle_volume or "-",
                    storage_styles=storage_styles,
                    fix_styles=fix_styles,
                    sample_age=record.sample_age,
                    sample_age_unit=record.sample_age_unit,
                    task_code=record.task_code or "",
                    samples=[],  # 这里不需要返回样品列表，避免循环引用
                    create_time=record.create_time,
                    update_time=record.update_time
                )
                bottle_groups.append(bottle_group)

            logger.info(f"样品 {sample_id} 关联了 {len(bottle_groups)} 个瓶组")
            return bottle_groups

        except Exception as e:
            import traceback
            error_detail = f"获取样品 {sample_id} 的瓶组列表失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_detail)
            raise ServiceException(message=f"获取瓶组列表失败: {str(e)}")

    async def _ensure_sample_bottle_relations(self, task_id: int):
        """确保所有样品记录都有正确的瓶组关联"""
        try:
            logger.info(f"检查任务 {task_id} 的样品瓶组关联")

            # 获取任务下所有样品记录
            sample_sql = text("""
                SELECT sr.id, sr.detection_method
                FROM sample_record sr
                JOIN sampling_task_group stg ON sr.sampling_task_group_id = stg.id
                WHERE stg.sampling_task_id = :task_id
            """)
            sample_result = await self.db.execute(sample_sql, {"task_id": task_id})
            sample_records = sample_result.fetchall()

            # 获取任务的所有瓶组
            bottle_groups = await self.bottle_group_dao.get_bottle_groups_by_task_id(task_id)

            # 创建检测方法到瓶组的映射
            method_to_bottle = {}
            for bottle_group in bottle_groups:
                if bottle_group.detection_method:
                    method_to_bottle[bottle_group.detection_method.strip()] = bottle_group.id

            logger.info(f"任务 {task_id} 有 {len(sample_records)} 个样品记录，{len(bottle_groups)} 个瓶组")

            # 为每个样品记录创建瓶组关联
            total_created = 0
            for sample_record in sample_records:
                sample_id, detection_methods_str = sample_record

                if not detection_methods_str:
                    logger.warning(f"样品 {sample_id} 没有检测方法数据")
                    continue

                # 检查样品是否已有瓶组关联
                existing_count_sql = text("SELECT COUNT(*) FROM sampling_bottle_group_sample WHERE sample_record_id = :sample_id")
                existing_count_result = await self.db.execute(existing_count_sql, {"sample_id": sample_id})
                existing_count = existing_count_result.scalar()

                if existing_count > 0:
                    logger.debug(f"样品 {sample_id} 已有 {existing_count} 个瓶组关联")
                    continue

                # 解析检测方法字符串
                detection_methods = [method.strip() for method in detection_methods_str.split(',') if method.strip()]
                logger.debug(f"样品 {sample_id} 有 {len(detection_methods)} 个检测方法")

                sample_created = 0
                for method_name in detection_methods:
                    if method_name in method_to_bottle:
                        bottle_id = method_to_bottle[method_name]

                        # 检查关联是否已存在
                        existing_relation = await self.bottle_group_sample_dao.get_bottle_group_sample_by_ids(
                            bottle_id, sample_id
                        )

                        if not existing_relation:
                            # 创建关联记录
                            sample_relation = SamplingBottleGroupSample(
                                bottle_group_id=bottle_id,
                                sample_record_id=sample_id
                            )
                            await self.bottle_group_sample_dao.create_bottle_group_sample(sample_relation)
                            total_created += 1
                            sample_created += 1

                if sample_created > 0:
                    logger.info(f"为样品 {sample_id} 创建了 {sample_created} 个瓶组关联")
                else:
                    logger.warning(f"样品 {sample_id} 的检测方法与瓶组不匹配")

            if total_created > 0:
                await self.db.commit()
                logger.info(f"为任务 {task_id} 创建了 {total_created} 个新的样品瓶组关联")
            else:
                logger.info(f"任务 {task_id} 的样品瓶组关联已完整")

        except Exception as e:
            logger.error(f"确保样品瓶组关联失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            await self.db.rollback()

    async def _group_samples_by_bottle(self, sample_records: List[SampleRecordWithDetectionModel]) -> Dict[str, Dict[str, Any]]:
        """按瓶组分组样品（按检测方法分组，一个样品可能拆分到多个瓶组）"""
        bottle_group_map = {}

        for sample_data in sample_records:
            # 根据检测类别、检测参数和检测方法查找匹配的瓶组
            bottle_maintenance = await self.bottle_maintenance_service.get_bottle_by_detection_triple(
                sample_data.detection_category or "",
                sample_data.detection_parameter or "",
                sample_data.detection_method
            )

            if bottle_maintenance:
                # 相同瓶组归为一组
                group_key = f"bottle_{bottle_maintenance.id}_{sample_data.detection_category}_{sample_data.detection_parameter}_{sample_data.detection_method}"
                bottle_maintenance_id = bottle_maintenance.id
            else:
                # 默认瓶组：每个样品-检测三元组组合单独一组
                group_key = f"default_{sample_data.sample_id}_{sample_data.detection_category}_{sample_data.detection_parameter}_{sample_data.detection_method}"
                bottle_maintenance_id = None
                bottle_maintenance = None

            if group_key not in bottle_group_map:
                bottle_group_map[group_key] = {
                    'bottle_maintenance_id': bottle_maintenance_id,
                    'bottle_maintenance': bottle_maintenance,
                    'detection_method': sample_data.detection_method,
                    'detection_category': sample_data.detection_category,
                    'detection_parameter': sample_data.detection_parameter,
                    'samples': []
                }

            # 检查是否已经添加了这个样品（避免重复）
            existing_sample = next((s for s in bottle_group_map[group_key]['samples']
                                  if s['sample_id'] == sample_data.sample_id), None)

            if not existing_sample:
                bottle_group_map[group_key]['samples'].append({
                    'sample_id': sample_data.sample_id,
                    'sample_number': sample_data.sample_number,
                    'sample_type': sample_data.sample_type,
                    'sample_source': sample_data.sample_source,
                    'point_name': sample_data.point_name
                })

        return bottle_group_map

    async def _get_task_code(self, task_id: int) -> str:
        """获取任务编号"""
        stmt = select(SamplingTask.task_code).where(SamplingTask.id == task_id)
        result = await self.db.execute(stmt)
        task_code = result.scalar_one_or_none()

        if not task_code:
            raise ServiceException(message=f"任务 {task_id} 不存在")

        return task_code
