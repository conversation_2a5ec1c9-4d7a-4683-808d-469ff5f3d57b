"""
项目报价特殊耗材费用明细数据模型
"""

from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationSpecialConsumableFee(Base):
    """
    项目报价特殊耗材费用明细表
    """

    __tablename__ = "project_quotation_special_consumable_fee"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey("project_quotation.id"), nullable=False, comment="项目报价ID")
    qualification_code = Column(String(50), nullable=True, unique=True, comment="资质唯一编号")
    parameter = Column(String(200), nullable=False, comment="参数")
    method = Column(String(200), nullable=False, comment="方法")
    unit_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment="特殊耗材单价")
    quantity = Column(Integer, nullable=False, default=1, comment="数量")
    total_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment="总价")
    remark = Column(Text, nullable=True, comment="备注")

    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")

    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="special_consumables_fee")
