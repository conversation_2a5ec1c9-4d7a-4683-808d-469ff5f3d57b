import request from '@/utils/request'

// 查询周报月报列表
export function listReport(query) {
  return request({
    url: '/report/list',
    method: 'get',
    params: query
  })
}

// 查询周报月报分页列表
export function pageReport(query) {
  return request({
    url: '/report/page',
    method: 'get',
    params: query
  })
}

// 查询周报月报详细
export function getReport(reportId) {
  return request({
    url: '/report/' + reportId,
    method: 'get'
  })
}

// 新增周报月报
export function addReport(data) {
  return request({
    url: '/report',
    method: 'post',
    data: data
  })
}

// 修改周报月报
export function updateReport(data) {
  return request({
    url: '/report',
    method: 'put',
    data: data
  })
}

// 删除周报月报
export function delReport(reportIds) {
  return request({
    url: '/report',
    method: 'delete',
    data: reportIds
  })
}
