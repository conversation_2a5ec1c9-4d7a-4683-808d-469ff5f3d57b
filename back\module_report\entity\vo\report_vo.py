from datetime import datetime
from typing import Optional, List, Literal
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query
from pydantic_validation_decorator import NotBlank, Size
from module_admin.entity.vo.user_vo import UserModel


class ReportModel(BaseModel):
    """
    周报月报模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    report_id: Optional[int] = Field(default=None, description='报告ID')
    report_type: Optional[Literal['weekly', 'monthly']] = Field(default=None, description='报告类型（weekly/monthly）')
    report_date: Optional[datetime] = Field(default=None, description='报告日期（周/月的第一天）')
    reporter_id: Optional[int] = Field(default=None, description='报告人ID')
    summary: Optional[str] = Field(default=None, description='本周/本月总结')
    plan: Optional[str] = Field(default=None, description='下周/下月计划')
    problems: Optional[str] = Field(default=None, description='本周/本月存在的问题')
    support_needed: Optional[str] = Field(default=None, description='需要的支持')
    is_saturated: Optional[Literal['0', '1']] = Field(default=None, description='下周工作是否饱和（0不饱和 1饱和）仅周报')
    status: Optional[Literal['0', '1']] = Field(default='0', description='状态（0正常 1停用）')
    del_flag: Optional[Literal['0', '2']] = Field(default='0', description='删除标志（0存在 2删除）')
    create_by: Optional[int] = Field(default=None, description='创建者ID')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[int] = Field(default=None, description='更新者ID')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')
    reporter: Optional[UserModel] = Field(default=None, description='报告人信息')
    creator: Optional[UserModel] = Field(default=None, description='创建者信息')
    updater: Optional[UserModel] = Field(default=None, description='更新者信息')

    @NotBlank(field_name='report_type', message='报告类型不能为空')
    def get_report_type(self):
        return self.report_type

    @NotBlank(field_name='report_date', message='报告日期不能为空')
    def get_report_date(self):
        return self.report_date

    @NotBlank(field_name='summary', message='总结不能为空')
    @Size(field_name='summary', min_length=1, max_length=2000, message='总结长度不能超过2000个字符')
    def get_summary(self):
        return self.summary

    @NotBlank(field_name='plan', message='计划不能为空')
    @Size(field_name='plan', min_length=1, max_length=2000, message='计划长度不能超过2000个字符')
    def get_plan(self):
        return self.plan


@as_query
class ReportQueryModel(BaseModel):
    """
    周报月报查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    report_type: Optional[Literal['weekly', 'monthly']] = Field(default=None, description='报告类型')
    reporter_id: Optional[int] = Field(default=None, description='报告人ID')
    report_date_start: Optional[datetime] = Field(default=None, description='报告日期开始')
    report_date_end: Optional[datetime] = Field(default=None, description='报告日期结束')
    is_saturated: Optional[Literal['0', '1']] = Field(default=None, description='是否饱和')


@as_query
class ReportPageQueryModel(ReportQueryModel):
    """
    周报月报分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class AddReportModel(ReportModel):
    """
    新增周报月报模型
    """
    pass


class EditReportModel(ReportModel):
    """
    编辑周报月报模型
    """
    pass
