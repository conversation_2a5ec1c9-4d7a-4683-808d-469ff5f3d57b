import request from '@/utils/request'

// 获取项目报价审批状态
export function getApprovalStatus(projectQuotationId) {
  return request({
    url: `/quotation/project-quotation-approval/status/${projectQuotationId}`,
    method: 'get'
  })
}

// 提交审批
export function submitForApproval(projectQuotationId) {
  return request({
    url: `/quotation/project-quotation-approval/submit/${projectQuotationId}`,
    method: 'post'
  })
}

// 执行审批操作
export function performApproval(projectQuotationId, data) {
  return request({
    url: `/quotation/project-quotation-approval/approve/${projectQuotationId}`,
    method: 'post',
    data: data
  })
}

// 获取当前用户待审批的项目列表（分页）
export function getPendingApprovals(query) {
  return request({
    url: '/quotation/project-quotation-approval/pending',
    method: 'get',
    params: query
  })
}

// 初始化审批记录
export function initApprovalRecords(projectQuotationId, businessType) {
  return request({
    url: `/quotation/project-quotation-approval/init/${projectQuotationId}`,
    method: 'post',
    params: {
      business_type: businessType
    }
  })
}