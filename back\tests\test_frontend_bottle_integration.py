#!/usr/bin/env python3
"""
测试前端瓶组集成功能的完整流程
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_frontend_bottle_integration():
    """测试前端瓶组集成功能"""
    
    print("=" * 80)
    print("测试前端瓶组集成功能的完整流程")
    print("=" * 80)
    
    # 1. 获取任务13的分组信息
    print("1. 获取任务13的分组信息...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/task/13",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('data', [])
            print(f"✓ 任务13有 {len(groups)} 个分组")
            
            if not groups:
                print("✗ 没有找到分组")
                return
                
            # 找分组19（有样品记录的分组）
            test_group = next((g for g in groups if g['id'] == 19), groups[0])
            group_id = test_group['id']
            print(f"  - 测试分组: ID={group_id}, 点位={test_group.get('pointName', 'N/A')}")
            
        else:
            print(f"✗ 获取分组信息失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取分组信息异常: {e}")
        return
    
    # 2. 获取分组的样品记录
    print(f"\n2. 获取分组 {group_id} 的样品记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/{group_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            sample_records = data.get('data', [])
            print(f"✓ 分组 {group_id} 有 {len(sample_records)} 个样品记录")
            
            if not sample_records:
                print("✗ 没有找到样品记录")
                return
                
            # 显示样品记录信息
            print("  - 样品记录列表:")
            for i, record in enumerate(sample_records, 1):
                print(f"    样品{i}: ID={record.get('id')}, 编号={record.get('sampleNumber')}")
                print(f"           检测方法: {record.get('detectionMethod', 'N/A')[:50]}...")
                print(f"           状态: {record.get('status')}")
                
        else:
            print(f"✗ 获取样品记录失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取样品记录异常: {e}")
        return
    
    # 3. 为每个样品记录获取关联的瓶组信息
    print(f"\n3. 为每个样品记录获取关联的瓶组信息...")
    sample_bottle_map = {}
    
    for sample in sample_records:
        sample_id = sample['id']
        sample_number = sample['sampleNumber']
        
        try:
            response = requests.get(
                f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                data = response.json()
                bottle_groups = data.get('data', [])
                sample_bottle_map[sample_id] = bottle_groups
                print(f"  ✓ 样品 {sample_number} 关联了 {len(bottle_groups)} 个瓶组")
                
                # 显示瓶组信息
                for bottle in bottle_groups[:2]:  # 只显示前2个
                    bottle_type = "匹配瓶组" if bottle.get('bottleMaintenanceId') else "默认瓶组"
                    print(f"    - {bottle.get('bottleGroupCode')} ({bottle_type})")
                    
                if len(bottle_groups) > 2:
                    print(f"    - ... 还有 {len(bottle_groups) - 2} 个瓶组")
                    
            else:
                print(f"  ✗ 样品 {sample_number} 获取瓶组失败，状态码: {response.status_code}")
                sample_bottle_map[sample_id] = []
                
        except Exception as e:
            print(f"  ✗ 样品 {sample_number} 获取瓶组异常: {e}")
            sample_bottle_map[sample_id] = []
    
    # 4. 统计信息
    print(f"\n4. 统计信息...")
    total_samples = len(sample_records)
    total_bottles = sum(len(bottles) for bottles in sample_bottle_map.values())
    samples_with_bottles = sum(1 for bottles in sample_bottle_map.values() if len(bottles) > 0)
    
    print(f"  - 样品记录总数: {total_samples}")
    print(f"  - 瓶组记录总数: {total_bottles}")
    print(f"  - 有瓶组的样品数: {samples_with_bottles}")
    print(f"  - 平均每个样品的瓶组数: {total_bottles / total_samples if total_samples > 0 else 0:.1f}")
    
    # 5. 验证前端展示逻辑
    print(f"\n5. 验证前端展示逻辑...")
    print("  前端界面应该显示:")
    print("  - 样品记录表格，每行有展开按钮")
    print("  - 点击展开按钮时，显示该样品关联的瓶组信息")
    print("  - 瓶组信息包括：瓶组编号、瓶组类型、检测方法、状态等")
    print("  - 如果样品没有关联瓶组，显示'暂无关联的瓶组信息'")
    
    # 6. 模拟前端数据结构
    print(f"\n6. 模拟前端数据结构...")
    frontend_data = []
    for sample in sample_records:
        sample_id = sample['id']
        sample_with_bottles = {
            **sample,
            'bottleGroups': sample_bottle_map.get(sample_id, [])
        }
        frontend_data.append(sample_with_bottles)
    
    print("  ✓ 前端数据结构构建完成")
    print(f"  - 数据包含 {len(frontend_data)} 个样品记录")
    print(f"  - 每个样品记录都包含 bottleGroups 字段")
    
    # 7. 测试结果
    print(f"\n" + "=" * 80)
    if total_samples > 0 and total_bottles > 0:
        print("✅ 前端瓶组集成功能测试成功！")
        print("   - 样品记录获取正常")
        print("   - 瓶组关联查询正常")
        print("   - 数据结构符合前端要求")
        print("   - 可以在前端界面中正常展示瓶组信息")
    else:
        print("❌ 前端瓶组集成功能测试失败！")
        print("   - 请检查样品记录和瓶组数据")
    print("=" * 80)
    
    # 8. 前端使用说明
    print(f"\n📋 前端使用说明:")
    print("1. 登录系统，进入采样执行页面")
    print("2. 点击任务的'样品管理'按钮")
    print("3. 在样品记录表格中，点击行前的展开按钮")
    print("4. 查看该样品关联的瓶组信息")
    print("5. 可以直接在瓶组表格中进行采集、送检等操作")

if __name__ == "__main__":
    asyncio.run(test_frontend_bottle_integration())
