<template>
  <div class="h5-task-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="header-left" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="header-title">任务详情</div>
      <div class="header-right"></div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
      <div class="error-actions">
        <el-button type="primary" size="small" @click="retryLoad">重试</el-button>
      </div>
    </div>

    <!-- 任务详情内容 -->
    <div v-else-if="taskData.id" class="task-content">
      <!-- 任务基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>任务编号：</label>
            <span>{{ taskData.taskCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>任务名称：</label>
            <span>{{ taskData.taskName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>项目名称：</label>
            <span>{{ taskData.projectName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>客户名称：</label>
            <span>{{ taskData.customerName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>任务状态：</label>
            <span class="status" :class="getStatusClass(taskData.status)">
              {{ getStatusLabel(taskData.status) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 分组信息 -->
      <div class="section">
        <h3 class="section-title">分组信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>周期序号：</label>
            <span>{{ taskData.cycleNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <label>周期类型：</label>
            <span>{{ taskData.cycleType || '-' }}</span>
          </div>
          <div class="info-item">
            <label>检测类别：</label>
            <span>{{ taskData.detectionCategory || '-' }}</span>
          </div>
          <div class="info-item">
            <label>点位名称：</label>
            <span>{{ taskData.pointName || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 执行信息 -->
      <div class="section">
        <h3 class="section-title">执行信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>执行人：</label>
            <span>
              <el-tag 
                v-for="name in taskData.assignedUserNames" 
                :key="name" 
                size="small" 
                style="margin-right: 4px;"
              >
                {{ name }}
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <label>计划开始：</label>
            <span>{{ formatDate(taskData.plannedStartDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>计划结束：</label>
            <span>{{ formatDate(taskData.plannedEndDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>实际开始：</label>
            <span>{{ formatDateTime(taskData.actualStartDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>实际结束：</label>
            <span>{{ formatDateTime(taskData.actualEndDate) || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="section" v-if="taskData.remarks">
        <h3 class="section-title">备注信息</h3>
        <div class="remarks-content">
          {{ taskData.remarks }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button 
          v-if="taskData.status === 0"
          type="primary" 
          size="large"
          @click="handleExecute"
          style="width: 100%;"
        >
          开始执行任务
        </el-button>
        
        <div v-if="taskData.status === 1" class="action-buttons">
          <el-button 
            type="success" 
            size="large"
            @click="handleComplete"
            style="flex: 1;"
          >
            完成任务
          </el-button>
          <el-button 
            type="warning" 
            size="large"
            @click="handleSamplingManagement"
            style="flex: 1;"
          >
            采样管理
          </el-button>
        </div>

        <el-button 
          v-if="taskData.status === 2"
          type="info" 
          size="large"
          @click="handleSamplingManagement"
          style="width: 100%;"
        >
          查看采样记录
        </el-button>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p>任务详情 - {{ new Date().toLocaleString() }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getGroupDetail, updateGroupStatus } from '@/api/sampling/taskGroup'
import { parseTime } from '@/utils/ruoyi'
import { setCache, getCache, CACHE_KEYS } from '@/utils/cache'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(true)
const error = ref('')
const taskData = ref({})

// 方法
const goBack = () => {
  router.go(-1)
}

const fetchTaskDetail = async () => {
  try {
    loading.value = true
    error.value = ''

    const taskId = route.query.id
    if (!taskId) {
      throw new Error('任务ID参数缺失')
    }

    // 先尝试从缓存获取
    const cacheKey = CACHE_KEYS.TASK_DETAIL + taskId
    const cachedData = getCache(cacheKey)
    
    if (cachedData) {
      taskData.value = cachedData
      loading.value = false
      
      // 后台更新数据
      try {
        const response = await getGroupDetail(taskId)
        if (response.code === 200) {
          taskData.value = response.data
          setCache(cacheKey, response.data)
        }
      } catch (error) {
        console.log('后台更新失败，使用缓存数据')
      }
      return
    }

    // 缓存中没有数据，从服务器获取
    const response = await getGroupDetail(taskId)
    if (response.code === 200) {
      taskData.value = response.data
      setCache(cacheKey, response.data)
    } else {
      throw new Error(response.msg || '获取任务详情失败')
    }
  } catch (err) {
    error.value = err.message || '获取任务详情失败'
    console.error('获取任务详情失败:', err)
  } finally {
    loading.value = false
  }
}

const retryLoad = () => {
  error.value = ''
  fetchTaskDetail()
}

const handleExecute = async () => {
  try {
    await updateGroupStatus(taskData.value.id, 1)
    ElMessage.success('任务已开始执行')
    taskData.value.status = 1
    
    // 更新缓存
    const cacheKey = CACHE_KEYS.TASK_DETAIL + taskData.value.id
    setCache(cacheKey, taskData.value)
  } catch (error) {
    console.error('开始执行任务失败:', error)
    ElMessage.error('开始执行任务失败')
  }
}

const handleComplete = async () => {
  try {
    await updateGroupStatus(taskData.value.id, 2)
    ElMessage.success('任务已完成')
    taskData.value.status = 2
    
    // 更新缓存
    const cacheKey = CACHE_KEYS.TASK_DETAIL + taskData.value.id
    setCache(cacheKey, taskData.value)
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  }
}

const handleSamplingManagement = () => {
  router.push(`/sampling-task-h5?id=${taskData.value.id}`)
}

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-pending'
    case 1: return 'status-active'
    case 2: return 'status-completed'
    default: return ''
  }
}

const getStatusLabel = (status) => {
  switch (status) {
    case 0: return '待执行'
    case 1: return '执行中'
    case 2: return '已完成'
    default: return '未知'
  }
}

const formatDate = (date) => {
  if (!date) return ''
  return parseTime(date, '{y}-{m}-{d}')
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}')
}

// 生命周期
onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped>
.h5-task-detail {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  width: 44px;
}

.loading {
  padding: 20px;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.error .el-icon {
  margin-bottom: 8px;
  font-size: 48px;
  color: #f56c6c;
}

.error-actions {
  margin-top: 15px;
}

.task-content {
  padding: 15px;
}

.section {
  background: white;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.info-grid {
  padding: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  color: #666;
  font-size: 14px;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #333;
  font-size: 14px;
  word-break: break-all;
  flex: 1;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-active {
  background: #f0f9ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.remarks-content {
  padding: 15px;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

.action-section {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
  background: white;
  border-radius: 8px;
  margin: 0 15px 15px;
}

.footer p {
  margin: 0;
}
</style>
