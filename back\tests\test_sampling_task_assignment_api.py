import pytest
import httpx
from datetime import datetime, timedelta


class TestSamplingTaskAssignmentAPI:
    """采样任务分配API测试"""
    
    BASE_URL = "http://127.0.0.1:9099"
    HEADERS = {"Authorization": "Bearer test_token"}
    
    @pytest.mark.asyncio
    async def test_create_sampling_task_from_second_cycle(self):
        """测试从第2周期开始创建采样任务"""
        async with httpx.AsyncClient() as client:
            # 准备测试数据
            task_data = {
                "taskName": "测试采样任务-从第2周期开始",
                "taskDescription": "测试从第2周期开始的采样任务创建",
                "projectQuotationId": 1,  # 假设存在的项目报价ID
                "assignedUserId": 1,  # 假设存在的用户ID
                "selectedCycleItemIds": [2, 3],  # 假设这些是连续的周期条目ID
                "plannedStartTime": (datetime.now() + timedelta(days=1)).isoformat(),
                "plannedEndTime": (datetime.now() + timedelta(days=7)).isoformat()
            }
            
            # 发送创建请求
            response = await client.post(
                f"{self.BASE_URL}/sampling/assignment/create-task",
                json=task_data,
                headers=self.HEADERS
            )
            
            # 验证响应
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            # 如果是业务逻辑错误（如数据不存在），状态码可能是200但success为false
            # 如果是验证逻辑错误，应该会返回具体的错误信息
            response_data = response.json()
            
            if response.status_code == 200:
                if response_data.get("success"):
                    print("✅ 采样任务创建成功")
                    print(f"任务ID: {response_data.get('data', {}).get('taskId')}")
                    print(f"任务编号: {response_data.get('data', {}).get('taskNumber')}")
                else:
                    print(f"❌ 采样任务创建失败: {response_data.get('msg')}")
                    # 检查是否还是周期验证错误
                    if "周期必须从第1周期开始" in response_data.get('msg', ''):
                        pytest.fail("验证逻辑修改失败，仍然要求从第1周期开始")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
    
    @pytest.mark.asyncio
    async def test_create_sampling_task_non_continuous_cycles(self):
        """测试非连续周期的采样任务创建（应该失败）"""
        async with httpx.AsyncClient() as client:
            # 准备测试数据 - 非连续的周期
            task_data = {
                "taskName": "测试采样任务-非连续周期",
                "taskDescription": "测试非连续周期的采样任务创建",
                "projectQuotationId": 1,
                "assignedUserId": 1,
                "selectedCycleItemIds": [1, 3],  # 跳过了第2周期
                "plannedStartTime": (datetime.now() + timedelta(days=1)).isoformat(),
                "plannedEndTime": (datetime.now() + timedelta(days=7)).isoformat()
            }
            
            # 发送创建请求
            response = await client.post(
                f"{self.BASE_URL}/sampling/assignment/create-task",
                json=task_data,
                headers=self.HEADERS
            )
            
            # 验证响应
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            response_data = response.json()
            
            if response.status_code == 200:
                if not response_data.get("success"):
                    error_msg = response_data.get('msg', '')
                    print(f"✅ 正确拒绝了非连续周期选择: {error_msg}")
                    # 验证错误消息包含连续性检查
                    if "周期选择必须连续" in error_msg:
                        print("✅ 连续性验证正常工作")
                    else:
                        print(f"⚠️ 错误消息可能不是连续性验证: {error_msg}")
                else:
                    pytest.fail("非连续周期选择应该被拒绝，但创建成功了")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")

    @pytest.mark.asyncio
    async def test_get_quotation_cycle_items(self):
        """测试获取项目报价的检测周期条目"""
        async with httpx.AsyncClient() as client:
            # 获取项目报价的检测周期条目
            response = await client.get(
                f"{self.BASE_URL}/sampling/assignment/quotation/1/cycle-items",
                headers=self.HEADERS
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("success"):
                    cycle_items = response_data.get('data', [])
                    print(f"✅ 获取到 {len(cycle_items)} 个检测周期条目")
                    
                    # 显示周期条目信息
                    for item in cycle_items[:5]:  # 只显示前5个
                        print(f"  - ID: {item.get('id')}, 项目明细ID: {item.get('projectQuotationItemId')}, 周期号: {item.get('cycleNumber')}, 状态: {item.get('status')}")
                else:
                    print(f"❌ 获取检测周期条目失败: {response_data.get('msg')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")


if __name__ == "__main__":
    import asyncio
    
    async def run_tests():
        test_instance = TestSamplingTaskAssignmentAPI()
        
        print("=== 测试获取检测周期条目 ===")
        await test_instance.test_get_quotation_cycle_items()
        
        print("\n=== 测试从第2周期开始创建采样任务 ===")
        await test_instance.test_create_sampling_task_from_second_cycle()
        
        print("\n=== 测试非连续周期创建采样任务 ===")
        await test_instance.test_create_sampling_task_non_continuous_cycles()
    
    asyncio.run(run_tests())