# 采样任务模块说明

## 概述

采样任务模块是LIMS2系统的重要组成部分，主要负责管理实验室的采样计划、任务分配和执行跟踪。该模块包含检测周期条目管理和采样任务管理两个核心功能。

## 功能特性

### 1. 检测周期条目管理

- **自动生成**：当项目报价审核通过时，系统自动根据检测周期生成对应的检测周期条目
- **手动生成**：支持手动为指定项目报价生成检测周期条目
- **重新生成**：支持重新生成检测周期条目（会删除原有条目）
- **状态管理**：支持批量和单个更新条目状态
- **查询筛选**：支持按项目报价、状态等条件查询
- **数据导出**：支持导出检测周期条目数据

### 2. 采样任务管理

- **任务创建**：创建采样任务并关联检测周期条目
- **任务分配**：将任务分配给指定用户执行
- **进度跟踪**：实时跟踪任务执行状态
- **时间管理**：设置计划开始时间和结束时间
- **查询统计**：支持多维度查询和统计分析
- **数据导出**：支持导出任务数据

## 数据库设计

### 主要数据表

1. **detection_cycle_item** - 检测周期条目表
   - 存储项目报价对应的检测周期条目
   - 包含周期序号、状态、时间信息等

2. **sampling_task** - 采样任务表
   - 存储采样任务基本信息
   - 包含任务编号、名称、状态、分配用户等

3. **sampling_task_cycle_item** - 任务与周期条目关联表
   - 建立采样任务与检测周期条目的多对多关系

4. **sampling_task_sequence** - 任务编号序列表
   - 用于生成唯一的任务编号

### 状态字典

- **采样任务状态**：待处理、进行中、已完成、已暂停、已取消
- **检测周期状态**：未分配、已分配、进行中、已完成、已取消
- **采样类型**：定期采样、临时采样、应急采样、补充采样
- **采样优先级**：低、中、高、紧急

## 技术架构

### 后端技术栈

- **框架**：FastAPI
- **数据库**：MySQL
- **ORM**：SQLAlchemy
- **认证**：JWT Token

### 前端技术栈

- **框架**：Vue 3
- **UI组件**：Element Plus
- **状态管理**：Vuex
- **路由**：Vue Router

## 文件结构

### 后端文件

```
back/
├── module_sampling/
│   ├── controller/
│   │   ├── sampling_task_controller.py      # 采样任务控制器
│   │   └── detection_cycle_item_controller.py # 检测周期条目控制器
│   ├── model/
│   │   ├── sampling_task.py                 # 采样任务模型
│   │   ├── detection_cycle_item.py          # 检测周期条目模型
│   │   └── sampling_task_cycle_item.py      # 关联表模型
│   ├── service/
│   │   ├── sampling_task_service.py         # 采样任务服务
│   │   └── detection_cycle_item_service.py  # 检测周期条目服务
│   └── dto/
│       ├── sampling_task_dto.py             # 采样任务DTO
│       └── detection_cycle_item_dto.py      # 检测周期条目DTO
└── sql/
    ├── sampling_task_migration.sql          # 数据库迁移脚本
    └── sampling_dict_data.sql               # 字典数据配置
```

### 前端文件

```
front/
├── src/
│   ├── api/sampling/
│   │   ├── samplingTask.js                  # 采样任务API
│   │   └── detectionCycleItem.js            # 检测周期条目API
│   ├── views/sampling/
│   │   ├── task/
│   │   │   └── index.vue                    # 采样任务管理页面
│   │   └── cycleItem/
│   │       └── index.vue                    # 检测周期条目管理页面
│   └── router/modules/
│       └── sampling.js                      # 采样模块路由配置
```

## 部署说明

### 1. 数据库初始化

```sql
-- 执行数据库迁移脚本
source /path/to/sampling_task_migration.sql

-- 执行字典数据配置
source /path/to/sampling_dict_data.sql
```

### 2. 后端部署

1. 确保已在 `server.py` 中注册了采样模块的控制器
2. 重启后端服务

### 3. 前端部署

1. 确保前端页面文件已正确放置
2. 路由配置已正确设置
3. 重新构建前端项目

## 使用流程

### 1. 检测周期条目生成

1. 项目报价审核通过后，系统自动生成检测周期条目
2. 或者手动在"检测周期条目"页面为指定项目生成条目

### 2. 采样任务创建

1. 在"采样任务"页面点击"新增"按钮
2. 填写任务基本信息（任务名称、描述等）
3. 选择关联的项目报价
4. 分配执行用户
5. 设置计划时间
6. 保存任务

### 3. 任务执行跟踪

1. 查看任务列表，了解任务状态
2. 更新任务状态（进行中、已完成等）
3. 查看任务详情和执行进度

## 权限配置

### 检测周期条目权限

- `sampling:cycle-item:query` - 查询权限
- `sampling:cycle-item:generate` - 生成权限
- `sampling:cycle-item:regenerate` - 重新生成权限
- `sampling:cycle-item:edit` - 修改权限
- `sampling:cycle-item:remove` - 删除权限
- `sampling:cycle-item:export` - 导出权限

### 采样任务权限

- `sampling:task:query` - 查询权限
- `sampling:task:add` - 新增权限
- `sampling:task:edit` - 修改权限
- `sampling:task:remove` - 删除权限
- `sampling:task:export` - 导出权限
- `sampling:task:assign` - 分配权限

## 注意事项

1. **数据一致性**：删除项目报价时会级联删除相关的检测周期条目
2. **权限控制**：确保用户具有相应的操作权限
3. **状态流转**：任务状态变更需要遵循业务规则
4. **数据备份**：重要操作前建议备份相关数据

## 扩展功能

未来可考虑添加的功能：

1. **消息通知**：任务状态变更时发送通知
2. **报表统计**：采样任务执行情况统计报表
3. **移动端支持**：支持移动设备访问和操作
4. **工作流引擎**：集成工作流引擎管理复杂的采样流程
5. **GPS定位**：记录采样地点的GPS坐标
6. **照片上传**：支持上传采样现场照片

## 联系方式

如有问题或建议，请联系开发团队。