"""
样品记录功能测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dto.sample_record_dto import Sam<PERSON><PERSON><PERSON>ord<PERSON>reateDTO
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.sampling_task_assignment_do import SamplingTaskAssignment
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem


class TestSampleRecordService:
    """样品记录服务测试"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def sample_record_service(self, mock_db):
        """样品记录服务实例"""
        return SampleRecordService(mock_db)
    
    @pytest.fixture
    def sample_create_dto(self):
        """样品记录创建DTO"""
        return SampleRecordCreateDTO(
            sampling_task_assignment_id=1,
            detection_cycle_item_id=1,
            project_quotation_item_id=1,
            sample_number=1,
            sample_type="水样",
            sample_source="地表水",
            point_name="监测点1",
            cycle_number=1,
            cycle_type="月度",
            detection_category="水质检测",
            detection_parameter="pH值",
            detection_method="玻璃电极法",
            remark="测试样品"
        )
    
    @pytest.fixture
    def mock_assignment(self):
        """模拟执行任务指派"""
        assignment = MagicMock(spec=SamplingTaskAssignment)
        assignment.id = 1
        assignment.sampling_task_id = 1
        assignment.cycle_number = 1
        assignment.cycle_type = "月度"
        assignment.detection_category = "水质检测"
        assignment.point_name = "监测点1"
        return assignment
    
    @pytest.fixture
    def mock_cycle_item(self):
        """模拟检测周期条目"""
        cycle_item = MagicMock(spec=DetectionCycleItem)
        cycle_item.id = 1
        cycle_item.cycle_number = 1
        
        # 模拟项目报价明细
        quotation_item = MagicMock(spec=ProjectQuotationItem)
        quotation_item.id = 1
        quotation_item.qualification_code = "TEST001"
        quotation_item.category = "水质检测"
        quotation_item.parameter = "pH值"
        quotation_item.method = "玻璃电极法"
        quotation_item.sample_source = "地表水"
        quotation_item.point_name = "监测点1"
        quotation_item.cycle_type = "月度"
        quotation_item.frequency = 1
        quotation_item.sample_count = 2
        
        cycle_item.project_quotation_item = quotation_item
        return cycle_item
    
    async def test_create_sample_record(self, sample_record_service, sample_create_dto, mock_db):
        """测试创建样品记录"""
        # 模拟DAO方法
        sample_record_service.sample_record_dao.get_max_sample_number_by_assignment = AsyncMock(return_value=0)
        sample_record_service.sample_record_dao.create_sample_record = AsyncMock()
        
        # 模拟创建的样品记录
        mock_record = MagicMock(spec=SampleRecord)
        mock_record.id = 1
        mock_record.sample_number = 1
        mock_record.to_dict.return_value = {
            'id': 1,
            'sample_number': 1,
            'sample_type': '水样',
            'status': 0
        }
        
        sample_record_service.sample_record_dao.create_sample_record.return_value = mock_record
        sample_record_service._convert_to_dto = AsyncMock()
        
        # 执行测试
        result = await sample_record_service.create_sample_record(sample_create_dto, 1)
        
        # 验证
        sample_record_service.sample_record_dao.get_max_sample_number_by_assignment.assert_called_once_with(1)
        sample_record_service.sample_record_dao.create_sample_record.assert_called_once()
        mock_db.commit.assert_called_once()
    
    async def test_calculate_max_sample_count(self, sample_record_service, mock_cycle_item):
        """测试计算最大样品数量"""
        # 模拟非现场直读类型
        sample_record_service._is_on_site_check_item = AsyncMock(return_value=False)
        
        # 执行测试
        result = await sample_record_service._calculate_max_sample_count([mock_cycle_item])
        
        # 验证：频次(1) * 样品数(2) = 2
        assert result == 2
    
    async def test_calculate_max_sample_count_on_site_check(self, sample_record_service, mock_cycle_item):
        """测试现场直读类型不生成样品记录"""
        # 模拟现场直读类型
        sample_record_service._is_on_site_check_item = AsyncMock(return_value=True)
        
        # 执行测试
        result = await sample_record_service._calculate_max_sample_count([mock_cycle_item])
        
        # 验证：现场直读类型应该返回0
        assert result == 0
    
    async def test_is_on_site_check_item_with_qualification_code(self, sample_record_service, mock_db):
        """测试通过资质编号判断现场直读类型"""
        # 模拟项目报价明细
        quotation_item = MagicMock(spec=ProjectQuotationItem)
        quotation_item.qualification_code = "TEST001"
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = "ON_SITE_CHECK"
        mock_db.execute.return_value = mock_result
        
        # 执行测试
        result = await sample_record_service._is_on_site_check_item(quotation_item)
        
        # 验证
        assert result is True
        mock_db.execute.assert_called_once()
    
    async def test_is_on_site_check_item_with_parameter_method(self, sample_record_service, mock_db):
        """测试通过参数和方法判断现场直读类型"""
        # 模拟项目报价明细（无资质编号）
        quotation_item = MagicMock(spec=ProjectQuotationItem)
        quotation_item.qualification_code = None
        quotation_item.parameter = "pH值"
        quotation_item.method = "便携式pH计法"
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = "ON_SITE_CHECK"
        mock_db.execute.return_value = mock_result
        
        # 执行测试
        result = await sample_record_service._is_on_site_check_item(quotation_item)
        
        # 验证
        assert result is True
        mock_db.execute.assert_called_once()
    
    async def test_generate_sample_records_for_assignment(self, sample_record_service, mock_assignment, mock_cycle_item):
        """测试为执行任务生成样品记录"""
        # 模拟方法
        sample_record_service._get_assignment_with_details = AsyncMock(return_value=mock_assignment)
        sample_record_service._get_cycle_items_for_assignment = AsyncMock(return_value=[mock_cycle_item])
        sample_record_service._calculate_max_sample_count = AsyncMock(return_value=2)
        sample_record_service.sample_record_dao.batch_create_sample_records = AsyncMock()
        sample_record_service._convert_to_dto = AsyncMock()
        
        # 模拟创建的样品记录
        mock_records = [MagicMock(spec=SampleRecord) for _ in range(2)]
        sample_record_service.sample_record_dao.batch_create_sample_records.return_value = mock_records
        
        # 执行测试
        result = await sample_record_service.generate_sample_records_for_assignment(1, 1)
        
        # 验证
        sample_record_service._get_assignment_with_details.assert_called_once_with(1)
        sample_record_service._get_cycle_items_for_assignment.assert_called_once_with(mock_assignment)
        sample_record_service._calculate_max_sample_count.assert_called_once_with([mock_cycle_item])
        sample_record_service.sample_record_dao.batch_create_sample_records.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
