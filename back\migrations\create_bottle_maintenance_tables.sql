-- 创建瓶组管理表
CREATE TABLE IF NOT EXISTS `bottle_maintenance` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bottle_code` varchar(20) NOT NULL COMMENT '瓶组编码，BOL+6位递增数',
  `bottle_type` varchar(100) NOT NULL COMMENT '容器类型，如玻璃瓶、木盒',
  `bottle_volume` varchar(50) NOT NULL COMMENT '容器容量，如100ML,1kg,>1L,500mL',
  `storage_styles` json COMMENT '存储方式，JSON数组，如["冷藏","避光"]',
  `fix_styles` json COMMENT '固定方式，JSON数组，如["尽快安装","吊顶上"]',
  `sample_age` int DEFAULT NULL COMMENT '样品时效数值',
  `sample_age_unit` varchar(20) DEFAULT NULL COMMENT '样品时效单位，如小时、天',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bottle_code` (`bottle_code`),
  KEY `idx_bottle_type` (`bottle_type`),
  KEY `idx_bottle_volume` (`bottle_volume`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='瓶组管理表';

-- 创建瓶组管理与技术手册关联表
CREATE TABLE IF NOT EXISTS `bottle_maintenance_technical_manual` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bottle_maintenance_id` int NOT NULL COMMENT '瓶组管理ID',
  `technical_manual_id` bigint NOT NULL COMMENT '技术手册ID',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bottle_technical` (`bottle_maintenance_id`, `technical_manual_id`),
  KEY `idx_bottle_maintenance_id` (`bottle_maintenance_id`),
  KEY `idx_technical_manual_id` (`technical_manual_id`),
  CONSTRAINT `fk_bottle_maintenance_relation_bottle` FOREIGN KEY (`bottle_maintenance_id`) REFERENCES `bottle_maintenance` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bottle_maintenance_relation_technical` FOREIGN KEY (`technical_manual_id`) REFERENCES `technical_manual` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='瓶组管理与技术手册关联表';

-- 创建瓶组编码序列表（用于生成BOL编码）
CREATE TABLE IF NOT EXISTS `bottle_code_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `current_value` int NOT NULL DEFAULT '0' COMMENT '当前序列值',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='瓶组编码序列表';

-- 初始化序列表
INSERT INTO `bottle_code_sequence` (`current_value`) VALUES (0) ON DUPLICATE KEY UPDATE `current_value` = `current_value`;
