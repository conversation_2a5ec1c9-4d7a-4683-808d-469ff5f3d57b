/**
 * 本地缓存工具类
 * 用于H5页面的离线数据缓存
 */

const CACHE_PREFIX = 'lims_h5_'
const CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟过期

/**
 * 设置缓存
 * @param {string} key 缓存键
 * @param {any} data 缓存数据
 * @param {number} expireTime 过期时间（毫秒），默认30分钟
 */
export function setCache(key, data, expireTime = CACHE_EXPIRE_TIME) {
  const cacheData = {
    data,
    timestamp: Date.now(),
    expireTime
  }
  
  try {
    localStorage.setItem(CACHE_PREFIX + key, JSON.stringify(cacheData))
  } catch (error) {
    console.warn('缓存设置失败:', error)
  }
}

/**
 * 获取缓存
 * @param {string} key 缓存键
 * @returns {any} 缓存数据，如果不存在或已过期则返回null
 */
export function getCache(key) {
  try {
    const cacheStr = localStorage.getItem(CACHE_PREFIX + key)
    if (!cacheStr) return null
    
    const cacheData = JSON.parse(cacheStr)
    const now = Date.now()
    
    // 检查是否过期
    if (now - cacheData.timestamp > cacheData.expireTime) {
      removeCache(key)
      return null
    }
    
    return cacheData.data
  } catch (error) {
    console.warn('缓存获取失败:', error)
    return null
  }
}

/**
 * 删除缓存
 * @param {string} key 缓存键
 */
export function removeCache(key) {
  try {
    localStorage.removeItem(CACHE_PREFIX + key)
  } catch (error) {
    console.warn('缓存删除失败:', error)
  }
}

/**
 * 清空所有缓存
 */
export function clearAllCache() {
  try {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(CACHE_PREFIX)) {
        localStorage.removeItem(key)
      }
    })
  } catch (error) {
    console.warn('清空缓存失败:', error)
  }
}

/**
 * 获取缓存大小（字节）
 */
export function getCacheSize() {
  try {
    let size = 0
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(CACHE_PREFIX)) {
        size += localStorage.getItem(key).length
      }
    })
    return size
  } catch (error) {
    console.warn('获取缓存大小失败:', error)
    return 0
  }
}

/**
 * 格式化缓存大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatCacheSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 缓存键常量
 */
export const CACHE_KEYS = {
  TASK_DETAIL: 'task_detail_',
  SAMPLE_RECORDS: 'sample_records_',
  SAMPLE_STATISTICS: 'sample_statistics_',
  POINT_INFO: 'point_info_',
  USER_LOCATION: 'user_location'
}
