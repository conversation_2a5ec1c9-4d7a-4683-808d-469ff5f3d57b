-- 创建合同管理表
-- 执行时间：2025-01-XX
-- 说明：合同管理模块的基本信息表

CREATE TABLE `contract` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_name` varchar(500) NOT NULL COMMENT '合同名称',
  `contract_number` varchar(100) DEFAULT NULL COMMENT '合同编号',
  `external_contract_number` varchar(100) DEFAULT NULL COMMENT '外部合同编号',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `region_province` varchar(50) NOT NULL COMMENT '省份',
  `region_city` varchar(50) NOT NULL COMMENT '城市',
  `client_name` varchar(200) NOT NULL COMMENT '委托单位',
  `client_contact` varchar(100) NOT NULL COMMENT '委托单位联系人',
  `acquisition_method` varchar(50) NOT NULL COMMENT '取得方式',
  `project_manager_ids` json NOT NULL COMMENT '项目负责人ID数组',
  `project_service_id` int NOT NULL COMMENT '项目客服ID',
  `dept_id` int DEFAULT NULL COMMENT '所属部门ID',
  `contract_sign_date` date DEFAULT NULL COMMENT '合同签订日期',
  `amount_type` varchar(20) NOT NULL COMMENT '金额类型',
  `contract_amount` decimal(15,2) NOT NULL COMMENT '合同金额',
  `quotation_total_amount` decimal(15,2) DEFAULT NULL COMMENT '报价单总金额',
  `changed_contract_amount` decimal(15,2) DEFAULT NULL COMMENT '变更后合同金额',
  `outsourcing_amount` decimal(15,2) DEFAULT NULL COMMENT '外协金额',
  `outsourcing_company` varchar(200) DEFAULT NULL COMMENT '外协单位',
  `completion_time` datetime DEFAULT NULL COMMENT '完工时间',
  `contract_status` varchar(50) DEFAULT NULL COMMENT '合同状态',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志(0代表存在 1代表删除)',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_name` (`contract_name`),
  KEY `idx_contract_number` (`contract_number`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_client_name` (`client_name`),
  KEY `idx_contract_status` (`contract_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同管理表';

-- 创建合同商务信息-部门分摊表
CREATE TABLE `contract_business_department` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `project_code` varchar(100) NOT NULL COMMENT '项目报价编号',
  `dept_id` int NOT NULL COMMENT '部门ID',
  `allocation_amount` decimal(15,2) NOT NULL COMMENT '分摊金额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志(0代表存在 1代表删除)',
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_contract_business_dept_contract` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同商务信息-部门分摊表';

-- 创建合同商务信息-任务拆解表
CREATE TABLE `contract_business_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `project_code` varchar(100) NOT NULL COMMENT '项目报价编号',
  `task_number` varchar(100) NOT NULL COMMENT '任务编号',
  `task_amount` decimal(15,2) NOT NULL COMMENT '任务金额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志(0代表存在 1代表删除)',
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_task_number` (`task_number`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_contract_business_task_contract` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同商务信息-任务拆解表';

-- 验证表创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'contract';

-- 验证字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'contract'
ORDER BY ORDINAL_POSITION;
