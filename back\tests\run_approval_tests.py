#!/usr/bin/env python3
"""
运行所有审批流程相关的测试
"""

import asyncio
import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_pytest_command(test_file, verbose=True):
    """
    运行pytest命令

    :param test_file: 测试文件路径
    :param verbose: 是否显示详细输出
    :return: 测试结果
    """
    cmd = ["python", "-m", "pytest"]

    if verbose:
        cmd.extend(["-v", "-s"])

    cmd.append(test_file)

    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=project_root)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        print(f"返回码: {result.returncode}")

        return result.returncode == 0

    except Exception as e:
        print(f"运行测试时发生错误: {e}")
        return False


def run_all_approval_tests():
    """
    运行所有审批相关的测试
    """
    print("开始运行项目报价审批流程测试套件")
    print(f"项目根目录: {project_root}")

    # 定义测试文件列表
    test_files = [
        "tests/test_approval_dao.py",
        "tests/test_approval_service.py",
        "tests/test_approval_api.py",
        "tests/test_approval_integration.py",
        "tests/test_project_quotation_approval.py"
    ]

    results = {}

    for test_file in test_files:
        test_path = project_root / test_file

        if not test_path.exists():
            print(f"⚠️  测试文件不存在: {test_file}")
            results[test_file] = False
            continue

        success = run_pytest_command(str(test_path))
        results[test_file] = success

    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print("测试结果摘要")
    print(f"{'='*60}")

    total_tests = len(test_files)
    passed_tests = sum(1 for success in results.values() if success)
    failed_tests = total_tests - passed_tests

    for test_file, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_file}")

    print(f"\n总计: {total_tests} 个测试文件")
    print(f"通过: {passed_tests} 个")
    print(f"失败: {failed_tests} 个")

    if failed_tests == 0:
        print("\n🎉 所有测试都通过了！")
        return True
    else:
        print(f"\n⚠️  有 {failed_tests} 个测试失败")
        return False


def run_specific_test(test_name):
    """
    运行特定的测试

    :param test_name: 测试名称
    """
    test_mapping = {
        "dao": "tests/test_approval_dao.py",
        "service": "tests/test_approval_service.py",
        "api": "tests/test_approval_api.py",
        "integration": "tests/test_approval_integration.py",
        "original": "tests/test_project_quotation_approval.py"
    }

    if test_name not in test_mapping:
        print(f"未知的测试名称: {test_name}")
        print(f"可用的测试: {', '.join(test_mapping.keys())}")
        return False

    test_file = test_mapping[test_name]
    test_path = project_root / test_file

    if not test_path.exists():
        print(f"测试文件不存在: {test_file}")
        return False

    return run_pytest_command(str(test_path))


def check_test_environment():
    """
    检查测试环境
    """
    print("检查测试环境...")

    # 检查pytest是否安装
    try:
        import pytest
        print(f"✅ pytest 已安装，版本: {pytest.__version__}")
    except ImportError:
        print("❌ pytest 未安装，请运行: pip install pytest pytest-asyncio")
        return False

    # 检查pytest-asyncio是否安装
    try:
        import pytest_asyncio
        print(f"✅ pytest-asyncio 已安装")
    except ImportError:
        print("❌ pytest-asyncio 未安装，请运行: pip install pytest-asyncio")
        return False

    # 检查必要的模块是否存在
    required_modules = [
        "module_admin.entity.do.user_do",
        "module_admin.entity.do.role_do",
        "module_quotation.entity.do.project_quotation_do",
        "module_quotation.service.project_quotation_approval_service"
    ]

    for module_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ 模块 {module_name} 可用")
        except ImportError as e:
            print(f"❌ 模块 {module_name} 不可用: {e}")
            return False

    print("✅ 测试环境检查通过")
    return True


def main():
    """
    主函数
    """
    if len(sys.argv) > 1:
        command = sys.argv[1]
    else:
        command = "help"

    if command == "help":
        print("用法:")
        print("  python run_approval_tests.py check        # 检查测试环境")
        print("  python run_approval_tests.py all          # 运行所有测试")
        print("  python run_approval_tests.py dao          # 运行DAO层测试")
        print("  python run_approval_tests.py service      # 运行服务层测试")
        print("  python run_approval_tests.py api          # 运行API层测试")
        print("  python run_approval_tests.py integration  # 运行集成测试")
        print("  python run_approval_tests.py original     # 运行原始测试")
        sys.exit(0)
    elif command == "check":
        success = check_test_environment()
        sys.exit(0 if success else 1)
    elif command == "all":
        if not check_test_environment():
            sys.exit(1)
        success = run_all_approval_tests()
        sys.exit(0 if success else 1)
    else:
        if not check_test_environment():
            sys.exit(1)
        success = run_specific_test(command)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
