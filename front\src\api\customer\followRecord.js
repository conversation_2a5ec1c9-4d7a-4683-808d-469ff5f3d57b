import request from '@/utils/request'

// 查询客户跟进记录列表
export function listCustomerFollowRecord(query) {
  return request({
    url: '/customer/follow-record/list',
    method: 'get',
    params: query
  })
}

// 查询客户跟进记录分页列表
export function pageCustomerFollowRecord(query) {
  return request({
    url: '/customer/follow-record/page',
    method: 'get',
    params: query
  })
}

// 查询客户跟进记录详细
export function getCustomerFollowRecord(id) {
  return request({
    url: '/customer/follow-record/' + id,
    method: 'get'
  })
}

// 新增客户跟进记录
export function addCustomerFollowRecord(data) {
  return request({
    url: '/customer/follow-record',
    method: 'post',
    data: data
  })
}

// 修改客户跟进记录
export function updateCustomerFollowRecord(data) {
  return request({
    url: '/customer/follow-record',
    method: 'put',
    data: data
  })
}

// 删除客户跟进记录
export function delCustomerFollowRecord(id) {
  return request({
    url: '/customer/follow-record/' + id,
    method: 'delete'
  })
}


