<template>
  <div class="test-quotation-total">
    <el-card header="测试合同报价单总金额计算">
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="合同ID">
          <el-input-number v-model="testForm.contractId" :min="1" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testCalculateTotal" :loading="loading">
            测试计算报价单总金额
          </el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div v-if="result">
        <h3>计算结果：</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
      
      <div v-if="error" style="color: red;">
        <h3>错误信息：</h3>
        <pre>{{ error }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { calculateQuotationTotalAmount } from '@/api/contract/contract'

const testForm = ref({
  contractId: 1
})

const loading = ref(false)
const result = ref(null)
const error = ref(null)

const testCalculateTotal = async () => {
  if (!testForm.value.contractId) {
    ElMessage.error('请输入合同ID')
    return
  }
  
  loading.value = true
  result.value = null
  error.value = null
  
  try {
    const response = await calculateQuotationTotalAmount(testForm.value.contractId)
    if (response.code === 200) {
      result.value = response.data
      ElMessage.success('计算成功')
    } else {
      error.value = response.msg || '计算失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    error.value = err.message || '请求失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.test-quotation-total {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
