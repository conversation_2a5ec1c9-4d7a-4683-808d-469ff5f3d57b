"""
采样点位信息服务层
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from module_sampling.entity.do.sampling_point_info_do import SamplingPointInfo
from module_sampling.dto.sampling_point_info_dto import (
    SamplingPointInfoCreateDTO,
    SamplingPointInfoUpdateDTO,
    SamplingPointInfoDTO
)
from utils.log_util import logger


class SamplingPointInfoService:
    """采样点位信息服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_point_info(self, point_info_dto: SamplingPointInfoCreateDTO, user_id: int) -> SamplingPointInfoDTO:
        """
        创建点位信息
        
        Args:
            point_info_dto: 点位信息创建DTO
            user_id: 创建用户ID
            
        Returns:
            创建的点位信息DTO
        """
        try:
            # 检查是否已存在该分组的点位信息
            existing_query = select(SamplingPointInfo).where(
                SamplingPointInfo.sampling_task_group_id == point_info_dto.sampling_task_group_id
            )
            existing_result = await self.db.execute(existing_query)
            existing_point_info = existing_result.scalar_one_or_none()
            
            if existing_point_info:
                raise ValueError(f"分组ID {point_info_dto.sampling_task_group_id} 的点位信息已存在")
            
            # 创建新的点位信息
            point_info = SamplingPointInfo(
                **point_info_dto.model_dump(exclude_unset=True),
                create_by=user_id,
                update_by=user_id
            )
            
            self.db.add(point_info)
            await self.db.commit()
            await self.db.refresh(point_info)
            
            logger.info(f"用户 {user_id} 创建点位信息成功，ID: {point_info.id}")
            return SamplingPointInfoDTO.model_validate(point_info)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建点位信息失败: {str(e)}")
            raise e
    
    async def get_point_info_by_id(self, point_info_id: int) -> Optional[SamplingPointInfoDTO]:
        """
        根据ID获取点位信息
        
        Args:
            point_info_id: 点位信息ID
            
        Returns:
            点位信息DTO
        """
        try:
            query = select(SamplingPointInfo).where(SamplingPointInfo.id == point_info_id)
            result = await self.db.execute(query)
            point_info = result.scalar_one_or_none()
            
            if point_info:
                return SamplingPointInfoDTO.model_validate(point_info)
            return None
            
        except Exception as e:
            logger.error(f"获取点位信息失败: {str(e)}")
            raise e
    
    async def get_point_info_by_group_id(self, group_id: int) -> Optional[SamplingPointInfoDTO]:
        """
        根据分组ID获取点位信息
        
        Args:
            group_id: 采样任务分组ID
            
        Returns:
            点位信息DTO
        """
        try:
            query = select(SamplingPointInfo).where(
                SamplingPointInfo.sampling_task_group_id == group_id
            )
            result = await self.db.execute(query)
            point_info = result.scalar_one_or_none()
            
            if point_info:
                return SamplingPointInfoDTO.model_validate(point_info)
            return None
            
        except Exception as e:
            logger.error(f"根据分组ID获取点位信息失败: {str(e)}")
            raise e
    
    async def update_point_info(self, point_info_id: int, point_info_dto: SamplingPointInfoUpdateDTO, user_id: int) -> Optional[SamplingPointInfoDTO]:
        """
        更新点位信息
        
        Args:
            point_info_id: 点位信息ID
            point_info_dto: 点位信息更新DTO
            user_id: 更新用户ID
            
        Returns:
            更新后的点位信息DTO
        """
        try:
            # 构建更新数据
            update_data = point_info_dto.model_dump(exclude_unset=True)
            if update_data:
                update_data['update_by'] = user_id
                
                # 执行更新
                query = update(SamplingPointInfo).where(
                    SamplingPointInfo.id == point_info_id
                ).values(**update_data)
                
                result = await self.db.execute(query)
                
                if result.rowcount == 0:
                    return None
                
                await self.db.commit()
                
                # 获取更新后的数据
                return await self.get_point_info_by_id(point_info_id)
            
            return await self.get_point_info_by_id(point_info_id)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新点位信息失败: {str(e)}")
            raise e
    
    async def delete_point_info(self, point_info_id: int) -> bool:
        """
        删除点位信息
        
        Args:
            point_info_id: 点位信息ID
            
        Returns:
            是否删除成功
        """
        try:
            query = delete(SamplingPointInfo).where(SamplingPointInfo.id == point_info_id)
            result = await self.db.execute(query)
            await self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除点位信息失败: {str(e)}")
            raise e
    
    async def get_point_info_list_by_task_id(self, task_id: int) -> List[SamplingPointInfoDTO]:
        """
        根据任务ID获取所有相关的点位信息列表
        
        Args:
            task_id: 采样任务ID
            
        Returns:
            点位信息DTO列表
        """
        try:
            # 通过任务分组关联查询点位信息
            query = select(SamplingPointInfo).join(
                SamplingPointInfo.sampling_task_group_id
            ).where(
                # 这里需要根据实际的关联关系调整
                # sampling_task_group.sampling_task_id == task_id
            )
            
            result = await self.db.execute(query)
            point_infos = result.scalars().all()
            
            return [SamplingPointInfoDTO.model_validate(point_info) for point_info in point_infos]
            
        except Exception as e:
            logger.error(f"根据任务ID获取点位信息列表失败: {str(e)}")
            raise e
