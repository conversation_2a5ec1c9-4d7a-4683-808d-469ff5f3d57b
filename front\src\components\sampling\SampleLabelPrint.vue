<template>
  <!-- 打印预览弹窗 -->
  <el-dialog
    v-model="visible"
    title="样品标签打印预览"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="print-preview-container">
      <!-- 打印预览区域 -->
      <div class="print-preview" ref="printRef">
        <div class="label-container" v-for="(label, index) in labels" :key="index">
          <div class="label-content">
            <!-- 左侧信息 -->
            <div class="label-left">
              <div class="label-row">
                <span class="label-title">样品类别：</span>
                <span class="label-value">{{ label.sampleCategory }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">样品编号：</span>
                <span class="label-value">{{ label.sampleNumber }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">采样日期：</span>
                <span class="label-value">{{ label.samplingDate }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">采样点位：</span>
                <span class="label-value">{{ label.samplingPoint }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">检测项目：</span>
                <span class="label-value">{{ label.testItems }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">保存容器：</span>
                <span class="label-value">{{ label.container }}</span>
              </div>
              <div class="label-row">
                <span class="label-title">保存方式：</span>
                <span class="label-value">{{ label.storageMethod }}</span>
              </div>
              <div class="label-row status-row">
                <span class="label-title">样品状态：</span>
                <div class="status-checkboxes">
                  <span class="status-item">
                    <span class="checkbox">☐</span> 待测
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 在测
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 测毕
                  </span>
                  <span class="status-item">
                    <span class="checkbox">☐</span> 留样
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 右侧二维码 -->
            <div class="label-right">
              <div class="qrcode-container">
                <canvas :data-ref="`qrcode-${index}`" class="qrcode"></canvas>
              </div>
            </div>
          </div>
          
          <!-- 底部信息 -->
          <div class="label-footer">
            <div class="company-name">{{ label.companyName }}</div>
            <div class="page-number">{{ label.pageNumber }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handlePrint">打印</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import QRCode from 'qrcode'
import { parseTime } from '@/utils/ruoyi'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  bottleGroup: {
    type: Object,
    default: () => ({})
  },
  taskInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const printRef = ref(null)

// 计算标签数据
const labels = computed(() => {
  if (!props.bottleGroup || !props.taskInfo) return []

  const bottleGroup = props.bottleGroup
  const taskInfo = props.taskInfo

  // 构建样品编号：任务编号（样品序号/样品总数量）
  const taskCode = taskInfo.taskCode || '241200010475'
  // 从瓶组的排序序号中获取样品序号
  const sampleIndex = bottleGroup.sortOrder || 1
  // 从任务信息中获取总样品数量
  const totalSamples = taskInfo.totalSamples || bottleGroup.totalCount || 4
  const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`

  // 构建采样点位：点位名称 （周期{周期数}）
  const pointName = taskInfo.pointName || '厂界下风向3'
  const cycleNumber = taskInfo.cycleNumber || bottleGroup.cycleNumber || 1
  const samplingPoint = `${pointName} （周期${cycleNumber}）`

  // 生成单个标签（每个瓶组一个标签）
  const labelsData = [{
    sampleCategory: taskInfo.detectionCategory || '无组织废气',
    sampleNumber: sampleNumber,
    samplingDate: parseTime(bottleGroup.samplingDate || taskInfo.samplingDate || new Date(), '{y}-{m}-{d}') || '2024-12-31',
    samplingPoint: samplingPoint,
    testItems: bottleGroup.testItems || taskInfo.testItems || '臭气浓度',
    container: bottleGroup.containerInfo || bottleGroup.bottleType || '10L 气袋 10L',
    storageMethod: bottleGroup.storageMethod || '17-25°C常温保存 密封 避光 无',
    companyName: '浙江求实环境监测有限公司',
    pageNumber: '1/1'
  }]

  return labelsData
})

// 方法
const generateQRCode = async (text, canvasRef) => {
  try {
    await QRCode.toCanvas(canvasRef, text, {
      width: 80,
      height: 80,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
  }
}

const generateAllQRCodes = async () => {
  await nextTick()

  for (let index = 0; index < labels.value.length; index++) {
    const label = labels.value[index]
    const canvasRef = document.querySelector(`[data-ref="qrcode-${index}"]`)
    if (canvasRef) {
      await generateQRCode(label.sampleNumber, canvasRef)
    }
  }
}

const configurePrinter = () => {
  try {
    // 动态添加打印样式
    const printStyle = document.createElement('style')
    printStyle.id = 'dynamic-print-style'
    printStyle.textContent = `
      @media print {
        @page {
          size: 75mm 60mm;
          margin: 0;
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
        body {
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
        * {
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
      }
    `

    // 移除旧的样式
    const oldStyle = document.getElementById('dynamic-print-style')
    if (oldStyle) {
      document.head.removeChild(oldStyle)
    }

    // 添加新样式
    document.head.appendChild(printStyle)

    console.log('打印机配置已设置: 75mm × 60mm')
  } catch (error) {
    console.warn('设置打印机配置失败:', error)
  }
}

const handlePrint = () => {
  try {
    // 配置打印机设置
    configurePrinter()

    // 显示打印配置提示
    ElMessage.info('请确保打印机纸张设置为75mm×60mm，边距设置为0')
  } catch (error) {
    console.error('打印配置失败:', error)
  }

  // 创建打印样式
  const printStyles = `
    <style>
      @media print {
        @page {
          size: 75mm 60mm;
          margin: 0;
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
        
        body {
          margin: 0;
          padding: 0;
          font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .label-container {
          width: 71mm;
          height: 56mm;
          page-break-after: always;
          border: 1px solid #000;
          padding: 2mm;
          box-sizing: border-box;
          position: relative;
        }
        
        .label-container:last-child {
          page-break-after: auto;
        }
        
        .label-content {
          display: flex;
          height: 45mm;
        }
        
        .label-left {
          flex: 1;
          font-size: 8px;
          line-height: 1.2;
        }
        
        .label-right {
          width: 20mm;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          padding-top: 2mm;
        }
        
        .label-row {
          margin-bottom: 1mm;
          display: flex;
          align-items: flex-start;
        }
        
        .label-title {
          font-weight: bold;
          min-width: 16mm;
          flex-shrink: 0;
        }
        
        .label-value {
          flex: 1;
          word-break: break-all;
        }
        
        .status-row {
          margin-top: 2mm;
        }
        
        .status-checkboxes {
          display: flex;
          flex-wrap: wrap;
          gap: 2mm;
        }
        
        .status-item {
          font-size: 7px;
          white-space: nowrap;
        }
        
        .checkbox {
          font-size: 8px;
          margin-right: 1mm;
        }
        
        .qrcode {
          width: 18mm !important;
          height: 18mm !important;
        }
        
        .label-footer {
          position: absolute;
          bottom: 1mm;
          left: 2mm;
          right: 2mm;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 7px;
        }
        
        .company-name {
          font-weight: bold;
        }
        
        .page-number {
          font-weight: bold;
        }
      }
    </style>
  `
  
  // 获取打印内容
  const printContent = printRef.value.innerHTML
  
  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>样品标签打印</title>
      ${printStyles}
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `)
  
  printWindow.document.close()
  
  // 等待内容加载完成后打印
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 500)
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      generateAllQRCodes()
    })
  }
})
</script>

<style scoped>
.print-preview-container {
  max-height: 70vh;
  overflow-y: auto;
}

.print-preview {
  background: #f5f5f5;
  padding: 20px;
}

.label-container {
  width: 300px; /* 75mm * 4 */
  height: 240px; /* 60mm * 4 */
  background: white;
  border: 2px solid #000;
  margin-bottom: 20px;
  padding: 8px;
  box-sizing: border-box;
  position: relative;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.label-content {
  display: flex;
  height: 180px;
}

.label-left {
  flex: 1;
  font-size: 12px;
  line-height: 1.4;
  padding-right: 10px;
}

.label-right {
  width: 80px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10px;
}

.label-row {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
}

.label-title {
  font-weight: bold;
  min-width: 64px;
  flex-shrink: 0;
  color: #333;
}

.label-value {
  flex: 1;
  word-break: break-all;
  color: #666;
}

.status-row {
  margin-top: 8px;
}

.status-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-item {
  font-size: 11px;
  white-space: nowrap;
  color: #666;
}

.checkbox {
  font-size: 12px;
  margin-right: 4px;
  color: #333;
}

.qrcode-container {
  border: 1px solid #ddd;
  padding: 4px;
  background: white;
}

.qrcode {
  width: 72px !important;
  height: 72px !important;
}

.label-footer {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  border-top: 1px solid #eee;
  padding-top: 4px;
}

.company-name {
  font-weight: bold;
  color: #333;
}

.page-number {
  font-weight: bold;
  color: #666;
}
</style>
