-- 为采样任务分组表添加分组编号字段
-- 创建时间: 2025-01-07
-- 描述: 为sampling_task_group表添加group_code字段，用于存储分组编号

-- 添加分组编号字段
ALTER TABLE sampling_task_group 
ADD COLUMN group_code VARCHAR(100) COMMENT '分组编号，格式：采样任务编号-分组ID';

-- 创建分组编号索引
CREATE INDEX idx_sampling_task_group_code ON sampling_task_group(group_code);

-- 为现有数据生成分组编号
-- 使用采样任务编号-分组ID的格式
UPDATE sampling_task_group stg
JOIN sampling_task st ON stg.sampling_task_id = st.id
SET stg.group_code = CONCAT(st.task_code, '-', stg.id)
WHERE stg.group_code IS NULL;

-- 添加唯一约束（确保分组编号唯一）
ALTER TABLE sampling_task_group 
ADD UNIQUE KEY uk_group_code (group_code);

-- 添加注释说明
ALTER TABLE sampling_task_group 
MODIFY COLUMN group_code VARCHAR(100) NOT NULL COMMENT '分组编号，格式：采样任务编号-分组ID';
