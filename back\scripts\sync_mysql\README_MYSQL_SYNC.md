# MySQL数据库同步工具使用说明

## 概述

MySQL数据库同步工具支持从源MySQL数据库同步所有数据到目标MySQL数据库。工具现在支持两种配置方式：
1. **配置文件方式**（推荐）：使用 `sync_config.py` 配置文件
2. **命令行参数方式**：通过命令行参数指定数据库连接信息

本工具提供了完整的MySQL数据库同步功能，可以将一个MySQL数据库的所有数据同步到另一个MySQL数据库。支持不同服务器之间的数据同步，同步前会自动清空目标数据库。

## 文件说明

- `mysql_sync.py` - 核心同步工具类，支持命令行参数
- `mysql_sync_simple.py` - 简化版本，使用配置文件
- `sync_config.py` - 数据库配置文件
- `README_MYSQL_SYNC.md` - 使用说明文档

## 功能特性

- ✅ 支持跨服务器数据库同步
- ✅ 自动清空目标数据库
- ✅ 完整的表结构同步
- ✅ 批量数据同步
- ✅ 详细的日志记录
- ✅ 错误处理和回滚
- ✅ 外键约束处理
- ✅ 进度显示
- ✅ **新增**：支持配置文件和命令行参数两种配置方式
- ✅ **新增**：默认读取配置文件，无需每次输入参数
- ✅ **新增**：智能依赖分析，自动分析表的外键依赖关系
- ✅ **新增**：拓扑排序算法，确保依赖表优先创建
- ✅ **新增**：循环依赖检测和处理

## 安装依赖

```bash
# 安装PyMySQL
pip install pymysql
```

## 使用方法

### 方法一：使用配置文件（推荐）

1. **修改配置文件**

   编辑 `sync_config.py` 文件，设置源数据库和目标数据库的连接信息：

   ```python
   # 源数据库配置
   SOURCE_DB_CONFIG = {
       'host': '*************',  # 源数据库主机地址
       'port': 3306,             # 源数据库端口
       'username': 'root',       # 源数据库用户名
       'password': 'password',   # 源数据库密码
       'database': 'source_db'   # 源数据库名
   }
   
   # 目标数据库配置
   TARGET_DB_CONFIG = {
       'host': '127.0.0.1',      # 目标数据库主机地址
       'port': 3306,             # 目标数据库端口
       'username': 'lims-user',  # 目标数据库用户名
       'password': 'lims-Root1', # 目标数据库密码
       'database': 'lims'        # 目标数据库名
   }
   ```

2. **运行同步脚本**

   **直接运行（推荐）**：
   ```bash
   cd /home/<USER>/workspace/lims2/back/scripts
   python mysql_sync.py
   ```
   
   **或使用简化脚本**：
   ```bash
   cd /home/<USER>/workspace/lims2/back/scripts
   python mysql_sync_simple.py
   ```
   
   **强制使用配置文件**：
   ```bash
   cd /home/<USER>/workspace/lims2/back/scripts
   python mysql_sync.py --use-config
   ```

### 方法二：使用命令行参数

```bash
cd /home/<USER>/workspace/lims2/back/scripts
python mysql_sync.py \
  --source-host ************* \
  --source-user root \
  --source-password password \
  --source-database source_db \
  --target-host 127.0.0.1 \
  --target-user lims-user \
  --target-password lims-Root1 \
  --target-database lims
```

### 配置优先级

1. **自动检测**：如果存在 `sync_config.py` 且未提供命令行参数，自动使用配置文件
2. **命令行优先**：如果提供了命令行参数，优先使用命令行参数
3. **强制配置文件**：使用 `--use-config` 参数强制使用配置文件

## 命令行参数说明

### 源数据库参数
- `--source-host`: 源数据库主机地址（必需）
- `--source-port`: 源数据库端口（默认：3306）
- `--source-user`: 源数据库用户名（必需）
- `--source-password`: 源数据库密码（必需）
- `--source-database`: 源数据库名（必需）

### 目标数据库参数
- `--target-host`: 目标数据库主机地址（必需）
- `--target-port`: 目标数据库端口（默认：3306）
- `--target-user`: 目标数据库用户名（必需）
- `--target-password`: 目标数据库密码（必需）
- `--target-database`: 目标数据库名（必需）

## 同步流程

1. **连接验证** - 验证源数据库和目标数据库连接
2. **清空目标库** - 删除目标数据库中的所有表
3. **获取表列表** - 从源数据库获取所有表名
4. **依赖分析** - 分析表的外键依赖关系
5. **拓扑排序** - 对表进行依赖排序，确保正确的创建顺序
6. **同步表结构** - 按依赖顺序在目标数据库中重建所有表
7. **同步数据** - 批量复制所有表的数据
8. **完成同步** - 显示同步结果和统计信息

## 日志文件

同步过程中的详细日志会保存到以下文件：
- `../logs/mysql_sync.log` - 完整版本的日志
- `../logs/mysql_sync_simple.log` - 简化版本的日志

## 注意事项

⚠️ **重要警告**
- 同步操作会**完全清空**目标数据库的所有数据
- 请确保目标数据库已备份重要数据
- 建议先在测试环境中验证同步效果
- 工具会自动分析和处理外键依赖，但复杂的循环依赖可能需要手动处理

### 权限要求

**源数据库用户需要的权限：**
- `SELECT` - 读取数据
- `SHOW VIEW` - 查看表结构

**目标数据库用户需要的权限：**
- `CREATE` - 创建表
- `DROP` - 删除表
- `INSERT` - 插入数据
- `DELETE` - 删除数据
- `ALTER` - 修改表结构

### 性能优化建议

1. **网络环境** - 确保源数据库和目标数据库之间网络稳定
2. **数据库配置** - 适当调整MySQL的缓冲区大小
3. **批量大小** - 根据数据量调整批量插入大小
4. **外键约束** - 同步期间会暂时禁用外键检查

## 故障排除

### 常见错误

1. **连接失败**
   - 检查数据库主机地址和端口
   - 验证用户名和密码
   - 确认数据库服务正在运行

2. **权限不足**
   - 确认用户具有必要的数据库权限
   - 检查防火墙设置

3. **数据类型不兼容**
   - 确保源数据库和目标数据库版本兼容
   - 检查字符集设置

4. **磁盘空间不足**
   - 确保目标服务器有足够的磁盘空间
   - 监控同步过程中的磁盘使用情况

5. **外键依赖问题**
   - 检查表结构是否存在外键依赖问题
   - 如果检测到循环依赖，工具会发出警告并尝试处理
   - 复杂的循环依赖可能需要手动调整表结构

### 调试模式

如需更详细的调试信息，可以修改日志级别：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 示例场景

### 场景1：生产环境到测试环境

```bash
# 将生产环境数据同步到测试环境
python mysql_sync.py \
  --source-host prod-db.company.com \
  --source-user readonly_user \
  --source-password prod_password \
  --source-database production_db \
  --target-host test-db.company.com \
  --target-user admin \
  --target-password test_password \
  --target-database test_db
```

### 场景2：数据库迁移

```bash
# 将旧服务器数据迁移到新服务器
python mysql_sync.py \
  --source-host old-server.com \
  --source-user root \
  --source-password old_password \
  --source-database legacy_db \
  --target-host new-server.com \
  --target-user root \
  --target-password new_password \
  --target-database new_db
```

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接配置
3. 用户权限设置
4. 网络连接状态

更多技术支持，请联系系统管理员。