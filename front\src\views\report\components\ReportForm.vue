<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body>
    <el-form :model="form" :rules="rules" ref="reportRef" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="报告日期" prop="reportDate">
            <el-date-picker
              v-model="form.reportDate"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="reportType === 'weekly'">
          <el-form-item label="下周工作饱和度" prop="isSaturated">
            <el-radio-group v-model="form.isSaturated">
              <el-radio value="1">饱和</el-radio>
              <el-radio value="0">不饱和</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item :label="summaryLabel" prop="summary">
        <el-input
          v-model="form.summary"
          type="textarea"
          :rows="4"
          :placeholder="`请输入${summaryLabel}`"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item :label="planLabel" prop="plan">
        <el-input
          v-model="form.plan"
          type="textarea"
          :rows="4"
          :placeholder="`请输入${planLabel}`"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item :label="problemsLabel" prop="problems">
        <el-input
          v-model="form.problems"
          type="textarea"
          :rows="3"
          :placeholder="`请输入${problemsLabel}`"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="需要的支持" prop="supportNeeded">
        <el-input
          v-model="form.supportNeeded"
          type="textarea"
          :rows="3"
          placeholder="请输入需要的支持"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  open: {
    type: Boolean,
    default: false
  },
  reportType: {
    type: String,
    required: true,
    validator: (value) => ['weekly', 'monthly'].includes(value)
  }
})

const emit = defineEmits(["cancel", "submit"])
const { proxy } = getCurrentInstance()

const dialogVisible = computed({
  get: () => props.open,
  set: (val) => {
    if (!val) {
      emit("cancel")
    }
  }
})

// 根据报告类型动态设置标签
const summaryLabel = computed(() => {
  return props.reportType === 'weekly' ? '本周总结' : '本月总结'
})

const planLabel = computed(() => {
  return props.reportType === 'weekly' ? '下周计划' : '下月计划'
})

const problemsLabel = computed(() => {
  return props.reportType === 'weekly' ? '本周存在的问题' : '本月存在的问题'
})

const defaultForm = {
  reportId: null,
  reportType: props.reportType,
  reportDate: null,
  summary: null,
  plan: null,
  problems: null,
  supportNeeded: null,
  isSaturated: null,
  remark: null
}

const data = reactive({
  form: { ...defaultForm },
  rules: {
    reportDate: [
      { required: true, message: "报告日期不能为空", trigger: "blur" }
    ],
    summary: [
      { required: true, message: `${summaryLabel.value}不能为空`, trigger: "blur" },
      { max: 2000, message: `${summaryLabel.value}长度不能超过2000个字符`, trigger: "blur" }
    ],
    plan: [
      { required: true, message: `${planLabel.value}不能为空`, trigger: "blur" },
      { max: 2000, message: `${planLabel.value}长度不能超过2000个字符`, trigger: "blur" }
    ],
    problems: [
      { max: 2000, message: `${problemsLabel.value}长度不能超过2000个字符`, trigger: "blur" }
    ],
    supportNeeded: [
      { max: 2000, message: "需要的支持长度不能超过2000个字符", trigger: "blur" }
    ],
    remark: [
      { max: 500, message: "备注长度不能超过500个字符", trigger: "blur" }
    ]
  }
})

const { form, rules } = toRefs(data)

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reportRef"].validate(valid => {
    if (valid) {
      // 设置报告类型
      form.value.reportType = props.reportType
      // 如果是月报，清空饱和度字段
      if (props.reportType === 'monthly') {
        form.value.isSaturated = null
      }
      emit("submit", form.value)
    }
  })
}

/** 取消按钮 */
function cancel() {
  emit("cancel")
}

/** 表单重置 */
function reset() {
  form.value = { ...defaultForm }
  form.value.reportType = props.reportType
  proxy.resetForm("reportRef")
}

/** 设置表单数据 */
function setFormData(data) {
  form.value = { ...defaultForm, ...data }
  form.value.reportType = props.reportType
}

// 暴露方法给父组件
defineExpose({
  reset,
  setFormData
})
</script>
