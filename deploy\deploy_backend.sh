#!/bin/bash
# 后端部署脚本
# 作用：配置后端环境，安装依赖，配置supervisor

set -e

echo "===== 开始部署后端 ====="

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
read -p "应用部署路径 [默认: $PROJECT_ROOT]: " APP_PATH
APP_PATH=${APP_PATH:-$PROJECT_ROOT}

read -p "后端目录名称 [默认: back]: " BACKEND_DIR
BACKEND_DIR=${BACKEND_DIR:-back}
BACKEND_PATH="$APP_PATH/$BACKEND_DIR"

read -p "环境配置 [默认: prod]: " ENV
ENV=${ENV:-prod}

read -p "应用端口 [默认: 9099]: " APP_PORT
APP_PORT=${APP_PORT:-9099}

read -p "是否创建虚拟环境? (y/n) [默认: y]: " CREATE_VENV
CREATE_VENV=${CREATE_VENV:-y}

# 确保后端目录存在
if [ ! -d "$BACKEND_PATH" ]; then
    echo "错误: 后端目录 $BACKEND_PATH 不存在"
    exit 1
fi

# 进入后端目录
cd "$BACKEND_PATH"
echo "当前工作目录: $(pwd)"

# 创建并激活虚拟环境
if [ "$CREATE_VENV" = "y" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "虚拟环境已激活"
fi

# 安装依赖
echo "安装Python依赖..."
pip install -r requirements.txt

# 检查并创建.env.prod文件
ENV_FILE=".env.$ENV"
if [ ! -f "$ENV_FILE" ]; then
    echo "警告: $ENV_FILE 文件不存在，将创建默认配置"
    
    # 获取数据库配置
    read -p "数据库类型 (mysql/postgresql) [默认: mysql]: " DB_TYPE
    DB_TYPE=${DB_TYPE:-mysql}
    
    read -p "数据库主机 [默认: 127.0.0.1]: " DB_HOST
    DB_HOST=${DB_HOST:-127.0.0.1}
    
    read -p "数据库端口 [默认: 3306]: " DB_PORT
    DB_PORT=${DB_PORT:-3306}
    
    read -p "数据库用户名 [默认: lims-user]: " DB_USERNAME
    DB_USERNAME=${DB_USERNAME:-lims-user}
    
    read -p "数据库密码 [默认: lims-Root1]: " DB_PASSWORD
    DB_PASSWORD=${DB_PASSWORD:-lims-Root1}
    
    read -p "数据库名称 [默认: lims]: " DB_DATABASE
    DB_DATABASE=${DB_DATABASE:-lims}
    
    # 获取Redis配置
    read -p "Redis主机 [默认: 127.0.0.1]: " REDIS_HOST
    REDIS_HOST=${REDIS_HOST:-127.0.0.1}
    
    read -p "Redis端口 [默认: 6379]: " REDIS_PORT
    REDIS_PORT=${REDIS_PORT:-6379}
    
    read -p "Redis密码 [默认: 空]: " REDIS_PASSWORD
    REDIS_PASSWORD=${REDIS_PASSWORD:-}
    
    # 创建配置文件
    cat > "$ENV_FILE" << EOF
# -------- 应用配置 --------
# 应用运行环境
APP_ENV = '$ENV'
# 应用名称
APP_NAME = 'RuoYi-FastAPI'
# 应用代理路径
APP_ROOT_PATH = '/$ENV-api'
# 应用主机
APP_HOST = '0.0.0.0'
# 应用端口
APP_PORT = $APP_PORT
# 应用版本
APP_VERSION= '1.6.2'
# 应用是否开启热重载
APP_RELOAD = false
# 应用是否开启IP归属区域查询
APP_IP_LOCATION_QUERY = true
# 应用是否允许账号同时登录
APP_SAME_TIME_LOGIN = true

# -------- Jwt配置 --------
# Jwt秘钥
JWT_SECRET_KEY = 'b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
# Jwt算法
JWT_ALGORITHM = 'HS256'
# 令牌过期时间
JWT_EXPIRE_MINUTES = 1440
# redis中令牌过期时间
JWT_REDIS_EXPIRE_MINUTES = 30

# -------- 数据库配置 --------
# 数据库类型，可选的有'mysql'、'postgresql'，默认为'mysql'
DB_TYPE = '$DB_TYPE'
# 数据库主机
DB_HOST = '$DB_HOST'
# 数据库端口
DB_PORT = $DB_PORT
# 数据库用户名
DB_USERNAME = '$DB_USERNAME'
# 数据库密码
DB_PASSWORD = '$DB_PASSWORD'
# 数据库名称
DB_DATABASE = '$DB_DATABASE'
# 是否开启sqlalchemy日志
DB_ECHO = true
# 允许溢出连接池大小的最大连接数
DB_MAX_OVERFLOW = 10
# 连接池大小，0表示连接数无限制
DB_POOL_SIZE = 50
# 连接回收时间（单位：秒）
DB_POOL_RECYCLE = 3600
# 连接池中没有线程可用时，最多等待的时间（单位：秒）
DB_POOL_TIMEOUT = 30

# -------- Redis配置 --------
# Redis主机
REDIS_HOST = '$REDIS_HOST'
# Redis端口
REDIS_PORT = $REDIS_PORT
# Redis用户名
REDIS_USERNAME = ''
# Redis密码
REDIS_PASSWORD = '$REDIS_PASSWORD'
# Redis数据库
REDIS_DATABASE = 2
EOF
    echo "$ENV_FILE 文件已创建"
fi

# 运行数据库迁移
read -p "是否运行数据库迁移? (y/n) [默认: y]: " RUN_MIGRATION
RUN_MIGRATION=${RUN_MIGRATION:-y}

if [ "$RUN_MIGRATION" = "y" ]; then
    echo "运行数据库迁移..."
    python scripts/generate_migration.py generate -m "deployment migration"
    python scripts/generate_migration.py apply
    echo "数据库迁移完成"
fi

# 创建supervisor配置文件
SUPERVISOR_CONF_DIR="/etc/supervisor/conf.d"
if [ ! -d "$SUPERVISOR_CONF_DIR" ]; then
    SUPERVISOR_CONF_DIR="/etc/supervisord.d"
    if [ ! -d "$SUPERVISOR_CONF_DIR" ]; then
        echo "错误: 找不到supervisor配置目录"
        exit 1
    fi
fi

# 生成supervisor配置
SUPERVISOR_CONF="$SCRIPT_DIR/lims-backend.conf"
cat > "$SUPERVISOR_CONF" << EOF
[program:lims-backend]
directory=$BACKEND_PATH
command=$BACKEND_PATH/venv/bin/python app.py --env=$ENV
autostart=true
autorestart=true
stderr_logfile=/var/log/lims/backend.err.log
stdout_logfile=/var/log/lims/backend.out.log
user=$(whoami)
environment=PATH="$BACKEND_PATH/venv/bin:%(ENV_PATH)s"
EOF

echo "Supervisor配置文件已生成: $SUPERVISOR_CONF"
echo "请以root权限执行以下命令安装supervisor配置:"
echo "sudo cp $SUPERVISOR_CONF $SUPERVISOR_CONF_DIR/"
echo "sudo supervisorctl reread"
echo "sudo supervisorctl update"
echo "sudo supervisorctl start lims-backend"

echo "===== 后端部署完成 ====="
