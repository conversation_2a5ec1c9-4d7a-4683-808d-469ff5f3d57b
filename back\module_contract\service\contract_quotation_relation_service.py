"""
合同关联报价单服务
"""

from typing import List, Optional
from datetime import datetime
from sqlalchemy import and_, delete
from sqlalchemy.orm import Session

from config.database import get_db
from module_contract.entity.do.contract_quotation_relation_do import ContractQuotationRelation
from module_contract.entity.vo.contract_quotation_relation_vo import (
    ContractQuotationRelationModel,
    ContractQuotationRelationDetailModel,
    ContractQuotationRelationCreateModel,
    ContractQuotationRelationUpdateModel,
    ContractQuotationRelationListModel
)
from module_quotation.service.project_quotation_service import ProjectQuotationService
from utils.current_user import CurrentUserModel
from utils.camel_case_util import CamelCaseUtil


class ContractQuotationRelationService:
    """
    合同关联报价单服务类
    """

    def __init__(self):
        self.project_quotation_service = ProjectQuotationService()

    def get_contract_quotation_relations(self, contract_id: int) -> ContractQuotationRelationListModel:
        """
        查询合同关联的报价单列表
        
        Args:
            contract_id: 合同ID
            
        Returns:
            ContractQuotationRelationListModel: 关联报价单列表
        """
        db: Session = next(get_db())
        try:
            # 查询关联记录
            relations = db.query(ContractQuotationRelation).filter(
                ContractQuotationRelation.contract_id == contract_id
            ).all()

            detail_list = []
            for relation in relations:
                # 获取项目报价详情
                try:
                    project_detail = self.project_quotation_service.get_project_quotation_by_code(
                        relation.project_code
                    )
                    
                    detail = ContractQuotationRelationDetailModel(
                        id=relation.id,
                        contract_id=relation.contract_id,
                        project_code=relation.project_code,
                        project_name=project_detail.get('projectName') if project_detail else None,
                        customer_name=project_detail.get('customerName') if project_detail else None,
                        project_manager=project_detail.get('projectManager') if project_detail else None,
                        status=project_detail.get('status') if project_detail else None,
                        create_time=relation.create_time
                    )
                    detail_list.append(detail)
                except Exception as e:
                    # 如果获取项目详情失败，仍然返回基本信息
                    detail = ContractQuotationRelationDetailModel(
                        id=relation.id,
                        contract_id=relation.contract_id,
                        project_code=relation.project_code,
                        project_name=None,
                        customer_name=None,
                        project_manager=None,
                        status=None,
                        create_time=relation.create_time
                    )
                    detail_list.append(detail)

            # 转换为驼峰命名
            result_list = []
            for detail in detail_list:
                detail_dict = detail.model_dump()
                camel_dict = CamelCaseUtil.snake_to_camel_dict(detail_dict)
                result_list.append(camel_dict)

            return ContractQuotationRelationListModel(
                total=len(result_list),
                rows=result_list
            )

        finally:
            db.close()

    def create_contract_quotation_relations(
        self, 
        contract_id: int, 
        create_model: ContractQuotationRelationCreateModel,
        current_user: CurrentUserModel
    ) -> bool:
        """
        创建合同关联报价单
        
        Args:
            contract_id: 合同ID
            create_model: 创建模型
            current_user: 当前用户
            
        Returns:
            bool: 创建结果
        """
        db: Session = next(get_db())
        try:
            # 先删除现有关联
            db.execute(
                delete(ContractQuotationRelation).where(
                    ContractQuotationRelation.contract_id == contract_id
                )
            )

            # 创建新的关联记录
            for project_code in create_model.project_codes:
                relation = ContractQuotationRelation(
                    contract_id=contract_id,
                    project_code=project_code,
                    create_by=current_user.user_name,
                    create_time=datetime.now()
                )
                db.add(relation)

            db.commit()
            return True

        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def update_contract_quotation_relations(
        self,
        contract_id: int,
        update_model: ContractQuotationRelationUpdateModel,
        current_user: CurrentUserModel
    ) -> bool:
        """
        更新合同关联报价单

        Args:
            contract_id: 合同ID
            update_model: 更新模型
            current_user: 当前用户

        Returns:
            bool: 更新结果
        """
        result = self.create_contract_quotation_relations(contract_id, update_model, current_user)

        # 更新成功后，触发报价单总金额重新计算
        if result:
            try:
                # 使用异步方式触发计算（这里可以考虑使用后台任务）
                import asyncio
                from config.database import get_async_db
                from module_contract.service.contract_service import ContractService

                async def update_quotation_total():
                    async_db = get_async_db()
                    async with async_db() as db:
                        contract_service = ContractService(db)
                        await contract_service.calculate_and_update_quotation_total_amount(contract_id)

                # 在新的事件循环中运行异步任务
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果当前有运行的事件循环，创建一个任务
                        asyncio.create_task(update_quotation_total())
                    else:
                        # 如果没有运行的事件循环，直接运行
                        asyncio.run(update_quotation_total())
                except Exception as e:
                    print(f"更新报价单总金额失败: {str(e)}")

            except Exception as e:
                print(f"触发报价单总金额计算失败: {str(e)}")

        return result

    def delete_contract_quotation_relations(self, contract_id: int) -> bool:
        """
        删除合同的所有关联报价单
        
        Args:
            contract_id: 合同ID
            
        Returns:
            bool: 删除结果
        """
        db: Session = next(get_db())
        try:
            db.execute(
                delete(ContractQuotationRelation).where(
                    ContractQuotationRelation.contract_id == contract_id
                )
            )
            db.commit()
            return True

        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()
