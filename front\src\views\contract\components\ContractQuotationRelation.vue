<template>
  <div class="contract-quotation-relation">
    <!-- 操作按钮 -->
    <div style="margin-bottom: 20px;">
      <el-button type="primary" icon="Plus" @click="handleAdd">新增关联项目报价</el-button>
      <el-button 
        type="danger" 
        icon="Delete" 
        :disabled="selectedRelations.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="relationList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      empty-text="暂无关联的项目报价"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" min-width="120" show-overflow-tooltip />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" show-overflow-tooltip />
      <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" show-overflow-tooltip />
      <el-table-column label="项目负责人" align="center" prop="projectManager" min-width="100" show-overflow-tooltip />
      <el-table-column label="项目状态" align="center" prop="status" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status" :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150" />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 项目报价选择弹框 -->
    <ProjectQuotationSelector
      v-model="selectorVisible"
      :exclude-project-codes="excludeProjectCodes"
      @confirm="handleSelectorConfirm"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ProjectQuotationSelector from '@/components/ProjectQuotationSelector/index.vue'
import {
  listContractQuotationRelation,
  addContractQuotationRelation,
  updateContractQuotationRelation
} from '@/api/contract/contractQuotationRelation'

const props = defineProps({
  contractId: {
    type: Number,
    required: true
  }
})

// 数据
const loading = ref(false)
const relationList = ref([])
const selectedRelations = ref([])
const selectorVisible = ref(false)

// 计算已关联的项目编号，用于排除
const excludeProjectCodes = computed(() => {
  return relationList.value.map(item => item.projectCode)
})

// 监听合同ID变化
watch(() => props.contractId, (newVal) => {
  if (newVal) {
    getList()
  }
}, { immediate: true })

/** 查询关联报价单列表 */
const getList = async () => {
  if (!props.contractId) return
  
  loading.value = true
  try {
    const response = await listContractQuotationRelation(props.contractId)
    if (response.code === 200) {
      relationList.value = response.data.rows || []
    }
  } catch (error) {
    console.error('查询合同关联报价单失败:', error)
    ElMessage.error('查询关联报价单失败')
  } finally {
    loading.value = false
  }
}

/** 新增关联项目报价 */
const handleAdd = () => {
  selectorVisible.value = true
}

/** 选择器确认 */
const handleSelectorConfirm = async (selectedProjects) => {
  try {
    const projectCodes = selectedProjects.map(item => item.projectCode)
    
    // 合并现有的项目编号和新选择的项目编号
    const allProjectCodes = [...excludeProjectCodes.value, ...projectCodes]
    
    const data = {
      projectCodes: allProjectCodes
    }
    
    await updateContractQuotationRelation(props.contractId, data)
    ElMessage.success('关联项目报价成功')
    getList()
  } catch (error) {
    console.error('关联项目报价失败:', error)
    ElMessage.error('关联项目报价失败')
  }
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  selectedRelations.value = selection
}

/** 删除单个关联 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该关联项目报价吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 从当前列表中移除该项目编号
    const remainingProjectCodes = relationList.value
      .filter(item => item.projectCode !== row.projectCode)
      .map(item => item.projectCode)
    
    const data = {
      projectCodes: remainingProjectCodes
    }
    
    await updateContractQuotationRelation(props.contractId, data)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除关联项目报价失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 批量删除 */
const handleBatchDelete = async () => {
  if (selectedRelations.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedRelations.value.length} 个关联项目报价吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 获取要删除的项目编号
    const deleteProjectCodes = selectedRelations.value.map(item => item.projectCode)
    
    // 从当前列表中移除选中的项目编号
    const remainingProjectCodes = relationList.value
      .filter(item => !deleteProjectCodes.includes(item.projectCode))
      .map(item => item.projectCode)
    
    const data = {
      projectCodes: remainingProjectCodes
    }
    
    await updateContractQuotationRelation(props.contractId, data)
    ElMessage.success('批量删除成功')
    selectedRelations.value = []
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除关联项目报价失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

/** 获取状态类型 */
const getStatusType = (status) => {
  switch (status) {
    case '草稿':
      return 'info'
    case '待审核':
      return 'warning'
    case '审核中':
      return 'warning'
    case '已审核':
      return 'success'
    case '已完成':
      return 'success'
    case '已取消':
      return 'danger'
    default:
      return 'info'
  }
}
</script>

<style scoped>
.contract-quotation-relation {
  padding: 0;
}
</style>
