"""
合同管理VO模型
"""

from typing import List, Optional
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, field_validator
from module_contract.entity.vo.contract_business_vo import (
    ContractBusinessDepartmentModel,
    ContractBusinessTaskModel,
    ContractBusinessInfoModel,
)
from pydantic.alias_generators import to_camel


class ContractModel(BaseModel):
    """合同基础模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(None, description="主键ID")

    # 基本信息
    contract_name: str = Field(..., description="合同名称", max_length=500)
    contract_number: Optional[str] = Field(None, description="合同编号", max_length=100)
    external_contract_number: Optional[str] = Field(None, description="外部合同编号", max_length=100)
    business_type: str = Field(..., description="业务类型", max_length=50)
    region_province: str = Field(..., description="省份", max_length=50)
    region_city: str = Field(..., description="城市", max_length=50)
    client_name: str = Field(..., description="委托单位", max_length=200)
    client_contact: str = Field(..., description="委托单位联系人", max_length=100)
    acquisition_method: str = Field(..., description="取得方式", max_length=50)
    project_manager_ids: List[int] = Field(..., description="项目负责人ID数组")
    project_service_id: int = Field(..., description="项目客服ID")
    dept_id: Optional[int] = Field(None, description="所属部门ID")
    contract_sign_date: Optional[date] = Field(None, description="合同签订日期")
    amount_type: str = Field(..., description="金额类型", max_length=20)
    contract_amount: Decimal = Field(..., description="合同金额")
    quotation_total_amount: Optional[Decimal] = Field(None, description="报价单总金额")
    changed_contract_amount: Optional[Decimal] = Field(None, description="变更后合同金额")
    outsourcing_amount: Optional[Decimal] = Field(None, description="外协金额")
    outsourcing_company: Optional[str] = Field(None, description="外协单位", max_length=200)
    completion_time: Optional[datetime] = Field(None, description="完工时间")
    contract_status: Optional[str] = Field(None, description="合同状态", max_length=50)
    remark: Optional[str] = Field(None, description="备注")

    @field_validator("business_type")
    @classmethod
    def validate_business_type(cls, v):
        """验证业务类型"""
        valid_types = [
            "一般委托",
            "年度",
            "验收",
            "送样",
            "监督监测",
            "应急监测",
            "环评检测",
            "场调检测",
            "地下水专项",
            "生态调查",
            "辐射",
            "油气回收",
            "LADR",
            "咨询服务",
            "数字化服务",
            "监理",
            "其他技术服务",
            "其他",
            "预留1",
            "预留2",
            "预留3",
        ]
        if v not in valid_types:
            raise ValueError(f"业务类型必须为以下值之一: {', '.join(valid_types)}")
        return v

    @field_validator("acquisition_method")
    @classmethod
    def validate_acquisition_method(cls, v):
        """验证取得方式"""
        valid_methods = ["商务谈判", "公开招标", "邀标", "其他"]
        if v not in valid_methods:
            raise ValueError(f"取得方式必须为以下值之一: {', '.join(valid_methods)}")
        return v

    @field_validator("amount_type")
    @classmethod
    def validate_amount_type(cls, v):
        """验证金额类型"""
        valid_types = ["固定金额", "非固定金额"]
        if v not in valid_types:
            raise ValueError(f"金额类型必须为以下值之一: {', '.join(valid_types)}")
        return v


class AddContractModel(BaseModel):
    """添加合同模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    # 基本信息
    contract_name: str = Field(..., description="合同名称", max_length=500)
    contract_number: Optional[str] = Field(None, description="合同编号", max_length=100)
    external_contract_number: Optional[str] = Field(None, description="外部合同编号", max_length=100)
    business_type: str = Field(..., description="业务类型", max_length=50)
    region_province: str = Field(..., description="省份", max_length=50)
    region_city: str = Field(..., description="城市", max_length=50)
    client_name: str = Field(..., description="委托单位", max_length=200)
    client_contact: str = Field(..., description="委托单位联系人", max_length=100)
    acquisition_method: str = Field(..., description="取得方式", max_length=50)
    project_manager_ids: List[int] = Field(..., description="项目负责人ID数组")
    project_service_id: int = Field(..., description="项目客服ID")
    dept_id: Optional[int] = Field(None, description="所属部门ID")
    contract_sign_date: Optional[date] = Field(None, description="合同签订日期")
    amount_type: str = Field(..., description="金额类型", max_length=20)
    contract_amount: Decimal = Field(..., description="合同金额")
    quotation_total_amount: Optional[Decimal] = Field(None, description="报价单总金额")
    changed_contract_amount: Optional[Decimal] = Field(None, description="变更后合同金额")
    outsourcing_amount: Optional[Decimal] = Field(None, description="外协金额")
    outsourcing_company: Optional[str] = Field(None, description="外协单位", max_length=200)
    completion_time: Optional[date] = Field(None, description="完工时间")
    contract_status: Optional[str] = Field(None, description="合同状态", max_length=50)
    remark: Optional[str] = Field(None, description="备注")
    business_info: Optional[ContractBusinessInfoModel] = Field(None, description="商务信息")

    @field_validator("business_type")
    @classmethod
    def validate_business_type(cls, v):
        """验证业务类型"""
        valid_types = [
            "一般委托",
            "年度",
            "验收",
            "送样",
            "监督监测",
            "应急监测",
            "环评检测",
            "场调检测",
            "地下水专项",
            "生态调查",
            "辐射",
            "油气回收",
            "LADR",
            "咨询服务",
            "数字化服务",
            "监理",
            "其他技术服务",
            "其他",
            "预留1",
            "预留2",
            "预留3",
        ]
        if v not in valid_types:
            raise ValueError(f"业务类型必须为以下值之一: {', '.join(valid_types)}")
        return v

    @field_validator("acquisition_method")
    @classmethod
    def validate_acquisition_method(cls, v):
        """验证取得方式"""
        valid_methods = ["商务谈判", "公开招标", "邀标", "其他"]
        if v not in valid_methods:
            raise ValueError(f"取得方式必须为以下值之一: {', '.join(valid_methods)}")
        return v

    @field_validator("amount_type")
    @classmethod
    def validate_amount_type(cls, v):
        """验证金额类型"""
        valid_types = ["固定金额", "非固定金额"]
        if v not in valid_types:
            raise ValueError(f"金额类型必须为以下值之一: {', '.join(valid_types)}")
        return v


class EditContractModel(BaseModel):
    """编辑合同模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int = Field(..., description="主键ID")

    # 基本信息
    contract_name: str = Field(..., description="合同名称", max_length=500)
    contract_number: Optional[str] = Field(None, description="合同编号", max_length=100)
    external_contract_number: Optional[str] = Field(None, description="外部合同编号", max_length=100)
    business_type: str = Field(..., description="业务类型", max_length=50)
    region_province: str = Field(..., description="省份", max_length=50)
    region_city: str = Field(..., description="城市", max_length=50)
    client_name: str = Field(..., description="委托单位", max_length=200)
    client_contact: str = Field(..., description="委托单位联系人", max_length=100)
    acquisition_method: str = Field(..., description="取得方式", max_length=50)
    project_manager_ids: List[int] = Field(..., description="项目负责人ID数组")
    project_service_id: int = Field(..., description="项目客服ID")
    dept_id: Optional[int] = Field(None, description="所属部门ID")
    contract_sign_date: Optional[date] = Field(None, description="合同签订日期")
    amount_type: str = Field(..., description="金额类型", max_length=20)
    contract_amount: Decimal = Field(..., description="合同金额")
    quotation_total_amount: Optional[Decimal] = Field(None, description="报价单总金额")
    changed_contract_amount: Optional[Decimal] = Field(None, description="变更后合同金额")
    outsourcing_amount: Optional[Decimal] = Field(None, description="外协金额")
    outsourcing_company: Optional[str] = Field(None, description="外协单位", max_length=200)
    completion_time: Optional[date] = Field(None, description="完工时间")
    contract_status: Optional[str] = Field(None, description="合同状态", max_length=50)
    remark: Optional[str] = Field(None, description="备注")
    business_info: Optional[ContractBusinessInfoModel] = Field(None, description="商务信息")

    @field_validator("business_type")
    @classmethod
    def validate_business_type(cls, v):
        """验证业务类型"""
        valid_types = [
            "一般委托",
            "年度",
            "验收",
            "送样",
            "监督监测",
            "应急监测",
            "环评检测",
            "场调检测",
            "地下水专项",
            "生态调查",
            "辐射",
            "油气回收",
            "LADR",
            "咨询服务",
            "数字化服务",
            "监理",
            "其他技术服务",
            "其他",
            "预留1",
            "预留2",
            "预留3",
        ]
        if v not in valid_types:
            raise ValueError(f"业务类型必须为以下值之一: {', '.join(valid_types)}")
        return v

    @field_validator("acquisition_method")
    @classmethod
    def validate_acquisition_method(cls, v):
        """验证取得方式"""
        valid_methods = ["商务谈判", "公开招标", "邀标", "其他"]
        if v not in valid_methods:
            raise ValueError(f"取得方式必须为以下值之一: {', '.join(valid_methods)}")
        return v

    @field_validator("amount_type")
    @classmethod
    def validate_amount_type(cls, v):
        """验证金额类型"""
        valid_types = ["固定金额", "非固定金额"]
        if v not in valid_types:
            raise ValueError(f"金额类型必须为以下值之一: {', '.join(valid_types)}")
        return v


class ContractQueryModel(BaseModel):
    """合同查询模型"""

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    page_num: Optional[int] = Field(default=1, description="页码")
    page_size: Optional[int] = Field(default=10, description="每页数量")
    contract_name: Optional[str] = Field(None, description="合同名称")
    contract_number: Optional[str] = Field(None, description="合同编号")
    business_type: Optional[str] = Field(None, description="业务类型")
    client_name: Optional[str] = Field(None, description="委托单位")
    project_manager: Optional[str] = Field(None, description="项目负责人")
    project_service: Optional[str] = Field(None, description="项目客服")
    contract_status: Optional[str] = Field(None, description="合同状态")


class ContractResponseModel(BaseModel):
    """合同响应模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int = Field(..., description="主键ID")

    # 基本信息
    contract_name: str = Field(..., description="合同名称")
    contract_number: Optional[str] = Field(None, description="合同编号")
    external_contract_number: Optional[str] = Field(None, description="外部合同编号")
    business_type: str = Field(..., description="业务类型")
    region_province: str = Field(..., description="省份")
    region_city: str = Field(..., description="城市")
    client_name: str = Field(..., description="委托单位")
    client_contact: str = Field(..., description="委托单位联系人")
    acquisition_method: str = Field(..., description="取得方式")
    project_manager_ids: List[int] = Field(..., description="项目负责人ID数组")
    project_manager_names: Optional[List[str]] = Field(None, description="项目负责人姓名数组")
    project_service_id: int = Field(..., description="项目客服ID")
    project_service_name: Optional[str] = Field(None, description="项目客服姓名")
    dept_id: Optional[int] = Field(None, description="所属部门ID")
    dept_name: Optional[str] = Field(None, description="所属部门名称")
    contract_sign_date: Optional[date] = Field(None, description="合同签订日期")
    amount_type: str = Field(..., description="金额类型")
    contract_amount: Decimal = Field(..., description="合同金额")
    quotation_total_amount: Optional[Decimal] = Field(None, description="报价单总金额")
    changed_contract_amount: Optional[Decimal] = Field(None, description="变更后合同金额")
    outsourcing_amount: Optional[Decimal] = Field(None, description="外协金额")
    outsourcing_company: Optional[str] = Field(None, description="外协单位")
    completion_time: Optional[datetime] = Field(None, description="完工时间")
    contract_status: Optional[str] = Field(None, description="合同状态")
    remark: Optional[str] = Field(None, description="备注")

    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[datetime] = Field(None, description="更新时间")
