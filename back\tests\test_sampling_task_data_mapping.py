"""
测试采样任务数据字段映射
"""
import asyncio


class TestSamplingTaskDataMapping:
    """测试采样任务数据字段映射"""

    async def test_cycle_items_field_mapping(self):
        """测试检测周期条目字段映射"""
        # 这是一个示例测试，实际使用时需要有真实的数据
        
        # 模拟检测周期条目数据
        mock_cycle_item = {
            'id': 1,
            'cycleNumber': 1,
            'category': '水质检测',  # 后端字段
            'parameter': 'pH值',    # 后端字段
            'method': '玻璃电极法',  # 后端字段
            'point_name': '采样点1', # 后端字段（下划线）
            'cycle_type': '月检',   # 后端字段（下划线）
            'sample_source': '地表水', # 后端字段（下划线）
            'status': 1,
            'statusLabel': '已分配'
        }
        
        # 模拟前端字段映射逻辑
        processed_item = {
            **mock_cycle_item,
            'detectionCategory': mock_cycle_item.get('detectionCategory') or mock_cycle_item.get('category'),
            'detectionParameter': mock_cycle_item.get('detectionParameter') or mock_cycle_item.get('parameter'),
            'detectionMethod': mock_cycle_item.get('detectionMethod') or mock_cycle_item.get('method'),
            'pointName': mock_cycle_item.get('pointName') or mock_cycle_item.get('point_name'),
            'cycleType': mock_cycle_item.get('cycleType') or mock_cycle_item.get('cycle_type'),
            'sampleSource': mock_cycle_item.get('sampleSource') or mock_cycle_item.get('sample_source')
        }
        
        # 验证字段映射
        assert processed_item['detectionCategory'] == '水质检测'
        assert processed_item['detectionParameter'] == 'pH值'
        assert processed_item['detectionMethod'] == '玻璃电极法'
        assert processed_item['pointName'] == '采样点1'
        assert processed_item['cycleType'] == '月检'
        assert processed_item['sampleSource'] == '地表水'
        
        print("✅ 字段映射测试通过")
        print(f"检测类别: {processed_item['detectionCategory']}")
        print(f"检测参数: {processed_item['detectionParameter']}")
        print(f"检测方法: {processed_item['detectionMethod']}")
        print(f"点位名称: {processed_item['pointName']}")
        print(f"周期类型: {processed_item['cycleType']}")
        print(f"样品来源: {processed_item['sampleSource']}")
    
    def test_grouping_logic(self):
        """测试分组逻辑"""
        # 模拟多个检测周期条目
        mock_cycle_items = [
            {
                'id': 1,
                'cycleNumber': 1,
                'category': '水质检测',
                'parameter': 'pH值',
                'method': '玻璃电极法',
                'point_name': '采样点1',
                'cycle_type': '月检',
                'sample_source': '地表水'
            },
            {
                'id': 2,
                'cycleNumber': 1,
                'category': '水质检测',
                'parameter': '溶解氧',
                'method': '电化学探头法',
                'point_name': '采样点1',
                'cycle_type': '月检',
                'sample_source': '地表水'
            },
            {
                'id': 3,
                'cycleNumber': 2,
                'category': '水质检测',
                'parameter': 'pH值',
                'method': '玻璃电极法',
                'point_name': '采样点1',
                'cycle_type': '月检',
                'sample_source': '地表水'
            }
        ]
        
        # 模拟分组逻辑
        group_map = {}
        for item in mock_cycle_items:
            # 字段映射
            processed_item = {
                **item,
                'detectionCategory': item.get('detectionCategory') or item.get('category'),
                'detectionParameter': item.get('detectionParameter') or item.get('parameter'),
                'detectionMethod': item.get('detectionMethod') or item.get('method'),
                'pointName': item.get('pointName') or item.get('point_name'),
                'cycleType': item.get('cycleType') or item.get('cycle_type'),
                'sampleSource': item.get('sampleSource') or item.get('sample_source')
            }
            
            group_key = f"{processed_item['cycleNumber']}-{processed_item['cycleType'] or ''}-{processed_item['detectionCategory'] or ''}-{processed_item['pointName'] or ''}"
            
            if group_key not in group_map:
                group_map[group_key] = {
                    'groupKey': group_key,
                    'cycleNumber': processed_item['cycleNumber'],
                    'cycleType': processed_item['cycleType'],
                    'detectionCategory': processed_item['detectionCategory'],
                    'pointName': processed_item['pointName'],
                    'items': []
                }
            
            group_map[group_key]['items'].append(processed_item)
        
        # 验证分组结果
        assert len(group_map) == 2  # 应该有2个分组
        
        # 第一个分组（周期1）应该有2个条目
        group1_key = "1-月检-水质检测-采样点1"
        assert group1_key in group_map
        assert len(group_map[group1_key]['items']) == 2
        
        # 第二个分组（周期2）应该有1个条目
        group2_key = "2-月检-水质检测-采样点1"
        assert group2_key in group_map
        assert len(group_map[group2_key]['items']) == 1
        
        print("✅ 分组逻辑测试通过")
        print(f"分组数量: {len(group_map)}")
        for key, group in group_map.items():
            print(f"分组 {key}: {len(group['items'])} 个条目")


if __name__ == "__main__":
    # 运行测试
    test = TestSamplingTaskDataMapping()
    
    # 运行同步测试
    test.test_grouping_logic()
    
    # 运行异步测试
    asyncio.run(test.test_cycle_items_field_mapping())
    
    print("\n🎉 所有测试通过！")
