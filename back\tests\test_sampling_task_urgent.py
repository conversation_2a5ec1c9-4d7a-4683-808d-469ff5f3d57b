#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务加急功能测试
"""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.dao.sampling_task_dao import Sampling<PERSON>askDao
from module_sampling.service.sampling_task_service import SamplingTaskService


class TestSamplingTaskUrgent:
    """采样任务加急功能测试类"""

    @pytest_asyncio.fixture
    async def test_task_id(self, db_session: AsyncSession):
        """创建测试任务并返回ID"""
        # 这里简化测试，假设数据库中已有任务ID为1的记录
        # 在实际环境中，你可能需要先创建测试数据
        return 1

    @pytest.mark.asyncio
    async def test_dao_urgent_operations(self, db_session: AsyncSession, test_task_id):
        """测试DAO层加急操作"""
        dao = SamplingTaskDao(db_session)
        
        try:
            # 测试设置加急
            await dao.set_task_urgent(test_task_id, True)
            task = await dao.get_sampling_task_by_id(test_task_id)
            if task:
                assert task.is_urgent is True
            
            # 测试取消加急
            await dao.set_task_urgent(test_task_id, False)
            task = await dao.get_sampling_task_by_id(test_task_id)
            if task:
                assert task.is_urgent is False
                
        except Exception as e:
            # 如果任务不存在，跳过测试
            pytest.skip(f"测试任务不存在: {e}")

    @pytest.mark.asyncio
    async def test_service_urgent_operations(self, db_session: AsyncSession, test_task_id):
        """测试Service层加急操作"""
        service = SamplingTaskService(db_session)
        
        try:
            # 测试设置加急
            result = await service.set_task_urgent(test_task_id, True)
            assert result["code"] == 200
            
            # 测试取消加急
            result = await service.set_task_urgent(test_task_id, False)
            assert result["code"] == 200
            
        except Exception as e:
            # 如果任务不存在，跳过测试
            pytest.skip(f"测试任务不存在: {e}")
        
        # 测试不存在的任务
        result = await service.set_task_urgent(99999, True)
        assert result["code"] == 500


if __name__ == "__main__":
    pytest.main(["-v", __file__])