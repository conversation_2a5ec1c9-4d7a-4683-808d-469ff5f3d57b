-- 修复项目报价审核人表的MySQL外键约束问题
-- 删除现有的project_quotation_approver表（如果存在）
DROP TABLE IF EXISTS project_quotation_approver;

-- 重新创建项目报价审核人关联表（MySQL版本）
CREATE TABLE project_quotation_approver (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_quotation_id INT NOT NULL COMMENT '项目报价ID',
    user_id INT NOT NULL COMMENT '审核人用户ID',
    create_by VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 添加外键约束
    CONSTRAINT fk_project_quotation_approver_quotation 
        FOREIGN KEY (project_quotation_id) REFERENCES project_quotation(id) ON DELETE CASCADE,
    CONSTRAINT fk_project_quotation_approver_user 
        FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    
    -- 添加唯一约束，防止重复添加同一个审核人
    UNIQUE KEY uk_quotation_user (project_quotation_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目报价审核人关联表';

-- 创建索引提高查询性能
CREATE INDEX idx_project_quotation_approver_quotation_id ON project_quotation_approver(project_quotation_id);
CREATE INDEX idx_project_quotation_approver_user_id ON project_quotation_approver(user_id);