-- 更新采样任务序列表格式
-- 1. 修改year_month字段从VARCHAR(6)改为VARCHAR(4)
-- 2. 修改id字段类型从BIGINT改为INT
-- 3. 更新现有数据格式从YYYYMM改为YYMM

-- 备份现有数据
CREATE TABLE sampling_task_sequence_backup AS SELECT * FROM sampling_task_sequence;

-- 更新现有数据格式：YYYYMM -> YYMM
UPDATE sampling_task_sequence 
SET year_month = SUBSTRING(year_month, 3, 2) || SUBSTRING(year_month, 5, 2)
WHERE LENGTH(year_month) = 6;

-- 修改表结构
ALTER TABLE sampling_task_sequence MODIFY COLUMN year_month VARCHAR(4) NOT NULL COMMENT '年月（YYMM）';
ALTER TABLE sampling_task_sequence MODIFY COLUMN id INT AUTO_INCREMENT NOT NULL COMMENT '主键ID';

-- 验证数据
SELECT COUNT(*) as total_records, 
       MIN(year_month) as min_year_month, 
       MAX(year_month) as max_year_month
FROM sampling_task_sequence;

-- 如果验证通过，可以删除备份表
-- DROP TABLE sampling_task_sequence_backup;