import re
from datetime import datetime
from typing import List, Dict

from fastapi import Request, Response
from sqlalchemy import and_, or_, select, distinct, update, func, delete
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.dao.technical_manual_category_dao import TechnicalManualCategoryDao
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_vo import (
    AddTechnicalManualModel,
    BatchInputTechnicalManualModel,
    BatchUpdateTechnicalManualModel,
    EditTechnicalManualModel,
    TechnicalManualPageQueryModel,
    TechnicalManualQueryModel,
)
from utils.common_util import CamelCaseUtil, SqlalchemyUtil
from utils.export_util import ExportUtil


class TechnicalManualService(BaseService[TechnicalManual]):
    """
    技术手册管理模块服务层
    """

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册服务

        :param db: 数据库会话
        """
        super().__init__(TechnicalManual, db)
        self.db = db
        self.category_dao = TechnicalManualCategoryDao(db)

    async def generate_test_code(self):
        """
        生成检测编号

        :return: 检测编号
        """
        # 查询最大的检测编号
        stmt = select(func.max(TechnicalManual.test_code)).where(TechnicalManual.test_code.like("JC%"))
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        # 如果没有检测编号，则从JC000001开始
        if not max_code:
            return "JC000001"

        # 提取数字部分并加1
        try:
            num = int(max_code[2:]) + 1
            return f"JC{num:06d}"
        except (ValueError, IndexError):
            # 如果解析失败，则从JC000001开始
            return "JC000001"

    async def get_or_create_category_code(
        self, classification: str, category: str, current_user: CurrentUserModel
    ) -> str:
        """
        获取或创建类目编号

        :param classification: 分类
        :param category: 检测类别
        :param current_user: 当前用户
        :return: 类目编号
        """
        # 先尝试获取现有的类目编号
        category_code = await self.category_dao.get_category_code_by_classification_and_category(
            classification, category
        )

        if category_code:
            return category_code

        # 如果不存在，则创建新的类目
        from module_basedata.entity.vo.technical_manual_category_vo import TechnicalManualCategoryModel

        category_data = TechnicalManualCategoryModel(
            classification=classification,
            category=category,
            create_by=current_user.user.user_name if current_user and current_user.user else "",
            create_time=datetime.now(),
            update_by=current_user.user.user_name if current_user and current_user.user else "",
            update_time=datetime.now(),
            remark="技术手册新增时自动创建",
        )

        new_category = await self.category_dao.create(category_data)
        return new_category.category_code

    async def create_categories_if_not_exists(
        self, classification: str, categories_str: str, current_user: CurrentUserModel
    ) -> list[str]:
        """
        根据分类和逗号分割的检测类别字符串，创建不存在的类目并返回类目编码列表

        :param classification: 分类
        :param categories_str: 检测类别字符串，逗号分割
        :param current_user: 当前用户
        :return: 类目编码列表
        """
        try:
            # 解析检测类别
            categories = [cat.strip() for cat in re.split(r"[,，]", categories_str) if cat and cat.strip()]

            if not categories:
                raise ValueError("检测类别不能为空")

            category_codes = []

            for category in categories:
                # 检查是否已存在
                existing_category = await self.category_dao.get_by_classification_and_category(classification, category)

                if existing_category:
                    # 已存在，直接使用
                    category_codes.append(existing_category.category_code)
                else:
                    # 不存在，创建新的类目
                    from module_basedata.entity.vo.technical_manual_category_vo import TechnicalManualCategoryModel

                    category_data = TechnicalManualCategoryModel(
                        classification=classification,
                        category=category,
                        category_code=await self.category_dao.get_next_category_code(),
                        create_by=current_user.user.user_name if current_user and current_user.user else "",
                        remark=f"技术手册新增时自动创建 - {classification}/{category}",
                    )

                    new_category = await self.category_dao.create(category_data)
                    category_codes.append(new_category.category_code)

            return category_codes

        except Exception as e:
            self.log_error(f"创建类目失败: {str(e)}", e)
            raise ServiceException(message=f"创建类目失败: {str(e)}")

    async def get_technical_manual_list(self, query_object: TechnicalManualQueryModel):
        """
        获取技术手册列表

        :param query_object: 查询参数对象
        :return: 技术手册列表
        """
        # 构建查询条件
        conditions = []

        # 如果有分类或检测类别查询条件，需要先获取对应的类目编号
        if query_object.classification or query_object.category:
            category_codes = await self.extract_category_codes(query_object.classification, query_object.category)
            # 如果有类目编号条件，添加到查询条件中
            if category_codes is not None:
                if category_codes:
                    # 使用JSON_CONTAINS查询类目编码数组中是否包含任一类目编码
                    category_conditions = []
                    for code in category_codes:
                        category_conditions.append(func.json_contains(TechnicalManual.category_codes, f'"{code}"'))
                    conditions.append(or_(*category_conditions))
                else:
                    # 没有匹配的类目，直接返回空结果
                    return []

        if query_object.parameter:
            if query_object.parameter in {",", "，"}:
                # 支持逗号和中文逗号分隔
                parameters = re.split(r"[,，]", query_object.parameter)
                conditions.append(TechnicalManual.parameter.in_(parameters))
            elif query_object.is_exact_query:
                conditions.append(TechnicalManual.parameter == query_object.parameter)
            else:
                conditions.append(TechnicalManual.parameter.like(f"%{query_object.parameter}%"))
        if query_object.method:
            if query_object.method in {",", "，"}:
                # 支持逗号和中文逗号分隔
                methods = re.split(r"[,，]", query_object.method)
                conditions.append(TechnicalManual.method.in_(methods))
            elif query_object.is_exact_query:
                conditions.append(TechnicalManual.method == query_object.method)
            else:
                conditions.append(TechnicalManual.method.like(f"%{query_object.method}%"))
        if query_object.qualificationCode:
            conditions.append(TechnicalManual.qualification_code == query_object.qualificationCode)
        if query_object.alias:
            # 支持别名JSON查询
            conditions.append(func.json_contains(TechnicalManual.alias_list, f'"{query_object.alias}"'))
        if query_object.begin_time and query_object.end_time:
            from datetime import time

            time_condition = and_(
                TechnicalManual.create_time
                >= datetime.combine(datetime.strptime(query_object.begin_time, "%Y-%m-%d"), time(00, 00, 00)),
                TechnicalManual.create_time
                <= datetime.combine(datetime.strptime(query_object.end_time, "%Y-%m-%d"), time(23, 59, 59)),
            )
            conditions.append(time_condition)

        # 先查询技术手册结果
        stmt = (
            select(TechnicalManual)
            .where(and_(*conditions))
            .order_by(
                TechnicalManual.parameter,
                TechnicalManual.method,
            )
        )
        result = await self.db.execute(stmt)
        manuals = result.scalars().all()

        # 组装结果
        technical_manuals = []
        for manual in manuals:
            manual_dict = SqlalchemyUtil.base_to_dict(manual)
            category_codes = manual.category_codes
            if category_codes:
                category_stmt = select(TechnicalManualCategory).where(
                    TechnicalManualCategory.category_code.in_(category_codes)
                )
                category_result = await self.db.execute(category_stmt)
                categories = category_result.scalars().all()
                if categories:
                    manual_dict["classification"] = categories[0].classification
                    manual_dict["category"] = ",".join([category.category for category in categories])
            technical_manuals.append(manual_dict)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manuals)

    async def get_technical_manual_page(self, query_object: TechnicalManualPageQueryModel):
        """
        获取技术手册分页列表

        :param query_object: 查询参数对象
        :return: 技术手册分页列表
        """
        from utils.page_util import PageUtil

        # 构建查询条件
        conditions = []

        # 如果有分类或检测类别查询条件，需要先获取对应的类目编号
        if query_object.classification or query_object.category:
            category_codes = await self.extract_category_codes(query_object.classification, query_object.category)

            # 如果有类目编号条件，添加到查询条件中
            if category_codes is not None:
                if category_codes:
                    # 使用JSON_CONTAINS查询类目编码数组中是否包含任一类目编码
                    category_conditions = []
                    for code in category_codes:
                        category_conditions.append(func.json_contains(TechnicalManual.category_codes, f'"{code}"'))
                    conditions.append(or_(*category_conditions))
                else:
                    # 没有匹配的类目，直接返回空结果
                    from utils.page_util import PageResponseModel

                    return PageResponseModel(
                        total=0, rows=[], page_num=query_object.page_num, page_size=query_object.page_size
                    )
        if query_object.parameter:
            if "," in query_object.parameter or "，" in query_object.parameter:
                # 支持逗号和中文逗号分隔
                parameters = re.split(r"[,，]", query_object.parameter)
                parameter_conditions = []
                for param in parameters:
                    parameter_conditions.append(
                        or_(TechnicalManual.parameter.like(f"%{param}%"), TechnicalManual.alias_list.like(f"%{param}%"))
                    )
                conditions.append(or_(*parameter_conditions))

            else:
                conditions.append(
                    or_(
                        TechnicalManual.parameter.like(f"%{query_object.parameter}%"),
                        TechnicalManual.alias_list.like(f"%{query_object.parameter}%"),
                    )
                )

        if query_object.method:
            if "," in query_object.method or "，" in query_object.method:
                # 支持逗号和中文逗号分隔
                methods = re.split(r"[,，]", query_object.method)
                conditions.append(TechnicalManual.method.in_(methods))
            else:
                conditions.append(TechnicalManual.method.like(f"%{query_object.method}%"))
        if query_object.qualificationCode:
            conditions.append(TechnicalManual.qualification_code == query_object.qualificationCode)
        if query_object.alias:
            # 支持别名JSON查询
            conditions.append(func.json_contains(TechnicalManual.alias_list, f'"{query_object.alias}"'))
        if query_object.begin_time and query_object.end_time:
            from datetime import time

            time_condition = and_(
                TechnicalManual.create_time
                >= datetime.combine(datetime.strptime(query_object.begin_time, "%Y-%m-%d"), time(00, 00, 00)),
                TechnicalManual.create_time
                <= datetime.combine(datetime.strptime(query_object.end_time, "%Y-%m-%d"), time(23, 59, 59)),
            )
            conditions.append(time_condition)

        # 执行关联查询
        stmt = (
            select(TechnicalManual)
            .where(and_(*conditions))
            .order_by(
                TechnicalManual.create_time.desc(),
                TechnicalManual.parameter,
                TechnicalManual.method,
            )
        )

        # 分页查询
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)
        # 组装结果，包含关联的分类和检测类别信息
        for manual_dict in page_result.rows:
            category_codes = manual_dict.get("categoryCodes") or manual_dict.get("category_codes")
            if category_codes:
                category_stmt = select(TechnicalManualCategory).where(
                    TechnicalManualCategory.category_code.in_(category_codes)
                )
                category_result = await self.db.execute(category_stmt)
                categories = category_result.scalars().all()
                if categories:
                    manual_dict["classification"] = categories[0].classification
                    manual_dict["category"] = ",".join([category.category for category in categories])
        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def extract_category_codes(self, classification: str, category: str) -> List[str]:
        category_codes = []
        if classification and category:
            # 精确匹配分类和检测类别
            category_code = await self.category_dao.get_category_code_by_classification_and_category(
                classification, category
            )
            if category_code:
                category_codes = [category_code]
        elif classification:
            # 只有分类，获取该分类下的所有检测类别
            # categories = await self.category_dao.get_categories_by_classification(classification)
            category_codes = await self.category_dao.get_category_codes_by_classification_and_categories(classification)
        elif category:
            if "," in category or "，" in category:
                # 支持逗号和中文逗号分隔
                categorys = re.split(r"[,，]", category)
                category_codes = await self.category_dao.get_classification_and_category_by_codes(categorys)
            else:
                category_codes = await self.category_dao.get_classification_and_category_by_codes([category])
        return category_codes

    async def get_technical_manual_detail(self, ids: List[int]) -> List[Dict]:
        """
        获取技术手册详情

        :param ids: 技术手册ID的列表
        :return: 技术手册详情
        """
        # 执行关联查询
        stmt = select(TechnicalManual).where(TechnicalManual.id.in_(ids))
        result = await self.db.execute(stmt)
        manuals = result.scalars().all()
        if not manuals:
            raise ServiceException(message=f"技术手册ID：{id}不存在")
        result = []
        for manual in manuals:
            # 组装结果，包含关联的分类和检测类别信息
            manual_dict = SqlalchemyUtil.base_to_dict(manual)
            if manual.category_codes:
                category_stmt = select(TechnicalManualCategory).where(
                    TechnicalManualCategory.category_code.in_(manual.category_codes)
                )
                category_result = await self.db.execute(category_stmt)
                categories = category_result.scalars().all()
                if categories:
                    manual_dict["classification"] = categories[0].classification
                    manual_dict["category"] = ",".join([category.category for category in categories])
            # 将下划线命名转换为驼峰命名
            result.append(CamelCaseUtil.transform_result(manual_dict))
        return result

    async def check_technical_manual_unique(self, category_code: str, parameter: str, method: str, id: int = None):
        """
        检查技术手册是否唯一

        :param category_code: 类目编号
        :param parameter: 检测参数
        :param method: 检测方法
        :param id: 技术手册ID
        :return: 是否唯一
        """
        conditions = [
            func.json_contains(TechnicalManual.category_codes, f'"{category_code}"'),
            TechnicalManual.parameter == parameter,
            TechnicalManual.method == method,
        ]

        if id:
            conditions.append(TechnicalManual.id != id)

        stmt = select(TechnicalManual).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def check_qualification_code_unique(self, qualification_code: str, id: int = None):
        """
        检查唯一编号是否唯一

        :param qualification_code: 唯一编号
        :param id: 技术手册ID
        :return: 是否唯一
        """
        if not qualification_code or qualification_code.strip() == "":
            return True

        conditions = [TechnicalManual.qualification_code == qualification_code.strip()]

        if id:
            conditions.append(TechnicalManual.id != id)

        stmt = select(TechnicalManual).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def add_technical_manual(
        self, _: Request, technical_manual_model: AddTechnicalManualModel, current_user: CurrentUserModel
    ):
        """
        新增技术手册

        :param request: 请求对象
        :param technical_manual_model: 新增技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 根据分类和检测类别创建类目并获取类目编码数组
            category_codes = await self.create_categories_if_not_exists(
                technical_manual_model.classification, technical_manual_model.category, current_user
            )
            if not category_codes:
                raise ServiceException(message="新增技术手册失败，创建类目编号失败")

            # 校验技术手册是否唯一
            for category_code in category_codes:
                if not await self.check_technical_manual_unique(
                    category_code, technical_manual_model.parameter, technical_manual_model.method
                ):
                    raise ServiceException(message=f"新增技术手册失败，该类目、参数和方法组合已存在")

            # 校验唯一编号是否唯一
            if not await self.check_qualification_code_unique(technical_manual_model.qualification_code):
                raise ServiceException(message=f"新增技术手册失败，唯一编号已存在")

            # 生成检测编号
            test_code = await self.generate_test_code()
            if not test_code:
                raise ServiceException(message="新增技术手册失败，生成检测编号失败")

            # 创建技术手册对象
            technical_manual = TechnicalManual(
                test_code=test_code,
                category_codes=category_codes,  # 使用类目编码数组
                parameter=technical_manual_model.parameter,
                method=technical_manual_model.method,
                description=technical_manual_model.description,
                # 新增字段
                qualification_code=(
                    technical_manual_model.qualification_code.strip()
                    if technical_manual_model.qualification_code
                    else None
                ),
                limitation_scope=technical_manual_model.limitation_scope,
                alias_list=technical_manual_model.alias_list,
                qualification_date=technical_manual_model.qualification_date,
                has_qualification=technical_manual_model.has_qualification,
                analysis_type=technical_manual_model.analysis_type,
                special_consumables_price=technical_manual_model.special_consumables_price,
                # 原有字段
                status=technical_manual_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=technical_manual_model.remark,
            )

            # 新增技术手册
            self.db.add(technical_manual)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="新增成功", result={"id": technical_manual.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加技术手册失败", e)
            raise

    async def edit_technical_manual(
        self, _: Request, technical_manual_model: EditTechnicalManualModel, current_user: CurrentUserModel
    ):
        """
        编辑技术手册

        :param request: 请求对象
        :param technical_manual_model: 编辑技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 校验技术手册是否存在
            stmt = select(TechnicalManual).where(TechnicalManual.id == technical_manual_model.id)
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()
            if not technical_manual:
                raise ServiceException(message=f"技术手册ID：{technical_manual_model.id}不存在")

            # 根据分类和检测类别创建类目并获取类目编码数组
            category_codes = await self.create_categories_if_not_exists(
                technical_manual_model.classification, technical_manual_model.category, current_user
            )
            if not category_codes:
                raise ServiceException(message="编辑技术手册失败，创建类目编号失败")

            # # 获取类目编号
            # category_code = category_codes or technical_manual_model.category_code
            # if not category_code:
            #     raise ServiceException(message="修改技术手册失败，类目编号不能为空")

            # 校验技术手册是否唯一
            for category_code in category_codes:
                if not await self.check_technical_manual_unique(
                    category_code=category_code,
                    parameter=technical_manual_model.parameter,
                    method=technical_manual_model.method,
                    id=technical_manual_model.id,
                ):
                    raise ServiceException(
                        message=f"修改技术手册失败，原因:该类目编码({category_code})、参数和方法组合已存在"
                    )

            # 校验唯一编号是否唯一
            if not await self.check_qualification_code_unique(
                technical_manual_model.qualification_code, technical_manual_model.id
            ):
                raise ServiceException(message=f"修改技术手册失败，唯一编号已存在")

            # 更新技术手册对象
            technical_manual.category_codes = category_codes
            technical_manual.parameter = technical_manual_model.parameter
            technical_manual.method = technical_manual_model.method
            technical_manual.description = technical_manual_model.description
            # 新增字段
            technical_manual.qualification_code = (
                technical_manual_model.qualification_code.strip() if technical_manual_model.qualification_code else None
            )
            technical_manual.limitation_scope = technical_manual_model.limitation_scope
            technical_manual.alias_list = technical_manual_model.alias_list
            technical_manual.qualification_date = technical_manual_model.qualification_date
            technical_manual.has_qualification = technical_manual_model.has_qualification
            technical_manual.analysis_type = technical_manual_model.analysis_type
            technical_manual.special_consumables_price = technical_manual_model.special_consumables_price
            # 原有字段
            technical_manual.status = technical_manual_model.status
            technical_manual.update_by = current_user.user.user_name if current_user and current_user.user else ""
            technical_manual.update_time = datetime.now()
            technical_manual.remark = technical_manual_model.remark

            # 更新技术手册
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="修改成功", result={"id": technical_manual.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改技术手册失败", e)
            raise

    async def delete_technical_manual(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册

        :param id: 技术手册ID
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 删除结果
        """
        try:
            # 校验技术手册是否存在
            stmt = delete(TechnicalManual).where(TechnicalManual.id == id)
            result = await self.db.execute(stmt)
            if result.rowcount == 0:
                raise ServiceException(message=f"技术手册ID：{id}不存在")

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="删除成功", result={"id": id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("删除技术手册失败", e)
            raise

    async def batch_update_technical_manual(
        self, _: Request, batch_update_model: BatchUpdateTechnicalManualModel, current_user: CurrentUserModel
    ):
        """
        批量更新技术手册

        :param request: 请求对象
        :param batch_update_model: 批量更新技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量更新结果
        """
        try:
            # 检查ID列表是否为空
            if not batch_update_model.ids:
                raise ServiceException(message="批量更新失败，ID列表不能为空")

            # 构建更新条件
            conditions = [TechnicalManual.id.in_(batch_update_model.ids)]

            # 构建更新值
            values = {}
            # 新增字段
            if batch_update_model.classification is not None:
                values["classification"] = batch_update_model.classification
            if batch_update_model.qualification_code is not None:
                values["qualification_code"] = (
                    batch_update_model.qualification_code.strip() if batch_update_model.qualification_code else None
                )
            if batch_update_model.limitation_scope is not None:
                values["limitation_scope"] = batch_update_model.limitation_scope
            if batch_update_model.common_alias is not None:
                values["common_alias"] = batch_update_model.common_alias
            if batch_update_model.alias_list is not None:
                values["alias_list"] = batch_update_model.alias_list
            if batch_update_model.qualification_date is not None:
                values["qualification_date"] = batch_update_model.qualification_date
            if batch_update_model.has_qualification is not None:
                values["has_qualification"] = batch_update_model.has_qualification
            if batch_update_model.analysis_type is not None:
                values["analysis_type"] = batch_update_model.analysis_type
            if batch_update_model.special_consumables_price is not None:
                values["special_consumables_price"] = batch_update_model.special_consumables_price
            # 原有字段
            if batch_update_model.status is not None:
                values["status"] = batch_update_model.status
            if batch_update_model.remark is not None:
                values["remark"] = batch_update_model.remark

            # 添加更新时间和更新人
            values["update_by"] = current_user.user.user_name if current_user and current_user.user else ""
            values["update_time"] = datetime.now()

            # 如果没有要更新的值，则返回错误
            if len(values) <= 2:  # 只有update_by和update_time
                raise ServiceException(message="批量更新失败，没有要更新的值")

            # 执行批量更新
            stmt = update(TechnicalManual).where(and_(*conditions)).values(**values)
            result = await self.db.execute(stmt)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message=f"批量更新成功，共更新{result.rowcount}条记录",
                result={"count": result.rowcount},
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量更新技术手册失败", e)
            raise

    async def batch_input_technical_manual(
        self, _: Request, batch_input_model: BatchInputTechnicalManualModel, current_user: CurrentUserModel
    ):
        """
        批量录入技术手册

        :param request: 请求对象
        :param batch_input_model: 批量录入技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量录入结果
        """
        try:
            # 解析输入数据
            categories = [cat.strip() for cat in batch_input_model.categories.split(",") if cat.strip()]
            parameters = [param.strip() for param in batch_input_model.parameters.split(",") if param.strip()]
            methods = [method.strip() for method in batch_input_model.methods.split(",") if method.strip()]

            # 检查输入数据长度是否一致
            if len(categories) != len(parameters) or len(parameters) != len(methods):
                raise ServiceException(message="批量录入失败，检测类别、参数和方法的数量必须一致")

            # 批量创建技术手册
            success_count = 0
            error_messages = []

            for i in range(len(categories)):
                try:
                    category = categories[i]
                    parameter = parameters[i]
                    method = methods[i]

                    # 获取或创建类目编号
                    category_codes = await self.create_categories_if_not_exists(
                        batch_input_model.classification, category, current_user
                    )
                    category_code = category_codes[0] if category_codes else None

                    # 校验技术手册是否唯一
                    if not await self.check_technical_manual_unique(category_code, parameter, method):
                        error_messages.append(f"第{i+1}条记录：类目、参数和方法组合已存在")
                        continue

                    # 校验唯一编号是否唯一
                    if not await self.check_qualification_code_unique(batch_input_model.qualification_code):
                        error_messages.append(f"第{i+1}条记录：唯一编号已存在")
                        continue

                    # 生成检测编号
                    test_code = await self.generate_test_code()

                    # 创建技术手册对象
                    technical_manual = TechnicalManual(
                        test_code=test_code,
                        category_codes=category_codes,
                        parameter=parameter,
                        method=method,
                        description=None,
                        # 新增字段
                        qualification_code=(
                            batch_input_model.qualification_code.strip()
                            if batch_input_model.qualification_code
                            else None
                        ),
                        limitation_scope=batch_input_model.limitation_scope,
                        alias_list=batch_input_model.alias_list,
                        qualification_date=batch_input_model.qualification_date,
                        has_qualification=batch_input_model.has_qualification,
                        special_consumables_price=batch_input_model.special_consumables_price,
                        # 原有字段
                        status=batch_input_model.status,
                        create_by=current_user.user.user_name if current_user and current_user.user else "",
                        create_time=datetime.now(),
                        update_by=current_user.user.user_name if current_user and current_user.user else "",
                        update_time=datetime.now(),
                        remark=batch_input_model.remark,
                    )

                    # 新增技术手册
                    self.db.add(technical_manual)
                    success_count += 1

                except Exception as e:
                    error_messages.append(f"第{i+1}条记录：{str(e)}")

            # 提交事务
            await self.db.commit()

            # 返回结果
            message = f"批量录入完成，成功{success_count}条"
            if error_messages:
                message += f'，失败{len(error_messages)}条：{"; ".join(error_messages)}'

            return CrudResponseModel(
                is_success=True,
                message=message,
                result={"success_count": success_count, "error_count": len(error_messages)},
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量录入技术手册失败", e)
            raise

    async def export_technical_manual(self, query_object: TechnicalManualQueryModel, export_type: str) -> Response:
        """
        导出技术手册

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取技术手册列表
        technical_manuals = await self.get_technical_manual_list(query_object)

        # 转换为字典列表
        data = []
        for manual in technical_manuals:
            # 将SQLAlchemy模型转换为字典
            manual_dict = {
                "id": manual.id,
                "test_code": manual.test_code or "",
                "category": manual.category,
                "parameter": manual.parameter,
                "method": manual.method,
                "description": manual.description or "",
                # 新增字段
                "classification": manual.classification or "",
                "qualification_code": manual.qualification_code or "",
                "limitation_scope": manual.limitation_scope or "",
                "alias_list": ",".join(manual.alias_list or []),
                "qualification_date": (
                    manual.qualification_date.strftime("%Y-%m-%d") if manual.qualification_date else ""
                ),
                # 原有字段
                "status": "启用" if manual.status == "0" else "停用",
                "create_time": manual.create_time.strftime("%Y-%m-%d %H:%M:%S") if manual.create_time else "",
                "create_by": manual.create_by,
                "remark": manual.remark or "",
            }
            data.append(manual_dict)

        # 定义表头映射
        headers = {
            "id": "ID",
            "test_code": "检测编号",
            "category": "检测类别",
            "parameter": "检测参数",
            "method": "检测方法",
            "description": "描述",
            # 新增字段
            "classification": "分类",
            "qualification_code": "唯一编号",
            "limitation_scope": "限制范围",
            "common_alias": "常用别名（",
            "qualification_date": "取得资质时间",
            # 原有字段
            "status": "状态",
            "create_time": "创建时间",
            "create_by": "创建人",
            "remark": "备注",
        }

        # 根据导出类型导出文件
        if export_type.lower() == "excel":
            return await ExportUtil.export_excel(data, "技术手册", headers)
        elif export_type.lower() == "pdf":
            return await ExportUtil.export_pdf(data, "技术手册", headers, "技术手册列表")
        elif export_type.lower() == "word":
            return await ExportUtil.export_word(data, "技术手册", headers, "技术手册列表")
        else:
            raise ServiceException(message=f"不支持的导出类型: {export_type}")

    async def get_categories(self):
        """
        获取所有检测类别

        :return: 检测类别列表
        """
        # 从类目表获取所有检测类别
        categories = await self.category_dao.get_categories_by_classification("")
        if not categories:
            # 如果没有指定分类，获取所有检测类别
            stmt = select(distinct(TechnicalManualCategory.category)).where(TechnicalManualCategory.status == "0")
            result = await self.db.execute(stmt)
            categories = result.scalars().all()

        category_list = [{"category": category} for category in categories]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category_list)

    async def get_parameters(self, category: str = None):
        """
        获取检测参数列表

        :param category: 检测类别
        :return: 检测参数列表
        """
        conditions = [TechnicalManual.status == "0"]

        # 如果指定了检测类别，需要通过类目表获取对应的类目编号
        if category:
            stmt = select(TechnicalManualCategory.category_code).where(TechnicalManualCategory.category == category)
            result = await self.db.execute(stmt)
            category_codes = result.scalars().all()

            if category_codes:
                # 使用JSON_CONTAINS查询包含任一类目编码的记录
                category_conditions = []
                for code in category_codes:
                    category_conditions.append(func.json_contains(TechnicalManual.category_codes, f'"{code}"'))
                conditions.append(or_(*category_conditions))
            else:
                # 如果没有找到对应的类目编号，返回空列表
                return []

        stmt = select(distinct(TechnicalManual.parameter)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        parameters = result.scalars().all()
        parameter_list = [{"parameter": parameter} for parameter in parameters]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(parameter_list)

    async def get_methods(self, category: str = None, parameter: str = None):
        """
        获取检测方法列表

        :param category: 检测类别
        :param parameter: 检测参数
        :return: 检测方法列表
        """
        conditions = [TechnicalManual.status == "0"]

        # 如果指定了检测类别，需要通过类目表获取对应的类目编号
        if category:
            stmt = select(TechnicalManualCategory.category_code).where(TechnicalManualCategory.category == category)
            result = await self.db.execute(stmt)
            category_codes = result.scalars().all()

            if category_codes:
                # 使用JSON_CONTAINS查询包含任一类目编码的记录
                category_conditions = []
                for code in category_codes:
                    category_conditions.append(func.json_contains(TechnicalManual.category_codes, f'"{code}"'))
                conditions.append(or_(*category_conditions))
            else:
                # 如果没有找到对应的类目编号，返回空列表
                return []

        if parameter:
            conditions.append(TechnicalManual.parameter == parameter)

        stmt = select(distinct(TechnicalManual.method)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        methods = result.scalars().all()
        method_list = [{"method": method} for method in methods]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(method_list)

    async def get_category_tree(self):
        """
        获取检测类别树形数据

        :return: 检测类别树形数据
        """
        # 从类目表查询所有启用的分类和检测类别数据
        stmt = (
            select(TechnicalManualCategory.classification, TechnicalManualCategory.category)
            .where(TechnicalManualCategory.status == "0")
            .distinct()
            .order_by(TechnicalManualCategory.classification, TechnicalManualCategory.category)
        )

        result = await self.db.execute(stmt)
        data = result.all()

        # 构建树形结构
        tree_data = {}
        for row in data:
            classification = row[0] or "未分类"
            category = row[1]

            if classification not in tree_data:
                tree_data[classification] = {
                    "label": classification,
                    "value": classification,
                    "key": f"classification_{classification}",
                    "children": [],
                    "selectable": False,  # 分类不可选择
                    "disabled": True,  # 禁用分类节点
                }

            # 检查是否已存在该检测类别
            existing_category = next(
                (item for item in tree_data[classification]["children"] if item["value"] == category), None
            )

            if not existing_category:
                tree_data[classification]["children"].append(
                    {
                        "label": category,
                        "value": category,
                        "key": f"category_{category}",
                        "selectable": True,  # 检测类别可选择
                        "disabled": False,  # 启用检测类别节点
                    }
                )

        # 转换为列表格式
        tree_list = list(tree_data.values())

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(tree_list)

    async def get_classifications(self):
        """
        获取所有分类

        :return: 分类列表
        """
        classifications = await self.category_dao.get_classifications()
        classification_list = [{"classification": classification} for classification in classifications]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(classification_list)

    async def get_categories_by_classification(self, classification: str):
        """
        根据分类获取检测类别列表

        :param classification: 分类
        :return: 检测类别列表
        """
        categories = await self.category_dao.get_categories_by_classification(classification)
        category_list = [{"category": category} for category in categories]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category_list)

    async def get_category_options(self):
        """
        获取分类和检测类别的选项数据

        :return: 分类和检测类别选项数据
        """
        # 获取所有分类
        classifications = await self.category_dao.get_classifications()

        result = {}
        for classification in classifications:
            categories = await self.category_dao.get_categories_by_classification(classification)
            result[classification] = categories

        return result
