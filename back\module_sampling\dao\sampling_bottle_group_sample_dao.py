"""
瓶组样品关联数据访问对象
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_

from module_sampling.entity.do.sampling_bottle_group_sample_do import SamplingBottleGroupSample
from module_sampling.entity.do.sample_record_do import SampleRecord


class SamplingBottleGroupSampleDAO:
    """瓶组样品关联数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_bottle_group_sample(self, bottle_group_sample: SamplingBottleGroupSample) -> SamplingBottleGroupSample:
        """创建瓶组样品关联记录"""
        self.db.add(bottle_group_sample)
        await self.db.flush()
        await self.db.refresh(bottle_group_sample)
        return bottle_group_sample
    
    async def batch_create_bottle_group_samples(self, bottle_group_samples: List[SamplingBottleGroupSample]) -> List[SamplingBottleGroupSample]:
        """批量创建瓶组样品关联记录"""
        self.db.add_all(bottle_group_samples)
        await self.db.flush()
        for relation in bottle_group_samples:
            await self.db.refresh(relation)
        return bottle_group_samples
    
    async def get_samples_by_bottle_group_id(self, bottle_group_id: int) -> List[SampleRecord]:
        """根据瓶组ID获取关联的样品记录列表"""
        stmt = select(SampleRecord).join(
            SamplingBottleGroupSample,
            SampleRecord.id == SamplingBottleGroupSample.sample_record_id
        ).where(SamplingBottleGroupSample.bottle_group_id == bottle_group_id).order_by(SampleRecord.sample_number)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_group_samples_by_bottle_group_id(self, bottle_group_id: int) -> List[SamplingBottleGroupSample]:
        """根据瓶组ID获取瓶组样品关联记录列表"""
        stmt = select(SamplingBottleGroupSample).where(SamplingBottleGroupSample.bottle_group_id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_group_sample_by_ids(self, bottle_group_id: int, sample_record_id: int) -> Optional[SamplingBottleGroupSample]:
        """根据瓶组ID和样品记录ID获取关联记录"""
        stmt = select(SamplingBottleGroupSample).where(
            and_(
                SamplingBottleGroupSample.bottle_group_id == bottle_group_id,
                SamplingBottleGroupSample.sample_record_id == sample_record_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def delete_bottle_group_sample(self, bottle_group_id: int, sample_record_id: int) -> bool:
        """删除瓶组样品关联记录"""
        stmt = delete(SamplingBottleGroupSample).where(
            and_(
                SamplingBottleGroupSample.bottle_group_id == bottle_group_id,
                SamplingBottleGroupSample.sample_record_id == sample_record_id
            )
        )
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_group_samples_by_bottle_group_id(self, bottle_group_id: int) -> bool:
        """删除瓶组下的所有样品关联记录"""
        stmt = delete(SamplingBottleGroupSample).where(SamplingBottleGroupSample.bottle_group_id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_group_samples_by_sample_record_id(self, sample_record_id: int) -> bool:
        """删除样品记录的所有瓶组关联记录"""
        stmt = delete(SamplingBottleGroupSample).where(SamplingBottleGroupSample.sample_record_id == sample_record_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
