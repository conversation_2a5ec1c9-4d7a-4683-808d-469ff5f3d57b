# LIMS 项目 Docker 部署指南

本文档介绍如何使用 Docker 和 Docker Compose 部署 LIMS（Laboratory Information Management System）项目。

## 项目架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    Frontend     │    │    Backend      │
│   (反向代理)     │◄───┤   (Vue 3)       │◄───┤   (FastAPI)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐            │
         └──────────────►│     MySQL       │◄───────────┘
                        │   (数据库)       │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │     Redis       │
                        │    (缓存)       │
                        └─────────────────┘
```

## 快速开始

### 1. 环境要求

- Docker >= 20.10
- Docker Compose >= 2.0
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

### 2. 一键部署

```bash
# 克隆项目（如果还没有）
git clone <your-repo-url>
cd lims2

# 进入 docker 目录
cd docker

# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

### 3. 手动部署

如果一键部署脚本有问题，可以手动执行以下步骤：

```bash
# 进入 docker 目录
cd docker

# 1. 创建必要的目录
mkdir -p mysql/conf.d nginx/conf.d ssl ../back/logs ../back/vf_admin/upload_path

# 2. 构建并启动服务
docker-compose up -d --build

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

## 访问地址

部署完成后，可以通过以下地址访问：

- **前端应用**: http://localhost
- **后端API**: http://localhost/dev-api
- **API文档**: http://localhost/dev-api/docs
- **默认账号**: admin / admin123

## 服务说明

### MySQL 数据库
- **端口**: 3306
- **数据库**: lims
- **用户名**: lims-user
- **密码**: lims-Root1
- **数据持久化**: mysql_data 卷

### Redis 缓存
- **端口**: 6379
- **数据持久化**: redis_data 卷

### 后端服务 (FastAPI)
- **端口**: 9099
- **环境**: 生产环境
- **日志**: ./back/logs
- **上传文件**: ./back/vf_admin/upload_path

### 前端服务 (Vue 3)
- **端口**: 内部 80 端口
- **构建**: 生产环境构建

### Nginx 反向代理
- **端口**: 80 (HTTP), 443 (HTTPS)
- **功能**: 
  - 前端静态文件服务
  - 后端API代理
  - 文件上传代理
  - 负载均衡

## 开发环境

如果需要开发环境（支持热重载），使用以下命令：

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d --build

# 查看开发环境日志
docker-compose -f docker-compose.dev.yml logs -f backend
```

开发环境特点：
- 后端支持热重载
- 源码挂载到容器内
- 使用开发环境配置

## 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]
```

### 数据库管理
```bash
# 连接到 MySQL
docker-compose exec mysql mysql -u lims-user -p lims

# 备份数据库
docker-compose exec mysql mysqldump -u lims-user -p lims > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u lims-user -p lims < backup.sql
```

### 应用管理
```bash
# 进入后端容器
docker-compose exec backend bash

# 查看后端日志
docker-compose logs -f backend

# 重新构建镜像
docker-compose build --no-cache

# 更新服务
docker-compose up -d --build
```

## 配置说明

### 环境变量
主要配置在 `.env` 文件中：
- 数据库连接信息
- Redis 连接信息
- 端口配置
- 域名配置

### 数据持久化
- MySQL 数据: `mysql_data` 卷
- Redis 数据: `redis_data` 卷
- 上传文件: `./back/vf_admin/upload_path` 目录
- 日志文件: `./back/logs` 目录

## 故障排除

### 1. 服务启动失败
```bash
# 查看详细日志
docker-compose logs [service_name]

# 检查容器状态
docker-compose ps

# 重新构建镜像
docker-compose build --no-cache [service_name]
```

### 2. 数据库连接失败
```bash
# 检查 MySQL 服务状态
docker-compose exec mysql mysqladmin ping

# 查看 MySQL 日志
docker-compose logs mysql

# 重启 MySQL 服务
docker-compose restart mysql
```

### 3. 前端无法访问后端
```bash
# 检查 Nginx 配置
docker-compose exec nginx nginx -t

# 查看 Nginx 日志
docker-compose logs nginx

# 重启 Nginx
docker-compose restart nginx
```

### 4. 镜像拉取失败
```bash
# 测试镜像仓库连接
./test-network.sh

# 运行故障排除脚本
./troubleshoot.sh

# 如果 Xuanyuan 镜像仓库不可用，可以手动拉取镜像
docker pull docker.xuanyuan.me/python:3.12-slim
docker pull docker.xuanyuan.me/mysql:8.0
docker pull docker.xuanyuan.me/redis:7-alpine
docker pull docker.xuanyuan.me/nginx:alpine
```

### 5. 端口冲突
如果默认端口被占用，可以修改 `.env` 文件中的端口配置，然后重新启动服务。

## 生产环境部署

### 1. SSL 证书配置
将 SSL 证书文件放在 `ssl` 目录下，并修改 `nginx/conf.d/lims.conf` 配置。

### 2. 域名配置
修改 `.env` 文件中的 `DOMAIN` 变量，并更新 Nginx 配置。

### 3. 安全配置
- 修改默认密码
- 配置防火墙
- 启用 HTTPS
- 定期备份数据

## 监控和维护

### 1. 日志监控
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01" --until="2024-01-02"
```

### 2. 资源监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df
```

### 3. 定期维护
- 定期备份数据库
- 清理无用的 Docker 镜像和容器
- 更新系统和依赖包
- 监控日志文件大小

## 技术支持

如果遇到问题，请：
1. 查看相关服务的日志
2. 检查配置文件是否正确
3. 确认网络和端口是否正常
4. 参考本文档的故障排除部分
