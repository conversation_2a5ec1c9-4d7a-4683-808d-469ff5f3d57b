from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from utils.response_util import ResponseUtil
from utils.common_util import CamelCaseUtil
from module_sampling.service.sampling_task_service import SamplingTaskService
from module_sampling.dto.sampling_task_dto import (
    SamplingTaskCreateDTO, SamplingTaskUpdateDTO, SamplingTaskDTO,
    SamplingTaskQueryDTO
)


router = APIRouter(prefix="/sampling/task", tags=["采样任务管理"])


@router.post("/create", response_model=CrudResponseModel, summary="创建采样任务")
async def create_sampling_task(
    task_dto: SamplingTaskCreateDTO = Body(..., description="采样任务创建信息"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """创建采样任务"""
    try:
        service = SamplingTaskService(db)
        result = await service.create_sampling_task_with_items(task_dto, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="创建采样任务成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/update/{task_id}", response_model=CrudResponseModel, summary="更新采样任务")
async def update_sampling_task(
    task_id: int = Path(..., description="任务ID"),
    task_dto: SamplingTaskUpdateDTO = Body(..., description="采样任务更新信息"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """更新采样任务"""
    try:
        service = SamplingTaskService(db)
        result = await service.update_sampling_task(task_id, task_dto, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="更新采样任务成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.delete("/delete/{task_id}", response_model=CrudResponseModel, summary="删除采样任务")
async def delete_sampling_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """删除采样任务"""
    try:
        service = SamplingTaskService(db)
        result = await service.delete_sampling_task(task_id, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="删除采样任务成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/members/{task_id}", summary="获取任务组员列表")
async def get_task_members(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """获取任务组员列表，用于执行人指派时的用户选择"""
    try:
        service = SamplingTaskService(db)
        members = await service.get_task_members(task_id)
        # 转换为驼峰形式
        camel_members = [CamelCaseUtil.transform_result(member) for member in members]
        return ResponseUtil.success(data=camel_members, msg="获取任务组员成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/get/{task_id}", response_model=CrudResponseModel, summary="获取采样任务详情")
async def get_sampling_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """获取采样任务详情"""
    try:
        service = SamplingTaskService(db)
        result = await service.get_sampling_task_by_id(task_id)
        if result:
            # 转换为驼峰形式
            camel_result = CamelCaseUtil.transform_result(result)
            return ResponseUtil.success(data=camel_result, msg="获取采样任务成功")
        else:
            return ResponseUtil.error(msg="采样任务不存在")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/page", response_model=CrudResponseModel, summary="分页查询采样任务")
async def page_sampling_tasks(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    task_name: Optional[str] = Query(None, description="任务名称"),
    task_code: Optional[str] = Query(None, description="任务编号"),
    project_quotation_id: Optional[int] = Query(None, description="项目报价ID"),
    responsible_user_id: Optional[int] = Query(None, description="负责人用户ID"),
    status: Optional[int] = Query(None, description="任务状态"),
    planned_start_date_from: Optional[str] = Query(None, description="计划开始日期（起）"),
    planned_start_date_to: Optional[str] = Query(None, description="计划开始日期（止）"),
    planned_end_date_from: Optional[str] = Query(None, description="计划结束日期（起）"),
    planned_end_date_to: Optional[str] = Query(None, description="计划结束日期（止）"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """分页查询采样任务"""
    try:
        from datetime import datetime

        # 构建查询条件
        query_dto = SamplingTaskQueryDTO(
            task_name=task_name,
            task_code=task_code,
            project_quotation_id=project_quotation_id,
            responsible_user_id=responsible_user_id,
            status=status,
            planned_start_date_from=datetime.strptime(planned_start_date_from, '%Y-%m-%d').date() if planned_start_date_from else None,
            planned_start_date_to=datetime.strptime(planned_start_date_to, '%Y-%m-%d').date() if planned_start_date_to else None,
            planned_end_date_from=datetime.strptime(planned_end_date_from, '%Y-%m-%d').date() if planned_end_date_from else None,
            planned_end_date_to=datetime.strptime(planned_end_date_to, '%Y-%m-%d').date() if planned_end_date_to else None
        )

        service = SamplingTaskService(db)
        tasks, total = await service.page_sampling_tasks(query_dto, page, size)

        result = {
            "records": tasks,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }

        return ResponseUtil.success(data=result, msg="查询采样任务成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/page-enhanced", response_model=CrudResponseModel, summary="增强版分页查询采样任务（参考项目报价列表）")
async def page_sampling_tasks_enhanced(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    task_name: Optional[str] = Query(None, description="任务名称"),
    task_code: Optional[str] = Query(None, description="任务编号"),
    project_name: Optional[str] = Query(None, description="项目名称"),
    project_code: Optional[str] = Query(None, description="项目代码"),
    customer_name: Optional[str] = Query(None, description="客户名称"),
    project_quotation_id: Optional[int] = Query(None, description="项目报价ID"),
    responsible_user_id: Optional[int] = Query(None, description="负责人用户ID"),
    assigned_user_name: Optional[str] = Query(None, description="分配用户姓名"),
    status: Optional[int] = Query(None, description="任务状态"),
    planned_start_date_from: Optional[str] = Query(None, description="计划开始日期（起）"),
    planned_start_date_to: Optional[str] = Query(None, description="计划开始日期（止）"),
    planned_end_date_from: Optional[str] = Query(None, description="计划结束日期（起）"),
    planned_end_date_to: Optional[str] = Query(None, description="计划结束日期（止）"),
    create_time_from: Optional[str] = Query(None, description="创建时间（起）"),
    create_time_to: Optional[str] = Query(None, description="创建时间（止）"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """增强版分页查询采样任务，参考项目报价列表的查询逻辑"""
    try:
        from datetime import datetime

        service = SamplingTaskService(db)

        # 构建增强查询参数
        enhanced_query_params = {
            'page': page,
            'size': size,
            'task_name': task_name,
            'task_code': task_code,
            'project_name': project_name,
            'project_code': project_code,
            'customer_name': customer_name,
            'project_quotation_id': project_quotation_id,
            'responsible_user_id': responsible_user_id,
            'assigned_user_name': assigned_user_name,
            'status': status,
            'planned_start_date_from': datetime.strptime(planned_start_date_from, '%Y-%m-%d').date() if planned_start_date_from else None,
            'planned_start_date_to': datetime.strptime(planned_start_date_to, '%Y-%m-%d').date() if planned_start_date_to else None,
            'planned_end_date_from': datetime.strptime(planned_end_date_from, '%Y-%m-%d').date() if planned_end_date_from else None,
            'planned_end_date_to': datetime.strptime(planned_end_date_to, '%Y-%m-%d').date() if planned_end_date_to else None,
            'create_time_from': datetime.strptime(create_time_from, '%Y-%m-%d %H:%M:%S') if create_time_from else None,
            'create_time_to': datetime.strptime(create_time_to, '%Y-%m-%d %H:%M:%S') if create_time_to else None
        }

        result = await service.page_sampling_tasks_enhanced(enhanced_query_params)

        return ResponseUtil.success(data=result, msg="查询采样任务成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))






@router.get("/project/{project_quotation_id}", response_model=CrudResponseModel, summary="根据项目报价ID获取采样任务列表")
async def get_tasks_by_project_quotation_id(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据项目报价ID获取采样任务列表"""
    try:
        service = SamplingTaskService(db)
        result = await service.get_tasks_by_project_quotation_id(project_quotation_id)
        return ResponseUtil.success(data=result, msg="获取采样任务列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/user/{responsible_user_id}", response_model=CrudResponseModel, summary="根据负责人用户ID获取采样任务列表")
async def get_tasks_by_responsible_user_id(
    responsible_user_id: int = Path(..., description="负责人用户ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据负责人用户ID获取采样任务列表"""
    try:
        service = SamplingTaskService(db)
        result = await service.get_tasks_by_responsible_user_id(responsible_user_id)
        return ResponseUtil.success(data=result, msg="获取采样任务列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/my-tasks", response_model=CrudResponseModel, summary="获取我的采样任务列表")
async def get_my_tasks(
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """获取我的采样任务列表"""
    try:
        service = SamplingTaskService(db)
        result = await service.get_tasks_by_responsible_user_id(current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="获取我的采样任务列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/status/{task_id}", response_model=CrudResponseModel, summary="更新采样任务状态")
async def update_task_status(
    task_id: int = Path(..., description="任务ID"),
    status: int = Body(..., description="新状态"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """更新采样任务状态"""
    try:
        from datetime import date

        # 构建更新DTO
        update_dto = SamplingTaskUpdateDTO(status=status)

        # 如果状态变为执行中，设置实际开始日期
        if status == 1:
            update_dto.actual_start_date = date.today()
        # 如果状态变为已完成，设置实际结束日期
        elif status == 2:
            update_dto.actual_end_date = date.today()

        service = SamplingTaskService(db)
        result = await service.update_sampling_task(task_id, update_dto, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="更新采样任务状态成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/urgent/{task_id}", response_model=CrudResponseModel, summary="设置采样任务加急状态")
async def set_task_urgent(
    task_id: int = Path(..., description="任务ID"),
    is_urgent: bool = Body(..., description="是否加急"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """设置采样任务加急状态"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"设置采样任务加急状态 - 任务ID: {task_id}, 是否加急: {is_urgent}, 用户ID: {current_user.user.user_id}")
        
        # 检查用户权限：超级权限、task-urgent权限或admin角色
        user_permissions = current_user.permissions if hasattr(current_user, 'permissions') else []
        user_roles = [role.role_key for role in current_user.user.role] if hasattr(current_user.user, 'role') else []
        
        logger.info(f"用户权限检查 - 权限列表: {user_permissions}, 角色列表: {user_roles}, 是否管理员: {current_user.user.admin}")
        
        has_permission = (
            '*:*:*' in user_permissions or  # 超级权限
            'task-urgent' in user_permissions or  # 特定权限
            'admin' in user_roles or  # admin角色
            current_user.user.admin  # 管理员标识
        )
        
        logger.info(f"权限检查结果: {has_permission}")
        
        if not has_permission:
            logger.warning(f"用户 {current_user.user.user_id} 没有设置加急任务的权限")
            return ResponseUtil.error(msg="您没有设置加急任务的权限")
        
        # 构建更新DTO
        update_dto = SamplingTaskUpdateDTO(is_urgent=is_urgent)
        logger.info(f"构建更新DTO: {update_dto}")
        
        service = SamplingTaskService(db)
        logger.info(f"开始调用服务更新采样任务")
        result = await service.update_sampling_task(task_id, update_dto, current_user.user.user_id)
        logger.info(f"服务调用成功，结果: {result}")
        
        action = "设置" if is_urgent else "取消"
        return ResponseUtil.success(data=result, msg=f"{action}采样任务加急状态成功")
    except Exception as e:
        logger.error(f"设置采样任务加急状态失败 - 任务ID: {task_id}, 错误信息: {str(e)}", exc_info=True)
        return ResponseUtil.error(msg=f"设置加急状态失败: {str(e)}")