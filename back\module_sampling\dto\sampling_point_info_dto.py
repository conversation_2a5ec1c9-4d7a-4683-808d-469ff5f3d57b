from pydantic import BaseModel, ConfigDict
from typing import Optional
from datetime import datetime
from decimal import Decimal
from pydantic.alias_generators import to_camel


class SamplingPointInfoCreateDTO(BaseModel):
    """采样点位信息创建DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_group_id: int
    point_name: Optional[str] = None
    
    # 点位照片信息
    point_photo_url: Optional[str] = None
    point_photo_name: Optional[str] = None
    
    # 环境照片信息（东南西北4张）
    east_photo_url: Optional[str] = None
    east_photo_name: Optional[str] = None
    south_photo_url: Optional[str] = None
    south_photo_name: Optional[str] = None
    west_photo_url: Optional[str] = None
    west_photo_name: Optional[str] = None
    north_photo_url: Optional[str] = None
    north_photo_name: Optional[str] = None
    
    # 经纬度信息
    longitude: Optional[Decimal] = None
    latitude: Optional[Decimal] = None
    coordinate_system: Optional[str] = 'WGS84'
    
    # 其他信息
    altitude: Optional[Decimal] = None
    sampling_time: Optional[datetime] = None
    weather_condition: Optional[str] = None
    temperature: Optional[Decimal] = None
    humidity: Optional[Decimal] = None
    wind_speed: Optional[Decimal] = None
    wind_direction: Optional[str] = None
    remarks: Optional[str] = None


class SamplingPointInfoUpdateDTO(BaseModel):
    """采样点位信息更新DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    point_name: Optional[str] = None
    
    # 点位照片信息
    point_photo_url: Optional[str] = None
    point_photo_name: Optional[str] = None
    
    # 环境照片信息（东南西北4张）
    east_photo_url: Optional[str] = None
    east_photo_name: Optional[str] = None
    south_photo_url: Optional[str] = None
    south_photo_name: Optional[str] = None
    west_photo_url: Optional[str] = None
    west_photo_name: Optional[str] = None
    north_photo_url: Optional[str] = None
    north_photo_name: Optional[str] = None
    
    # 经纬度信息
    longitude: Optional[Decimal] = None
    latitude: Optional[Decimal] = None
    coordinate_system: Optional[str] = None
    
    # 其他信息
    altitude: Optional[Decimal] = None
    sampling_time: Optional[datetime] = None
    weather_condition: Optional[str] = None
    temperature: Optional[Decimal] = None
    humidity: Optional[Decimal] = None
    wind_speed: Optional[Decimal] = None
    wind_direction: Optional[str] = None
    remarks: Optional[str] = None


class SamplingPointInfoDTO(BaseModel):
    """采样点位信息DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int
    sampling_task_group_id: int
    point_name: Optional[str] = None
    
    # 点位照片信息
    point_photo_url: Optional[str] = None
    point_photo_name: Optional[str] = None
    
    # 环境照片信息（东南西北4张）
    east_photo_url: Optional[str] = None
    east_photo_name: Optional[str] = None
    south_photo_url: Optional[str] = None
    south_photo_name: Optional[str] = None
    west_photo_url: Optional[str] = None
    west_photo_name: Optional[str] = None
    north_photo_url: Optional[str] = None
    north_photo_name: Optional[str] = None
    
    # 经纬度信息
    longitude: Optional[Decimal] = None
    latitude: Optional[Decimal] = None
    coordinate_system: Optional[str] = None
    
    # 其他信息
    altitude: Optional[Decimal] = None
    sampling_time: Optional[datetime] = None
    weather_condition: Optional[str] = None
    temperature: Optional[Decimal] = None
    humidity: Optional[Decimal] = None
    wind_speed: Optional[Decimal] = None
    wind_direction: Optional[str] = None
    remarks: Optional[str] = None
    
    # 审计字段
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None


class SamplingPointInfoPhotoUploadDTO(BaseModel):
    """点位信息照片上传DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    photo_type: str  # point, east, south, west, north
    photo_url: str
    photo_name: str
