import request from '@/utils/request'

// 查询样品记录列表
export function listSampleRecords(query) {
  return request({
    url: '/sampling/sample-records',
    method: 'get',
    params: query
  })
}



// 查询任务分组的样品记录列表
export function getSampleRecordsByGroup(groupId) {
  return request({
    url: `/sampling/sample-records/group/${groupId}`,
    method: 'get'
  })
}

// 查询样品记录详细
export function getSampleRecord(recordId) {
  return request({
    url: `/sampling/sample-records/${recordId}`,
    method: 'get'
  })
}

// 新增样品记录
export function addSampleRecord(data) {
  return request({
    url: '/sampling/sample-records/create',
    method: 'post',
    data: data
  })
}

// 修改样品记录
export function updateSampleRecord(data) {
  return request({
    url: '/sampling/sample-records',
    method: 'put',
    data: data
  })
}

// 更新样品记录状态
export function updateSampleRecordStatus(recordId, status) {
  return request({
    url: `/sampling/sample-records/status/${recordId}`,
    method: 'put',
    data: status
  })
}

// 删除样品记录
export function delSampleRecord(recordId) {
  return request({
    url: `/sampling/sample-records/${recordId}`,
    method: 'delete'
  })
}



// 为任务分组生成样品记录
export function generateSampleRecordsForGroup(groupId) {
  return request({
    url: `/sampling/sample-records/generate/group/${groupId}`,
    method: 'post',
    timeout: 30000  // 增加超时时间到30秒
  })
}



// 批量更新样品记录状态
export function batchUpdateSampleRecordStatus(data) {
  return request({
    url: '/sampling/sample-records/batch-status',
    method: 'put',
    data: data
  })
}

// 获取任务分组的样品统计信息（通过计算样品记录得出）
export async function getSampleStatisticsByGroup(groupId) {
  try {
    // 先获取样品记录
    const response = await getSampleRecordsByGroup(groupId)
    const records = response.data || []

    // 计算统计信息
    const totalCount = records.length
    const pendingCount = records.filter(r => r.status === 0).length
    const collectedCount = records.filter(r => r.status === 1).length
    const submittedCount = records.filter(r => r.status === 2).length
    const testingCount = records.filter(r => r.status === 3).length
    const completedCount = records.filter(r => r.status === 4).length

    return {
      code: 200,
      msg: '操作成功',
      data: {
        totalCount,
        pendingCount,
        collectedCount,
        submittedCount,
        testingCount,
        completedCount
      }
    }
  } catch (error) {
    return {
      code: 500,
      msg: '获取统计信息失败',
      data: {
        totalCount: 0,
        pendingCount: 0,
        collectedCount: 0,
        submittedCount: 0,
        testingCount: 0,
        completedCount: 0
      }
    }
  }
}
