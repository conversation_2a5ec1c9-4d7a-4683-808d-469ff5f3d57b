# 客户管理模块数据库设计（优化版）

## 数据库表设计

根据需求文档，客户管理模块需要支持客户的录入、查询、修改和删除操作，并且客户分为两级结构：集团（一级客户）和公司（二级客户）。为了简化设计，我们将客户集团表和客户公司表合并为一张统一的客户表。

### 1. 客户表（customer）

存储所有客户信息，包括集团和公司。

| 字段名 | 数据类型 | 是否主键 | 是否必填 | 描述 |
| ------ | -------- | -------- | -------- | ---- |
| customer_id | bigint | 是 | 是 | 客户ID，自增主键 |
| customer_name | varchar(100) | 否 | 是 | 客户名称，唯一索引 |
| customer_level | char(1) | 否 | 是 | 客户级别（1集团 2公司） |
| parent_id | bigint | 否 | 否 | 上级客户ID，公司关联到集团 |
| customer_type | varchar(20) | 否 | 是 | 客户类型（政府/企业/园区） |
| customer_source | varchar(50) | 否 | 是 | 客户来源 |
| province | varchar(50) | 否 | 是 | 省份 |
| city | varchar(50) | 否 | 是 | 城市 |
| district | varchar(50) | 否 | 否 | 区县 |
| address | varchar(255) | 否 | 否 | 详细地址 |
| status | char(1) | 否 | 是 | 状态（0正常 1停用） |
| del_flag | char(1) | 否 | 是 | 删除标志（0存在 2删除） |
| create_by | varchar(64) | 否 | 是 | 创建者 |
| create_time | datetime | 否 | 是 | 创建时间 |
| update_by | varchar(64) | 否 | 否 | 更新者 |
| update_time | datetime | 否 | 否 | 更新时间 |
| remark | varchar(500) | 否 | 否 | 备注 |

### 2. 客户联系人表（customer_contact）

存储客户联系人信息，一个客户可以有多个联系人。

| 字段名 | 数据类型 | 是否主键 | 是否必填 | 描述 |
| ------ | -------- | -------- | -------- | ---- |
| contact_id | bigint | 是 | 是 | 联系人ID，自增主键 |
| customer_id | bigint | 否 | 是 | 客户ID，外键关联customer表 |
| contact_name | varchar(50) | 否 | 是 | 联系人姓名 |
| position | varchar(50) | 否 | 否 | 职务 |
| phone | varchar(20) | 否 | 否 | 电话 |
| wechat | varchar(50) | 否 | 否 | 微信 |
| email | varchar(100) | 否 | 否 | 邮箱 |
| is_primary | char(1) | 否 | 是 | 是否主要联系人（0否 1是） |
| status | char(1) | 否 | 是 | 状态（0正常 1停用） |
| create_by | varchar(64) | 否 | 是 | 创建者 |
| create_time | datetime | 否 | 是 | 创建时间 |
| update_by | varchar(64) | 否 | 否 | 更新者 |
| update_time | datetime | 否 | 否 | 更新时间 |
| remark | varchar(500) | 否 | 否 | 备注 |

### 3. 客户内部负责人关联表（customer_internal_manager）

存储客户与内部负责人的关联关系，一个客户可以有多个内部负责人。

| 字段名 | 数据类型 | 是否主键 | 是否必填 | 描述 |
| ------ | -------- | -------- | -------- | ---- |
| id | bigint | 是 | 是 | 关联ID，自增主键 |
| customer_id | bigint | 否 | 是 | 客户ID，外键关联customer表 |
| user_id | bigint | 否 | 是 | 内部负责人ID，关联sys_user表 |
| is_primary | char(1) | 否 | 是 | 是否主要负责人（0否 1是） |
| create_by | varchar(64) | 否 | 是 | 创建者 |
| create_time | datetime | 否 | 是 | 创建时间 |
| update_by | varchar(64) | 否 | 否 | 更新者 |
| update_time | datetime | 否 | 否 | 更新时间 |

## 数据库表关系

1. 客户表（customer）可以自关联，形成层级结构，公司级客户通过parent_id关联到集团级客户。
2. 客户表（customer）与客户联系人表（customer_contact）是一对多关系，一个客户可以有多个联系人。
3. 客户表（customer）与客户内部负责人关联表（customer_internal_manager）是一对多关系，一个客户可以有多个内部负责人。

## 索引设计

1. 客户表（customer）
   - 主键索引：customer_id
   - 唯一索引：customer_name（确保客户名称唯一）
   - 普通索引：parent_id（加速查询某个集团下的所有公司）
   - 普通索引：customer_level, customer_type, customer_source, province, city

2. 客户联系人表（customer_contact）
   - 主键索引：contact_id
   - 外键索引：customer_id
   - 普通索引：contact_name, phone, email

3. 客户内部负责人关联表（customer_internal_manager）
   - 主键索引：id
   - 外键索引：customer_id
   - 普通索引：user_id

## 设计优势

1. **简化数据结构**：将两张表合并为一张表，减少了表的数量，简化了数据库结构。
2. **灵活性**：通过自关联实现层级结构，未来如果需要增加更多层级，只需要继续使用相同的自关联结构即可。
3. **查询效率**：可以直接在一张表中查询所有客户，也可以通过customer_level字段区分集团和公司。
4. **数据一致性**：集团和公司使用相同的字段结构，保持了数据的一致性。
5. **扩展性**：如果未来需要增加新的客户级别，只需要在customer_level字段中添加新的值即可。
