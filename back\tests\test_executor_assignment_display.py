"""
测试执行人指派显示功能
"""
import asyncio


class TestExecutorAssignmentDisplay:
    """测试执行人指派显示功能"""
    
    def test_field_mapping(self):
        """测试字段映射逻辑"""
        # 模拟后端返回的执行人指派数据
        mock_executor_assignments = [
            {
                'id': 1,
                'cycleNumber': 1,
                'cycleType': '月检',
                'detectionCategory': '水质检测',
                'pointName': '采样点1',
                'assignedUserIds': [1, 2],
                'assignedUserNames': ['张三', '李四']  # 后端返回的用户名列表
            },
            {
                'id': 2,
                'cycleNumber': 2,
                'cycleType': '月检',
                'detectionCategory': '水质检测',
                'pointName': '采样点1',
                'assignedUserIds': [3, 4],
                'assignedUserNames': ['王五', '赵六']
            }
        ]
        
        # 模拟分组数据
        mock_group = {
            'cycleNumber': 1,
            'cycleType': '月检',
            'detectionCategory': '水质检测',
            'pointName': '采样点1'
        }
        
        # 模拟前端查找执行人的逻辑
        assigned_executors = []
        assignment = None
        
        for a in mock_executor_assignments:
            if (a['cycleNumber'] == mock_group['cycleNumber'] and
                a['cycleType'] == mock_group['cycleType'] and
                a['detectionCategory'] == mock_group['detectionCategory'] and
                a['pointName'] == mock_group['pointName']):
                assignment = a
                break
        
        if assignment:
            # 优先使用后端返回的用户名列表
            if assignment.get('assignedUserNames') and len(assignment['assignedUserNames']) > 0:
                assigned_executors = assignment['assignedUserNames']
            elif assignment.get('assignedUserIds') and len(assignment['assignedUserIds']) > 0:
                # 如果没有用户名列表，则根据用户ID查找用户名（这里模拟）
                mock_user_list = [
                    {'userId': 1, 'nickName': '张三', 'userName': 'zhangsan'},
                    {'userId': 2, 'nickName': '李四', 'userName': 'lisi'},
                    {'userId': 3, 'nickName': '王五', 'userName': 'wangwu'},
                    {'userId': 4, 'nickName': '赵六', 'userName': 'zhaoliu'}
                ]
                assigned_executors = []
                for user_id in assignment['assignedUserIds']:
                    user = next((u for u in mock_user_list if u['userId'] == user_id), None)
                    if user:
                        assigned_executors.append(user['nickName'] or user['userName'])
                    else:
                        assigned_executors.append(f'用户{user_id}')
        
        # 验证结果
        assert len(assigned_executors) == 2
        assert assigned_executors == ['张三', '李四']
        
        print("✅ 执行人字段映射测试通过")
        print(f"找到的执行人: {', '.join(assigned_executors)}")
    
    def test_display_logic(self):
        """测试显示逻辑"""
        # 模拟表格行数据
        mock_table_rows = [
            {
                'id': 1,
                'cycleNumber': 1,
                'isFirstInGroup': True,
                'assignedExecutors': ['张三', '李四']
            },
            {
                'id': 2,
                'cycleNumber': 1,
                'isFirstInGroup': False,
                'assignedExecutors': ['张三', '李四']
            },
            {
                'id': 3,
                'cycleNumber': 2,
                'isFirstInGroup': True,
                'assignedExecutors': []
            }
        ]
        
        # 模拟前端显示逻辑
        for row in mock_table_rows:
            display_text = ""
            if row['isFirstInGroup'] and row['assignedExecutors']:
                display_text = ', '.join(row['assignedExecutors'])
            elif row['isFirstInGroup']:
                display_text = "未指派"
            else:
                display_text = ""  # 非第一行不显示
            
            print(f"行 {row['id']}: {display_text}")
        
        # 验证第一行显示执行人
        assert mock_table_rows[0]['isFirstInGroup']
        assert len(mock_table_rows[0]['assignedExecutors']) > 0
        
        # 验证第二行不显示（非第一行）
        assert not mock_table_rows[1]['isFirstInGroup']
        
        # 验证第三行显示"未指派"
        assert mock_table_rows[2]['isFirstInGroup']
        assert len(mock_table_rows[2]['assignedExecutors']) == 0
        
        print("✅ 执行人显示逻辑测试通过")
    
    def test_batch_assignment_data_structure(self):
        """测试批量指派的数据结构"""
        # 模拟批量选择的条目
        selected_items = [
            {
                'cycleNumber': 1,
                'cycleType': '月检',
                'detectionCategory': '水质检测',
                'pointName': '采样点1',
                'detectionParameter': 'pH值',
                'detectionMethod': '玻璃电极法'
            },
            {
                'cycleNumber': 2,
                'cycleType': '月检',
                'detectionCategory': '水质检测',
                'pointName': '采样点1',
                'detectionParameter': '溶解氧',
                'detectionMethod': '电化学探头法'
            }
        ]
        
        # 模拟批量指派请求数据
        task_id = 123
        assigned_user_ids = [1, 2, 3]
        
        request_data = {
            'taskId': task_id,
            'assignments': []
        }
        
        for item in selected_items:
            assignment = {
                'samplingTaskId': task_id,
                'cycleNumber': item['cycleNumber'],
                'cycleType': item['cycleType'],
                'detectionCategory': item['detectionCategory'],
                'pointName': item['pointName'],
                'assignedUserIds': assigned_user_ids
            }
            request_data['assignments'].append(assignment)
        
        # 验证数据结构
        assert request_data['taskId'] == task_id
        assert len(request_data['assignments']) == 2
        assert all(a['assignedUserIds'] == assigned_user_ids for a in request_data['assignments'])
        
        print("✅ 批量指派数据结构测试通过")
        print(f"任务ID: {request_data['taskId']}")
        print(f"指派数量: {len(request_data['assignments'])}")
        print(f"执行人ID: {assigned_user_ids}")


if __name__ == "__main__":
    # 运行测试
    test = TestExecutorAssignmentDisplay()
    
    test.test_field_mapping()
    test.test_display_logic()
    test.test_batch_assignment_data_structure()
    
    print("\n🎉 所有执行人显示测试通过！")
