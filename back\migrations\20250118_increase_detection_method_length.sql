-- 增加检测方法字段长度
-- 执行时间: 2025-01-18
-- 描述: 增加sampling_bottle_group表中detection_method字段的长度，以支持更长的检测方法描述

-- 1. 修改采样瓶组表的检测方法字段长度
-- 先删除相关索引（忽略错误）
DROP INDEX `idx_detection_method` ON `sampling_bottle_group`;

-- 修改字段类型为TEXT
ALTER TABLE `sampling_bottle_group`
MODIFY COLUMN `detection_method` TEXT COMMENT '检测方法';

-- 重新创建索引（TEXT字段需要指定长度）
ALTER TABLE `sampling_bottle_group`
ADD INDEX `idx_detection_method` (`detection_method`(255));

-- 2. 修改瓶组与检测方法关联表的字段长度
-- 先删除相关索引（忽略错误）
DROP INDEX `idx_detection_method` ON `bottle_maintenance_method`;

-- 修改字段类型为TEXT
ALTER TABLE `bottle_maintenance_method`
MODIFY COLUMN `detection_method` TEXT COMMENT '检测方法';

-- 重新创建索引（TEXT字段需要指定长度）
ALTER TABLE `bottle_maintenance_method`
ADD INDEX `idx_detection_method` (`detection_method`(255));
