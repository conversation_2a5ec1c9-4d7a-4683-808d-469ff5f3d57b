"""
瓶组管理VO模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class BottleMaintenanceModel(BaseModel):
    """瓶组管理基础模型"""

    id: Optional[int] = Field(None, description="主键ID")
    bottle_code: Optional[str] = Field(None, description="瓶组编码")
    bottle_type: str = Field(..., description="容器类型")
    bottle_volume: str = Field(..., description="容器容量")
    storage_styles: Optional[List[str]] = Field(None, description="存储方式")
    fix_styles: Optional[List[str]] = Field(None, description="固定方式")
    sample_age: Optional[int] = Field(None, description="样品时效数值")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    remark: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class AddBottleMaintenanceModel(BaseModel):
    """添加瓶组管理模型"""

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    bottle_type: str = Field(..., description="容器类型")
    bottle_volume: str = Field(..., description="容器容量")
    storage_styles: Optional[List[str]] = Field(None, description="存储方式")
    fix_styles: Optional[List[str]] = Field(None, description="固定方式")
    sample_age: Optional[int] = Field(None, description="样品时效数值")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    remark: Optional[str] = Field(None, description="备注")
    technical_manual_ids: Optional[List[int]] = Field(None, description="关联的技术手册ID列表")


class EditBottleMaintenanceModel(BaseModel):
    """编辑瓶组管理模型"""

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    id: int = Field(..., description="主键ID")
    bottle_type: str = Field(..., description="容器类型")
    bottle_volume: str = Field(..., description="容器容量")
    storage_styles: Optional[List[str]] = Field(None, description="存储方式")
    fix_styles: Optional[List[str]] = Field(None, description="固定方式")
    sample_age: Optional[int] = Field(None, description="样品时效数值")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    remark: Optional[str] = Field(None, description="备注")
    technical_manual_ids: Optional[List[int]] = Field(None, description="关联的技术手册ID列表")


class BottleMaintenanceQueryModel(BaseModel):
    """瓶组管理查询模型"""

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    bottle_code: Optional[str] = Field(None, description="瓶组编码")
    bottle_type: Optional[str] = Field(None, description="容器类型")
    bottle_volume: Optional[str] = Field(None, description="容器容量")
    storage_styles: Optional[str] = Field(None, description="存储方式")
    fix_styles: Optional[str] = Field(None, description="固定方式")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    # 技术手册相关查询字段
    parameter: Optional[str] = Field(None, description="技术手册参数")
    method: Optional[str] = Field(None, description="技术手册方法")
    category: Optional[str] = Field(None, description="技术手册类别")


class BottleMaintenancePageQueryModel(BottleMaintenanceQueryModel):
    """
    技术手册分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    page_num: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页记录数")


class BottleMaintenanceResponseModel(BaseModel):
    """瓶组管理响应模型"""

    id: int = Field(..., description="主键ID")
    bottleCode: str = Field(..., description="瓶组编码")
    bottleType: str = Field(..., description="容器类型")
    bottleVolume: str = Field(..., description="容器容量")
    storageStyles: Optional[List[str]] = Field(None, description="存储方式")
    fixStyles: Optional[List[str]] = Field(None, description="固定方式")
    sampleAge: Optional[int] = Field(None, description="样品时效数值")
    sampleAgeUnit: Optional[str] = Field(None, description="样品时效单位")
    remark: Optional[str] = Field(None, description="备注")
    technicalManuals: Optional[List[dict]] = Field(None, description="关联的技术手册列表")

    class Config:
        from_attributes = True


class BottleMaintenanceImportModel(BaseModel):
    """瓶组管理批量导入模型"""

    bottle_type: str = Field(..., description="容器类型")
    bottle_volume: str = Field(..., description="容器容量")
    storage_styles: Optional[str] = Field(None, description="存储方式，逗号分隔")
    fix_styles: Optional[str] = Field(None, description="固定方式，逗号分隔")
    sample_age: Optional[int] = Field(None, description="样品时效数值")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    remark: Optional[str] = Field(None, description="备注")
    # 技术手册信息
    parameter: Optional[str] = Field(None, description="技术手册参数，多个用逗号分隔")
    method: Optional[str] = Field(None, description="技术手册方法，多个用逗号分隔")
    category: Optional[str] = Field(None, description="技术手册类别，多个用逗号分隔")


class BottleMaintenanceImportErrorModel(BaseModel):
    """瓶组管理导入错误模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    row: int = Field(..., description="错误行号")
    field: str = Field(..., description="错误字段")
    message: str = Field(..., description="错误信息")


class BottleMaintenanceImportResultModel(BaseModel):
    """瓶组管理导入结果模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    success_count: int = Field(default=0, description="成功导入数量")
    error_count: int = Field(default=0, description="失败导入数量")
    errors: List[BottleMaintenanceImportErrorModel] = Field(default=[], description="错误列表")
    data: List[BottleMaintenanceResponseModel] = Field(default=[], description="成功导入的数据")
