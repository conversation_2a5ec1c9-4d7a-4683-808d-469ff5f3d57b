-- 为采样任务分组表添加状态字段
-- 创建时间: 2024-12-17
-- 描述: 为sampling_task_group表添加独立的status字段，用于管理每个分组的执行状态

-- 添加状态字段
ALTER TABLE sampling_task_group 
ADD COLUMN status INT DEFAULT 0 COMMENT '分组状态：0-待执行，1-执行中，2-已完成';

-- 创建状态字段索引
CREATE INDEX idx_sampling_task_group_status ON sampling_task_group(status);

-- 更新现有数据的状态
-- 根据关联的采样任务状态来初始化分组状态
UPDATE sampling_task_group stg
JOIN sampling_task st ON stg.sampling_task_id = st.id
SET stg.status = st.status
WHERE stg.status = 0;

-- 添加注释说明
ALTER TABLE sampling_task_group MODIFY COLUMN status INT DEFAULT 0 COMMENT '分组状态：0-待执行，1-执行中，2-已完成';
