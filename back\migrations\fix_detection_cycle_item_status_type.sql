-- 修复检测周期条目表status字段类型
-- 创建时间: 2025-06-29
-- 描述: 将detection_cycle_item表的status字段从varchar类型修改为int类型，以匹配实体类定义

-- 1. 首先备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS detection_cycle_item_backup AS 
SELECT * FROM detection_cycle_item;

-- 2. 更新现有的字符串状态值为对应的数字值
UPDATE detection_cycle_item 
SET status = CASE 
    WHEN status = 'unassigned' THEN '0'
    WHEN status = 'assigned' THEN '1'
    WHEN status = 'completed' THEN '2'
    ELSE '0'
END
WHERE status IN ('unassigned', 'assigned', 'completed');

-- 3. 修改字段类型为int
ALTER TABLE detection_cycle_item 
MODIFY COLUMN status int(11) NOT NULL DEFAULT 0 COMMENT '状态：0-未分配，1-已分配，2-已完成';

-- 4. 更新索引（如果需要的话）
-- 索引应该会自动适应新的数据类型

-- 5. 验证数据迁移
-- 可以通过以下查询验证迁移是否成功：
-- SELECT status, COUNT(*) FROM detection_cycle_item GROUP BY status;

-- 迁移完成后可以删除备份表（可选）
-- DROP TABLE IF EXISTS detection_cycle_item_backup;