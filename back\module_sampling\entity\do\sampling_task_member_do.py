from sqlalchemy import Column, BigInteger, ForeignKey, DateTime, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from config.database import Base


class SamplingTaskMember(Base):
    """采样任务组员表"""
    __tablename__ = 'sampling_task_member'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, nullable=False, comment='采样任务ID')
    user_id = Column(BigInteger, nullable=False, comment='组员用户ID')

    # 审计字段
    create_by = Column(BigInteger, comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义 - 暂时注释掉，避免外键约束问题
    # sampling_task = relationship("SamplingTask", back_populates="task_members")
    # user = relationship("SysUser", foreign_keys=[user_id])
    
    # 创建人和更新人关系 - 暂时注释掉，避免外键约束问题
    # creator = relationship("SysUser", foreign_keys=[create_by])
    # updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引和约束
    __table_args__ = (
        Index('idx_sampling_task_member_task_id', 'sampling_task_id'),
        Index('idx_sampling_task_member_user_id', 'user_id'),
        {'comment': '采样任务组员表'}
    )
    
    def __repr__(self):
        return f"<SamplingTaskMember(id={self.id}, sampling_task_id={self.sampling_task_id}, user_id={self.user_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'user_id': self.user_id,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
