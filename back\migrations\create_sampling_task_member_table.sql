-- 创建采样任务组员表
CREATE TABLE IF NOT EXISTS `sampling_task_member` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `sampling_task_id` BIGINT NOT NULL COMMENT '采样任务ID',
    `user_id` BIGINT NOT NULL COMMENT '组员用户ID',
    `create_by` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` BIGINT COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `sys_user`(`user_id`) ON DELETE CASCADE,
    FOREIGN KEY (`create_by`) REFERENCES `sys_user`(`user_id`) ON DELETE SET NULL,
    FOREI<PERSON><PERSON> KEY (`update_by`) REFERENCES `sys_user`(`user_id`) ON DELETE SET NULL,
    
    -- 唯一约束：同一任务中同一用户只能有一条记录
    UNIQUE KEY `uk_task_user` (`sampling_task_id`, `user_id`),
    
    -- 索引
    INDEX `idx_sampling_task_member_task_id` (`sampling_task_id`),
    INDEX `idx_sampling_task_member_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务组员表';
