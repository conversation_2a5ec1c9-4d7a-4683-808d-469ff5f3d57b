from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport
from module_quotation.entity.vo.project_quotation_customer_support_vo import (
    ProjectQuotationCustomerSupportModel,
    AddProjectQuotationCustomerSupportModel,
    ProjectQuotationCustomerSupportQueryModel
)
from module_quotation.dao.project_quotation_customer_support_dao import ProjectQuotationCustomerSupportDao
from module_admin.dao.user_dao import UserDao
from utils.common_util import CamelCaseUtil
from exceptions.exception import ServiceException


class ProjectQuotationCustomerSupportService:
    """
    项目报价客服服务层
    """

    @classmethod
    async def get_project_quotation_customer_supports(cls, query_db: AsyncSession, project_quotation_id: int) -> List[ProjectQuotationCustomerSupportModel]:
        """
        获取项目报价客服列表
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 客服列表
        """
        customer_supports = await ProjectQuotationCustomerSupportDao.get_customer_supports_by_quotation_id(query_db, project_quotation_id)
        
        result = []
        for customer_support in customer_supports:
            # 获取用户信息
            user_detail = await UserDao.get_user_detail_by_id(query_db, customer_support.user_id)
            if user_detail and user_detail.get('user_basic_info'):
                user = user_detail['user_basic_info']
                customer_support_model = ProjectQuotationCustomerSupportModel(
                    id=customer_support.id,
                    project_quotation_id=customer_support.project_quotation_id,
                    user_id=customer_support.user_id,
                    user_name=user.user_name,
                    nick_name=user.nick_name,
                    create_time=customer_support.create_time
                )
                result.append(customer_support_model)
        
        return result

    @classmethod
    async def add_project_quotation_customer_supports(cls, query_db: AsyncSession, add_model: AddProjectQuotationCustomerSupportModel, current_user: str) -> bool:
        """
        添加项目报价客服
        
        :param query_db: 数据库会话
        :param add_model: 添加客服模型
        :param current_user: 当前用户
        :return: 是否成功
        """
        try:
            # 先删除原有的客服
            await ProjectQuotationCustomerSupportDao.delete_customer_supports_by_quotation_id(query_db, add_model.project_quotation_id)
            
            # 添加新的客服
            for user_id in add_model.user_ids:
                # 验证用户是否存在
                user_detail = await UserDao.get_user_detail_by_id(query_db, user_id)
                if not user_detail or not user_detail.get('user_basic_info'):
                    raise ServiceException(message=f"用户ID {user_id} 不存在")
                
                customer_support = ProjectQuotationCustomerSupport(
                    project_quotation_id=add_model.project_quotation_id,
                    user_id=user_id,
                    create_by=current_user,
                    update_by=current_user
                )
                await ProjectQuotationCustomerSupportDao.add_customer_support(query_db, customer_support)
            
            return True
        except Exception as e:
            raise ServiceException(message=f"添加项目报价客服失败: {str(e)}")

    @classmethod
    async def delete_project_quotation_customer_supports(cls, query_db: AsyncSession, project_quotation_id: int) -> bool:
        """
        删除项目报价客服
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 是否成功
        """
        try:
            return await ProjectQuotationCustomerSupportDao.delete_customer_supports_by_quotation_id(query_db, project_quotation_id)
        except Exception as e:
            raise ServiceException(message=f"删除项目报价客服失败: {str(e)}")

    @classmethod
    async def get_customer_support_ids_by_quotation_id(cls, query_db: AsyncSession, project_quotation_id: int) -> List[int]:
        """
        根据项目报价ID获取客服ID列表
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 客服ID列表
        """
        return await ProjectQuotationCustomerSupportDao.get_customer_support_ids_by_quotation_id(query_db, project_quotation_id)