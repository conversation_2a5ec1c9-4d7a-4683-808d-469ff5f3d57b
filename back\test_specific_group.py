#!/usr/bin/env python3
"""
测试特定分组的编号显示
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_specific_group():
    """测试特定分组的编号显示"""
    
    print("🔍 测试特定分组的编号显示...")
    
    # 使用我们知道存在的分组ID
    group_id = 27
    
    print(f"\n1. 获取分组 {group_id} 的详情:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/{group_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            group_detail = data.get('data', {})
            
            print(f"   分组详情:")
            print(f"     ID: {group_detail.get('id')}")
            print(f"     分组编号: {group_detail.get('groupCode')}")
            print(f"     任务编号: {group_detail.get('taskCode')}")
            print(f"     任务名称: {group_detail.get('taskName')}")
            print(f"     项目名称: {group_detail.get('projectName')}")
            print(f"     客户名称: {group_detail.get('customerName')}")
            print(f"     周期序号: {group_detail.get('cycleNumber')}")
            print(f"     周期类型: {group_detail.get('cycleType')}")
            print(f"     检测类别: {group_detail.get('detectionCategory')}")
            print(f"     点位名称: {group_detail.get('pointName')}")
            
            # 验证前端显示逻辑
            print(f"\n2. 验证前端显示逻辑:")
            
            # PC端采样管理弹窗应该显示的内容
            print(f"   PC端采样管理弹窗应该显示:")
            print(f"     分组编号: {group_detail.get('groupCode', '未设置')}")
            print(f"     任务名称: {group_detail.get('taskName', '-')}")
            print(f"     项目名称: {group_detail.get('projectName', '-')}")
            print(f"     客户名称: {group_detail.get('customerName', '-')}")
            print(f"     周期序号: {group_detail.get('cycleNumber', '-')}")
            print(f"     周期类型: {group_detail.get('cycleType', '-')}")
            print(f"     检测类别: {group_detail.get('detectionCategory', '-')}")
            print(f"     点位名称: {group_detail.get('pointName', '-')}")
            
            # H5页面应该显示的内容
            print(f"\n   H5页面头部应该显示:")
            print(f"     分组编号: {group_detail.get('groupCode', '未设置')}")
            print(f"     任务状态: {group_detail.get('status', 0)}")
            
            print(f"\n   H5页面基础信息应该显示:")
            print(f"     分组编号: {group_detail.get('groupCode', '-')}")
            print(f"     任务名称: {group_detail.get('taskName', '-')}")
            print(f"     项目名称: {group_detail.get('projectName', '-')}")
            print(f"     客户名称: {group_detail.get('customerName', '-')}")
            print(f"     周期序号: {group_detail.get('cycleNumber', '-')}")
            print(f"     周期类型: {group_detail.get('cycleType', '-')}")
            print(f"     检测类别: {group_detail.get('detectionCategory', '-')}")
            print(f"     点位名称: {group_detail.get('pointName', '-')}")
            
            # 检查分组编号是否存在
            group_code = group_detail.get('groupCode')
            if group_code:
                print(f"\n3. 分组编号验证:")
                print(f"   ✅ 分组编号存在: {group_code}")
                print(f"   ✅ 前端修改后应该显示: {group_code}")
                print(f"   ✅ 修改前显示的是任务编号: {group_detail.get('taskCode', '无')}")
                
                # 对比修改前后的显示内容
                print(f"\n4. 修改前后对比:")
                print(f"   修改前 - PC端采样管理弹窗显示: 任务编号: {group_detail.get('taskCode', '无')}")
                print(f"   修改后 - PC端采样管理弹窗显示: 分组编号: {group_code}")
                print(f"   修改前 - H5页面头部显示: 任务编号: {group_detail.get('taskCode', '无')}")
                print(f"   修改后 - H5页面头部显示: 分组编号: {group_code}")
                print(f"   修改前 - H5页面基础信息显示: 任务编号: {group_detail.get('taskCode', '无')}")
                print(f"   修改后 - H5页面基础信息显示: 分组编号: {group_code}")
                
                return True
            else:
                print(f"\n3. 分组编号验证:")
                print(f"   ❌ 分组编号不存在或为空")
                print(f"   ❌ 前端可能显示空白或'-'")
                print(f"   ⚠️  需要检查分组编号生成逻辑")
                
                return False
        else:
            print(f"   ❌ 获取分组详情失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_specific_group()
    
    if success:
        print("\n🎉 分组编号显示功能修改成功！")
        print("\n📋 修改总结:")
        print("   1. PC端采样管理弹窗:")
        print("      - 修改前: 显示'任务编号: {{ currentAssignment.taskCode }}'")
        print("      - 修改后: 显示'分组编号: {{ currentAssignment.groupCode }}'")
        print("\n   2. H5页面头部:")
        print("      - 修改前: 显示'任务编号: {{ taskData.taskCode }}'")
        print("      - 修改后: 显示'分组编号: {{ taskData.groupCode }}'")
        print("\n   3. H5页面基础信息:")
        print("      - 修改前: 显示'任务编号: {{ taskData.taskCode }}'")
        print("      - 修改后: 显示'分组编号: {{ taskData.groupCode }}'")
        print("\n✨ 用户现在可以在采样管理界面看到正确的分组编号！")
    else:
        print("\n💥 分组编号显示功能测试失败")
        print("   - 请检查分组编号字段是否正确生成")
        print("   - 请检查后端API返回的数据结构")
