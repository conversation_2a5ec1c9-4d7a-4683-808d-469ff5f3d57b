"""
样品记录控制器
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dto.sample_record_dto import (
    SampleRecordCreateDTO, SampleRecordUpdateDTO, SampleRecordDTO,
    SampleRecordQueryDTO, SampleRecordBatchCreateDTO
)
from utils.response_util import ResponseUtil
from utils.common_util import CamelCaseUtil

# 创建路由器
router = APIRouter(prefix="/sampling/sample-records", tags=["样品记录管理"])

# 日志记录器
# logger = LoggerUtil.get_logger(__name__)


@router.post("/create", summary="创建样品记录")
async def create_sample_record(
    create_dto: SampleRecordCreateDTO = Body(..., description="样品记录创建信息"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    创建样品记录
    
    Args:
        create_dto: 样品记录创建信息
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        # logger.info(f"用户 {current_user.user.user_id} 开始创建样品记录")

        service = SampleRecordService(db)
        result = await service.create_sample_record(create_dto, current_user.user.user_id)

        # logger.info(f"样品记录创建成功，ID: {result.id}")
        return ResponseUtil.success(data=result, msg="样品记录创建成功")

    except Exception as e:
        # logger.error(f"创建样品记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))





@router.post("/generate/group/{group_id}", summary="为任务分组生成样品记录")
async def generate_sample_records_for_group(
    group_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    为任务分组生成样品记录

    Args:
        group_id: 任务分组ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        生成结果
    """
    try:
        # logger.info(f"用户 {current_user.user.user_id} 开始为任务分组 {group_id} 生成样品记录")

        service = SampleRecordService(db)
        results = await service.generate_sample_records_for_group(group_id, current_user.user.user_id)

        # logger.info(f"样品记录生成成功，共生成 {len(results)} 条记录")
        return ResponseUtil.success(data=results, msg=f"样品记录生成成功，共生成 {len(results)} 条记录")

    except Exception as e:
        # logger.error(f"生成样品记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))





@router.get("/group/{group_id}", summary="获取任务分组的样品记录列表")
async def get_sample_records_by_group(
    group_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取任务分组的样品记录列表

    Args:
        group_id: 任务分组ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        样品记录列表
    """
    try:
        service = SampleRecordService(db)
        records = await service.get_sample_records_by_group(group_id)

        # 将字段名从蛇形命名转换为驼峰命名
        camel_records = CamelCaseUtil.transform_result(records)

        return ResponseUtil.success(data=camel_records)

    except Exception as e:
        import traceback
        error_detail = f"获取样品记录列表失败: {str(e)}\n{traceback.format_exc()}"
        print(error_detail)  # 临时调试用
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/status/{record_id}", summary="更新样品记录状态")
async def update_sample_record_status(
    record_id: int,
    status: int = Body(..., description="目标状态"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    更新样品记录状态
    
    Args:
        record_id: 样品记录ID
        status: 目标状态
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        # logger.info(f"用户 {current_user.user.user_id} 开始更新样品记录 {record_id} 状态为 {status}")

        service = SampleRecordService(db)
        result = await service.update_sample_record_status(record_id, status, current_user.user.user_id)

        if result:
            # logger.info(f"样品记录状态更新成功")
            return ResponseUtil.success(msg="样品记录状态更新成功")
        else:
            return ResponseUtil.error(msg="样品记录不存在或更新失败")

    except Exception as e:
        # logger.error(f"更新样品记录状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{record_id}", summary="获取样品记录详情")
async def get_sample_record_detail(
    record_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取样品记录详情
    
    Args:
        record_id: 样品记录ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品记录详情
    """
    try:
        service = SampleRecordService(db)
        record = await service.sample_record_dao.get_sample_record_by_id(record_id)
        
        if not record:
            return ResponseUtil.error(msg="样品记录不存在")
        
        result = await service._convert_to_dto(record)
        
        # 将字段名从蛇形命名转换为驼峰命名
        camel_result = CamelCaseUtil.transform_result(result)
        
        return ResponseUtil.success(data=camel_result)

    except Exception as e:
        # logger.error(f"获取样品记录详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{record_id}", summary="删除样品记录")
async def delete_sample_record(
    record_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    删除样品记录
    
    Args:
        record_id: 样品记录ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        # logger.info(f"用户 {current_user.user.user_id} 开始删除样品记录 {record_id}")

        service = SampleRecordService(db)
        result = await service.sample_record_dao.delete_sample_record(record_id)

        if result:
            await db.commit()
            # logger.info(f"样品记录删除成功")
            return ResponseUtil.success(msg="样品记录删除成功")
        else:
            return ResponseUtil.error(msg="样品记录不存在或删除失败")

    except Exception as e:
        await db.rollback()
        # logger.error(f"删除样品记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



