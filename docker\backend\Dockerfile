# 使用官方 Python 3.12 镜像作为基础镜像
FROM docker.xuanyuan.me/python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV TZ=Asia/Shanghai

# 设置 APT 镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs vf_admin/upload_path vf_admin/download_path vf_admin/gen_path

# 设置权限
RUN chmod +x app.py

# 创建生产环境配置文件
RUN if [ ! -f .env.prod ]; then \
    cp .env.dev .env.prod && \
    sed -i "s/APP_ENV = 'dev'/APP_ENV = 'prod'/" .env.prod && \
    sed -i "s/APP_RELOAD = true/APP_RELOAD = false/" .env.prod && \
    sed -i "s/DB_HOST = '127.0.0.1'/DB_HOST = 'mysql'/" .env.prod && \
    sed -i "s/REDIS_HOST = '127.0.0.1'/REDIS_HOST = 'redis'/" .env.prod; \
    fi

# 暴露端口
EXPOSE 9099

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9099/health || exit 1

# 启动命令
CMD ["python", "app.py", "--env=prod"]
