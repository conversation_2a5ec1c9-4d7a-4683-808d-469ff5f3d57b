from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, BigInteger, String, ForeignKey
from config.database import Base


class SysDeptLeader(Base):
    """
    部门负责人关联表
    """

    __tablename__ = 'sys_dept_leader'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    dept_id = Column(Integer, ForeignKey('sys_dept.dept_id'), nullable=False, comment='部门ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    create_by = Column(String(64), nullable=True, default='', comment='创建者')
    create_time = Column(DateTime, nullable=True, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), nullable=True, default='', comment='更新者')
    update_time = Column(DateTime, nullable=True, default=datetime.now(), comment='更新时间')