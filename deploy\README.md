# 部署脚本说明

本目录包含了项目部署相关的脚本和配置文件，用于简化项目的部署和维护过程。

## 脚本列表

1. **init_env.sh** - 初始化部署环境
   - 安装必要的软件包（Python、Nginx、Supervisor等）
   - 配置数据库和Redis
   - 创建应用目录和日志目录
   - 配置防火墙

2. **deploy_backend.sh** - 部署后端
   - 创建Python虚拟环境
   - 安装Python依赖
   - 配置环境变量
   - 运行数据库迁移
   - 生成Supervisor配置

3. **deploy_frontend.sh** - 部署前端
   - 安装Node.js依赖
   - 构建前端代码
   - 生成Nginx配置

4. **update_code.sh** - 一键更新代码
   - 拉取最新代码
   - 更新后端（依赖、迁移、重启服务）
   - 更新前端（依赖、构建）

5. **backup.sh** - 备份数据
   - 备份数据库
   - 备份上传文件
   - 自动清理旧备份

6. **install_ssl.sh** - 安装SSL证书
   - 使用Let's Encrypt安装SSL证书
   - 配置Nginx使用HTTPS
   - 设置证书自动续期

## 配置文件

1. **lims-backend.conf** - Supervisor配置文件（由deploy_backend.sh生成）
2. **lims-nginx.conf** - Nginx配置文件（由deploy_frontend.sh生成）
3. **nginx_ssl.conf** - 带SSL的Nginx配置模板

## 使用方法

### 初次部署

1. 初始化环境：
   ```bash
   sudo ./init_env.sh
   ```

2. 部署后端：
   ```bash
   ./deploy_backend.sh
   ```

3. 部署前端：
   ```bash
   ./deploy_frontend.sh
   ```

4. 安装Supervisor配置：
   ```bash
   sudo cp lims-backend.conf /etc/supervisor/conf.d/
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start lims-backend
   ```

5. 安装Nginx配置：
   ```bash
   sudo cp lims-nginx.conf /etc/nginx/conf.d/lims.conf
   sudo nginx -t
   sudo systemctl restart nginx
   ```

6. （可选）安装SSL证书：
   ```bash
   sudo ./install_ssl.sh
   ```

### 更新代码

```bash
./update_code.sh
```

### 备份数据

```bash
./backup.sh
```

## 注意事项

1. 所有脚本都是交互式的，会提示您输入必要的配置信息
2. 部分脚本需要root权限运行（如init_env.sh和install_ssl.sh）
3. 请根据实际情况修改生成的配置文件
4. 建议定期运行backup.sh脚本备份数据
5. 更新代码前建议先备份数据

## 目录结构

```
deploy/
├── README.md           # 本说明文件
├── init_env.sh         # 初始化环境脚本
├── deploy_backend.sh   # 后端部署脚本
├── deploy_frontend.sh  # 前端部署脚本
├── update_code.sh      # 代码更新脚本
├── backup.sh           # 数据备份脚本
├── install_ssl.sh      # SSL证书安装脚本
├── nginx_ssl.conf      # SSL的Nginx配置模板
├── lims-backend.conf   # Supervisor配置（生成的）
└── lims-nginx.conf     # Nginx配置（生成的）
```
