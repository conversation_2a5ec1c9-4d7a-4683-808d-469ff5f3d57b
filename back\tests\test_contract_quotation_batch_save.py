"""
测试合同关联报价批量保存功能
"""

import pytest
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from config.database import get_async_db
from module_contract.service.contract_quotation_relation_service import ContractQuotationRelationService
from module_contract.entity.vo.contract_quotation_relation_vo import ContractQuotationRelationUpdateModel


class TestContractQuotationBatchSave:
    """合同关联报价批量保存测试"""

    @pytest.fixture
    async def db_session(self):
        """获取数据库会话"""
        async_db = get_async_db()
        async with async_db() as db:
            yield db

    async def test_batch_save_quotation_relations(self, db_session: AsyncSession):
        """测试批量保存合同关联报价单"""
        service = ContractQuotationRelationService(db_session)
        
        # 假设有一个测试合同ID
        test_contract_id = 1
        
        try:
            # 模拟前端传来的项目编号列表（全量保存）
            test_project_codes = ["PRJ001", "PRJ002", "PRJ003"]
            
            update_model = ContractQuotationRelationUpdateModel(
                projectCodes=test_project_codes
            )
            
            # 模拟当前用户
            from module_admin.entity.vo.user_vo import CurrentUserModel
            current_user = CurrentUserModel(
                user=type('User', (), {
                    'user_name': 'test_user',
                    'nick_name': '测试用户'
                })()
            )
            
            # 执行批量保存
            result = await service.update_contract_quotation_relations(
                test_contract_id, update_model, current_user
            )
            
            print(f"批量保存测试结果: {result}")
            
            # 验证保存后的数据
            relation_result = await service.get_contract_quotation_relations(test_contract_id)
            print(f"保存后的关联数据数量: {relation_result.total}")
            
        except Exception as e:
            print(f"批量保存测试失败: {str(e)}")
            # 在测试环境中，可能没有相关数据，这是正常的

    async def test_quotation_relation_workflow(self, db_session: AsyncSession):
        """测试合同关联报价单的完整工作流程"""
        service = ContractQuotationRelationService(db_session)
        
        test_contract_id = 1
        
        try:
            # 1. 查询初始状态
            initial_result = await service.get_contract_quotation_relations(test_contract_id)
            initial_count = initial_result.total
            print(f"初始关联数量: {initial_count}")
            
            # 2. 模拟前端操作：添加新的项目关联
            new_project_codes = ["PRJ004", "PRJ005"]
            
            # 获取现有的项目编号
            existing_codes = [row['projectCode'] for row in initial_result.rows] if initial_result.rows else []
            
            # 合并现有和新增的项目编号（模拟前端的全量保存逻辑）
            all_project_codes = existing_codes + new_project_codes
            
            update_model = ContractQuotationRelationUpdateModel(
                projectCodes=all_project_codes
            )
            
            # 模拟当前用户
            from module_admin.entity.vo.user_vo import CurrentUserModel
            current_user = CurrentUserModel(
                user=type('User', (), {
                    'user_name': 'test_user',
                    'nick_name': '测试用户'
                })()
            )
            
            # 3. 执行全量保存
            save_result = await service.update_contract_quotation_relations(
                test_contract_id, update_model, current_user
            )
            print(f"全量保存结果: {save_result}")
            
            # 4. 验证保存后的数据
            final_result = await service.get_contract_quotation_relations(test_contract_id)
            final_count = final_result.total
            print(f"保存后关联数量: {final_count}")
            
            # 5. 模拟删除操作：移除一些项目
            remaining_codes = all_project_codes[:-1]  # 移除最后一个
            
            update_model_delete = ContractQuotationRelationUpdateModel(
                projectCodes=remaining_codes
            )
            
            delete_result = await service.update_contract_quotation_relations(
                test_contract_id, update_model_delete, current_user
            )
            print(f"删除操作结果: {delete_result}")
            
            # 6. 验证删除后的数据
            after_delete_result = await service.get_contract_quotation_relations(test_contract_id)
            after_delete_count = after_delete_result.total
            print(f"删除后关联数量: {after_delete_count}")
            
        except Exception as e:
            print(f"工作流程测试失败: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    async def run_test():
        test_instance = TestContractQuotationBatchSave()
        async_db = get_async_db()
        async with async_db() as db:
            await test_instance.test_batch_save_quotation_relations(db)
            await test_instance.test_quotation_relation_workflow(db)
    
    asyncio.run(run_test())
