from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from module_admin.entity.do.dept_leader_do import SysDeptLeader


class DeptLeaderDao:
    """
    部门负责人关联模块数据库操作层
    """

    @classmethod
    async def get_leaders_by_dept_id(cls, db: AsyncSession, dept_id: int) -> List[int]:
        """
        根据部门ID获取负责人ID列表

        :param db: orm对象
        :param dept_id: 部门ID
        :return: 负责人ID列表
        """
        result = await db.execute(
            select(SysDeptLeader.user_id).where(SysDeptLeader.dept_id == dept_id)
        )
        return [row[0] for row in result.fetchall()]

    @classmethod
    async def get_dept_ids_by_leader(cls, db: AsyncSession, user_id: int) -> List[int]:
        """
        根据用户ID获取其负责的部门ID列表

        :param db: orm对象
        :param user_id: 用户ID
        :return: 部门ID列表
        """
        result = await db.execute(
            select(SysDeptLeader.dept_id).where(SysDeptLeader.user_id == user_id)
        )
        return [row[0] for row in result.fetchall()]

    @classmethod
    async def add_dept_leaders(cls, db: AsyncSession, dept_id: int, leader_ids: List[int], create_by: str = 'system'):
        """
        为部门添加负责人

        :param db: orm对象
        :param dept_id: 部门ID
        :param leader_ids: 负责人ID列表
        :param create_by: 创建者
        """
        for leader_id in leader_ids:
            dept_leader = SysDeptLeader(
                dept_id=dept_id,
                user_id=leader_id,
                create_by=create_by,
                update_by=create_by
            )
            db.add(dept_leader)

    @classmethod
    async def delete_dept_leaders_by_dept_id(cls, db: AsyncSession, dept_id: int):
        """
        删除部门的所有负责人关系
        
        :param db: 数据库会话
        :param dept_id: 部门ID
        """
        stmt = delete(SysDeptLeader).where(SysDeptLeader.dept_id == dept_id)
        await db.execute(stmt)

    @classmethod
    async def update_dept_leaders(cls, db: AsyncSession, dept_id: int, leader_ids: List[int], update_by: str = 'system'):
        """
        更新部门的负责人关系
        
        :param db: 数据库会话
        :param dept_id: 部门ID
        :param leader_ids: 新的负责人ID列表
        :param update_by: 更新者
        """
        # 先删除原有关系
        await cls.delete_dept_leaders_by_dept_id(db, dept_id)
        
        # 添加新的关系
        if leader_ids:
            await cls.add_dept_leaders(db, dept_id, leader_ids, update_by)

    @classmethod
    async def check_dept_leader_exists(cls, db: AsyncSession, dept_id: int, user_id: int) -> bool:
        """
        检查用户是否为指定部门的负责人

        :param db: orm对象
        :param dept_id: 部门ID
        :param user_id: 用户ID
        :return: 是否为负责人
        """
        result = await db.execute(
            select(SysDeptLeader).where(
                SysDeptLeader.dept_id == dept_id,
                SysDeptLeader.user_id == user_id
            )
        )
        return result.scalar() is not None