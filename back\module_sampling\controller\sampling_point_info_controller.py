"""
采样点位信息控制器
"""

from fastapi import APIRouter, Depends, Body, Path, Query, UploadFile, File, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_sampling.service.sampling_point_info_service import SamplingPointInfoService
from module_sampling.dto.sampling_point_info_dto import (
    SamplingPointInfoCreateDTO,
    SamplingPointInfoUpdateDTO,
    SamplingPointInfoDTO,
    SamplingPointInfoPhotoUploadDTO
)
from module_admin.annotation.log_annotation import Log
from config.enums import BusinessType
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.response_util import ResponseUtil
from utils.log_util import logger
from module_admin.service.common_service import CommonService


router = APIRouter(prefix="/sampling/point-info", tags=["采样点位信息管理"])


@router.post("/create", response_model=CrudResponseModel, summary="创建点位信息")
@Log(title='点位信息', business_type=BusinessType.INSERT)
async def create_point_info(
    request: Request,
    point_info_dto: SamplingPointInfoCreateDTO = Body(..., description="点位信息创建数据"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """创建点位信息"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.create_point_info(point_info_dto, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="创建点位信息成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/get/{point_info_id}", response_model=CrudResponseModel, summary="根据ID获取点位信息")
async def get_point_info_by_id(
    point_info_id: int = Path(..., description="点位信息ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据ID获取点位信息"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.get_point_info_by_id(point_info_id)
        if result:
            return ResponseUtil.success(data=result, msg="获取点位信息成功")
        else:
            return ResponseUtil.error(msg="点位信息不存在")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/group/{group_id}", response_model=CrudResponseModel, summary="根据分组ID获取点位信息")
async def get_point_info_by_group_id(
    group_id: int = Path(..., description="采样任务分组ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据分组ID获取点位信息"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.get_point_info_by_group_id(group_id)
        if result:
            return ResponseUtil.success(data=result, msg="获取点位信息成功")
        else:
            return ResponseUtil.success(data=None, msg="该分组暂无点位信息")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/update/{point_info_id}", response_model=CrudResponseModel, summary="更新点位信息")
@Log(title='点位信息', business_type=BusinessType.UPDATE)
async def update_point_info(
    point_info_id: int = Path(..., description="点位信息ID"),
    point_info_dto: SamplingPointInfoUpdateDTO = Body(..., description="点位信息更新数据"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """更新点位信息"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.update_point_info(point_info_id, point_info_dto, current_user.user.user_id)
        if result:
            return ResponseUtil.success(data=result, msg="更新点位信息成功")
        else:
            return ResponseUtil.error(msg="点位信息不存在")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.delete("/delete/{point_info_id}", response_model=CrudResponseModel, summary="删除点位信息")
@Log(title='点位信息', business_type=BusinessType.DELETE)
async def delete_point_info(
    point_info_id: int = Path(..., description="点位信息ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """删除点位信息"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.delete_point_info(point_info_id)
        if result:
            return ResponseUtil.success(msg="删除点位信息成功")
        else:
            return ResponseUtil.error(msg="点位信息不存在")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.post("/upload-photo", response_model=CrudResponseModel, summary="上传点位照片")
@Log(title='点位信息', business_type=BusinessType.IMPORT)
async def upload_point_photo(
    request: Request,
    file: UploadFile = File(..., description="照片文件"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """上传点位照片"""
    try:
        # 验证文件类型
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        file_extension = file.filename.split('.')[-1].lower()
        if f'.{file_extension}' not in allowed_extensions:
            return ResponseUtil.error(msg="不支持的文件类型，请上传图片文件")
        
        # 验证文件大小（限制为5MB）
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await file.read()
        if len(file_content) > max_size:
            return ResponseUtil.error(msg="文件大小不能超过5MB")
        
        # 重置文件指针
        await file.seek(0)
        
        # 使用通用上传服务
        upload_result = await CommonService.upload_service(request, file)
        
        if upload_result.is_success:
            return ResponseUtil.success(data=upload_result.result, msg="照片上传成功")
        else:
            return ResponseUtil.error(msg=upload_result.message)
            
    except Exception as e:
        logger.error(f"上传点位照片失败: {str(e)}")
        return ResponseUtil.error(msg=f"上传失败: {str(e)}")


@router.get("/task/{task_id}/points", response_model=CrudResponseModel, summary="根据任务ID获取所有点位信息")
async def get_point_info_list_by_task_id(
    task_id: int = Path(..., description="采样任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据任务ID获取所有相关的点位信息列表"""
    try:
        service = SamplingPointInfoService(db)
        result = await service.get_point_info_list_by_task_id(task_id)
        return ResponseUtil.success(data=result, msg="获取点位信息列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))
