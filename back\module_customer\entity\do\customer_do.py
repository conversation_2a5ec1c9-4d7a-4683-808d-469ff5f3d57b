from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from config.database import Base


class Customer(Base):
    """
    客户表（包含集团和公司）
    """

    __tablename__ = 'customer'

    customer_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='客户ID')
    customer_name = Column(String(100), nullable=False, unique=True, comment='客户名称')
    customer_level = Column(String(1), nullable=False, comment='客户级别（1集团 2公司）')
    parent_id = Column(BigInteger, ForeignKey('customer.customer_id'), nullable=True, comment='上级客户ID')
    customer_type = Column(String(20), nullable=False, comment='客户类型（政府/企业/园区）')
    customer_source = Column(String(50), nullable=False, comment='客户来源')
    province = Column(String(50), nullable=False, comment='省份')
    city = Column(String(50), nullable=False, comment='城市')
    district = Column(String(50), default=None, comment='区县')
    address = Column(String(255), default=None, comment='详细地址')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    del_flag = Column(String(1), default='0', comment='删除标志（0存在 2删除）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')
    remark = Column(String(500), default=None, comment='备注')
    customer_importance = Column(String(20), nullable=True, comment='客户重要性（重点客户/普通客户/潜在客户）')

    # 关联关系
    parent = relationship('Customer', remote_side=[customer_id], backref='children')
    contacts = relationship('CustomerContact', back_populates='customer')
    internal_managers = relationship('CustomerInternalManager', back_populates='customer')
    follow_records = relationship('CustomerFollowRecord', back_populates='customer')


class CustomerContact(Base):
    """
    客户联系人表
    """

    __tablename__ = 'customer_contact'

    contact_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='联系人ID')
    customer_id = Column(BigInteger, ForeignKey('customer.customer_id'), nullable=False, comment='客户ID')
    contact_name = Column(String(50), nullable=False, comment='联系人姓名')
    position = Column(String(50), default=None, comment='职务')
    phone = Column(String(20), default=None, comment='电话')
    wechat = Column(String(50), default=None, comment='微信')
    email = Column(String(100), default=None, comment='邮箱')
    is_primary = Column(String(1), default='0', comment='是否主要联系人（0否 1是）')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')
    remark = Column(String(500), default=None, comment='备注')

    # 关联关系
    customer = relationship('Customer', back_populates='contacts')


class CustomerInternalManager(Base):
    """
    客户内部负责人关联表
    """

    __tablename__ = 'customer_internal_manager'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='关联ID')
    customer_id = Column(BigInteger, ForeignKey('customer.customer_id'), nullable=False, comment='客户ID')
    user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment='内部负责人ID')
    is_primary = Column(String(1), default='0', comment='是否主要负责人（0否 1是）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')

    # 关联关系
    customer = relationship('Customer', back_populates='internal_managers')
    user = relationship('SysUser', foreign_keys=[user_id])
