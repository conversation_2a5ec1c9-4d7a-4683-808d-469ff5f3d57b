"""
测试样品记录生成功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from server import app

# 测试用的认证token
TEST_TOKEN = "test_token"
TEST_HEADERS = {"Authorization": f"Bearer {TEST_TOKEN}"}

def test_generate_sample_records():
    """测试生成样品记录"""
    client = TestClient(app)
    
    print("=== 测试生成样品记录功能 ===")
    
    # 测试为执行任务ID=1生成样品记录
    assignment_id = 1
    
    try:
        print(f"\n正在为执行任务 {assignment_id} 生成样品记录...")
        
        response = client.post(
            f"/sampling/sample-records/generate/{assignment_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {data}")
            
            if "data" in data and isinstance(data["data"], list):
                print(f"成功生成 {len(data['data'])} 条样品记录")
                
                # 显示生成的样品记录详情
                for i, record in enumerate(data["data"][:3]):  # 只显示前3条
                    print(f"\n样品记录 {i+1}:")
                    print(f"  - ID: {record.get('id')}")
                    print(f"  - 样品编号: {record.get('sample_number')}")
                    print(f"  - 点位名称: {record.get('point_name')}")
                    print(f"  - 样品类型: {record.get('sample_type')}")
                    print(f"  - 周期序号: {record.get('cycle_number')}")
                    print(f"  - 检测参数: {record.get('detection_parameter')}")
                    print(f"  - 检测方法: {record.get('detection_method')}")
                
                if len(data["data"]) > 3:
                    print(f"\n... 还有 {len(data['data']) - 3} 条记录")
            else:
                print("响应数据格式异常")
                
        elif response.status_code == 500:
            try:
                error_data = response.json()
                print(f"服务器错误: {error_data.get('detail', '未知错误')}")
            except:
                print(f"服务器错误: {response.text}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误响应: {response.text}")
                
    except Exception as e:
        print(f"测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_get_sample_records():
    """测试获取样品记录列表"""
    client = TestClient(app)
    
    print("\n=== 测试获取样品记录列表 ===")
    
    assignment_id = 1
    
    try:
        print(f"\n正在获取执行任务 {assignment_id} 的样品记录列表...")
        
        response = client.get(
            f"/sampling/sample-records/assignment/{assignment_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {data}")
            
            if "data" in data and isinstance(data["data"], list):
                print(f"找到 {len(data['data'])} 条样品记录")
                
                # 显示样品记录详情
                for i, record in enumerate(data["data"][:3]):  # 只显示前3条
                    print(f"\n样品记录 {i+1}:")
                    print(f"  - ID: {record.get('id')}")
                    print(f"  - 样品编号: {record.get('sample_number')}")
                    print(f"  - 点位名称: {record.get('point_name')}")
                    print(f"  - 状态: {record.get('status')}")
                
                if len(data["data"]) > 3:
                    print(f"\n... 还有 {len(data['data']) - 3} 条记录")
            else:
                print("响应数据格式异常")
                
        else:
            print(f"请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误响应: {response.text}")
                
    except Exception as e:
        print(f"测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_get_statistics():
    """测试获取样品记录统计"""
    client = TestClient(app)
    
    print("\n=== 测试获取样品记录统计 ===")
    
    assignment_id = 1
    
    try:
        print(f"\n正在获取执行任务 {assignment_id} 的样品记录统计...")
        
        response = client.get(
            f"/sampling/sample-records/statistics/assignment/{assignment_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"统计数据: {data}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误响应: {response.text}")
                
    except Exception as e:
        print(f"测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试样品记录管理功能...")
    
    # 1. 先测试获取现有的样品记录
    test_get_sample_records()
    
    # 2. 测试生成样品记录
    test_generate_sample_records()
    
    # 3. 再次获取样品记录，查看生成结果
    test_get_sample_records()
    
    # 4. 测试统计功能
    test_get_statistics()
    
    print("\n=== 所有测试完成 ===")
