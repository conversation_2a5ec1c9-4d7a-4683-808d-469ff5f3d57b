<template>
  <div class="h5-index">
    <!-- 头部 -->
    <div class="header">
      <h1>LIMS采样管理</h1>
      <div class="subtitle">实验室信息管理系统</div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-container">
      <div class="menu-grid">
        <!-- 扫码功能 -->
        <div class="menu-item" @click="showScanner = true">
          <div class="menu-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="menu-title">扫码采样</div>
          <div class="menu-desc">扫描二维码进入采样任务</div>
        </div>

        <!-- 执行任务 -->
        <div class="menu-item" @click="goToExecutionTasks">
          <div class="menu-icon">
            <el-icon><List /></el-icon>
          </div>
          <div class="menu-title">执行任务</div>
          <div class="menu-desc">查看我的执行任务</div>
        </div>

        <!-- 离线数据 -->
        <div class="menu-item" @click="showOfflineData">
          <div class="menu-icon">
            <el-icon><Download /></el-icon>
          </div>
          <div class="menu-title">离线数据</div>
          <div class="menu-desc">查看缓存的离线数据</div>
        </div>

        <!-- 设置 -->
        <div class="menu-item" @click="showSettings">
          <div class="menu-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="menu-title">设置</div>
          <div class="menu-desc">应用设置和缓存管理</div>
        </div>
      </div>
    </div>

    <!-- 快速访问 -->
    <div class="quick-access">
      <h3>快速访问</h3>
      <div class="recent-tasks">
        <div 
          v-for="task in recentTasks" 
          :key="task.id"
          class="task-card"
          @click="goToTask(task.id)"
        >
          <div class="task-info">
            <div class="task-name">{{ task.taskName }}</div>
            <div class="task-code">{{ task.taskCode }}</div>
          </div>
          <div class="task-status" :class="getStatusClass(task.status)">
            {{ getStatusLabel(task.status) }}
          </div>
        </div>
        
        <div v-if="recentTasks.length === 0" class="empty-tasks">
          <el-icon><Document /></el-icon>
          <p>暂无最近访问的任务</p>
        </div>
      </div>
    </div>

    <!-- 扫码弹窗 -->
    <el-dialog 
      v-model="showScanner" 
      title="扫码采样" 
      width="90%"
      :show-close="false"
      class="scanner-dialog"
    >
      <QRCodeScanner 
        :auto-start="true"
        @scan-result="handleScanResult"
        @scan-error="handleScanError"
      />
      <template #footer>
        <el-button @click="showScanner = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 离线数据弹窗 -->
    <el-dialog v-model="showOfflineDialog" title="离线数据" width="90%">
      <div class="offline-data">
        <div class="cache-info">
          <p>缓存大小: {{ formatCacheSize(cacheSize) }}</p>
          <p>缓存项目: {{ cacheCount }} 个</p>
        </div>
        <div class="cache-actions">
          <el-button type="danger" @click="clearCache">清空缓存</el-button>
          <el-button @click="refreshCacheInfo">刷新信息</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 设置弹窗 -->
    <el-dialog v-model="showSettingsDialog" title="设置" width="90%">
      <div class="settings">
        <div class="setting-item">
          <label>自动缓存数据</label>
          <el-switch v-model="autoCache" />
        </div>
        <div class="setting-item">
          <label>离线模式提醒</label>
          <el-switch v-model="offlineNotification" />
        </div>
        <div class="setting-item">
          <label>扫码后自动跳转</label>
          <el-switch v-model="autoRedirect" />
        </div>
      </div>
    </el-dialog>

    <!-- 底部信息 -->
    <div class="footer">
      <p>版本: v1.0.0</p>
      <p>© 2024 LIMS采样管理系统</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import {
  Search,
  List,
  Download,
  Setting,
  Document
} from '@element-plus/icons-vue'
import QRCodeScanner from '@/components/h5/QRCodeScanner.vue'
import { getCacheSize, clearAllCache, formatCacheSize } from '@/utils/cache'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const showScanner = ref(false)
const showOfflineDialog = ref(false)
const showSettingsDialog = ref(false)
const recentTasks = ref([])
const cacheSize = ref(0)
const cacheCount = ref(0)

// 设置项
const autoCache = ref(true)
const offlineNotification = ref(true)
const autoRedirect = ref(true)

// 方法
const goToExecutionTasks = () => {
  router.push('/execution-tasks-h5')
}

const goToTask = (taskId) => {
  router.push(`/sampling-task-h5?id=${taskId}`)
}

const showOfflineData = () => {
  refreshCacheInfo()
  showOfflineDialog.value = true
}

const showSettings = () => {
  showSettingsDialog.value = true
}

const handleScanResult = (result) => {
  showScanner.value = false
  
  // 解析二维码结果，提取任务ID
  try {
    // 假设二维码格式为: /sampling-task-h5?id=123
    const url = new URL(result, window.location.origin)
    const taskId = url.searchParams.get('id')
    
    if (taskId) {
      if (autoRedirect.value) {
        goToTask(taskId)
      } else {
        ElMessageBox.confirm(`是否跳转到任务 ${taskId}？`, '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          goToTask(taskId)
        }).catch(() => {
          // 用户取消
        })
      }
    } else {
      ElMessage.error('无效的二维码格式')
    }
  } catch (error) {
    ElMessage.error('二维码解析失败')
  }
}

const handleScanError = (error) => {
  ElMessage.error(`扫码失败: ${error}`)
}

const refreshCacheInfo = () => {
  cacheSize.value = getCacheSize()
  // 简单计算缓存项目数量
  cacheCount.value = Object.keys(localStorage).filter(key => 
    key.startsWith('lims_h5_')
  ).length
}

const clearCache = () => {
  ElMessageBox.confirm('确认清空所有缓存数据？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    clearAllCache()
    refreshCacheInfo()
    ElMessage.success('缓存已清空')
  }).catch(() => {
    // 用户取消
  })
}

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-pending'
    case 1: return 'status-active'
    case 2: return 'status-completed'
    default: return ''
  }
}

const getStatusLabel = (status) => {
  switch (status) {
    case 0: return '待执行'
    case 1: return '执行中'
    case 2: return '已完成'
    default: return '未知'
  }
}

// 生命周期
onMounted(() => {
  refreshCacheInfo()
  
  // 加载最近访问的任务（从localStorage获取）
  try {
    const recent = localStorage.getItem('lims_h5_recent_tasks')
    if (recent) {
      recentTasks.value = JSON.parse(recent)
    }
  } catch (error) {
    console.warn('加载最近任务失败:', error)
  }
})
</script>

<style scoped>
.h5-index {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 40px 20px 30px;
  text-align: center;
}

.header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.menu-container {
  padding: 20px;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.menu-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.menu-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  color: white;
  font-size: 24px;
}

.menu-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.menu-desc {
  font-size: 12px;
  color: #666;
}

.quick-access {
  padding: 0 20px 20px;
}

.quick-access h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
}

.recent-tasks {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.task-card:last-child {
  border-bottom: none;
}

.task-card:hover {
  background: #f8f9fa;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.task-code {
  font-size: 12px;
  color: #666;
}

.task-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-active {
  background: #f0f9ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.empty-tasks {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

.empty-tasks .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.scanner-dialog {
  border-radius: 12px;
}

.offline-data {
  text-align: center;
}

.cache-info {
  margin-bottom: 20px;
}

.cache-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

.cache-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.settings {
  padding: 10px 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  font-size: 14px;
  color: #333;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
}

.footer p {
  margin: 4px 0;
}
</style>
