#!/usr/bin/env python3
"""
添加类别代码字段到技术手册价格表的迁移脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from config.env import DataBaseConfig


async def migrate_category_code():
    """
    执行类别代码字段迁移
    """
    print("开始执行类别代码字段迁移...")
    
    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
        
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)
        
        # 迁移SQL语句
        check_field_sql = """
        SELECT COUNT(*) as field_exists
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'technical_manual_price' 
        AND COLUMN_NAME = 'category_code';
        """
        
        add_field_sql = """
        ALTER TABLE technical_manual_price 
        ADD COLUMN category_code VARCHAR(100) COMMENT '类别代码，关联技术手册类别表';
        """
        
        add_index_sql = """
        CREATE INDEX idx_technical_manual_price_category_code ON technical_manual_price(category_code);
        """
        
        verify_sql = """
        SELECT COUNT(*) as total_records
        FROM technical_manual_price;
        """
        
        async with engine.begin() as conn:
            # 检查字段是否已存在
            result = await conn.execute(text(check_field_sql))
            field_exists = result.fetchone()[0]
            
            if field_exists > 0:
                print("✅ category_code 字段已存在，跳过迁移")
                return True
            
            print("📝 添加 category_code 字段...")
            await conn.execute(text(add_field_sql))
            print("✅ category_code 字段添加成功")
            
            print("📝 添加索引...")
            try:
                await conn.execute(text(add_index_sql))
                print("✅ 索引添加成功")
            except Exception as e:
                if "Duplicate key name" in str(e):
                    print("⚠️  索引已存在，跳过")
                else:
                    print(f"⚠️  索引添加失败: {e}")
            
            print("📝 验证迁移结果...")
            result = await conn.execute(text(verify_sql))
            row = result.fetchone()
            total_records = row[0]
            
            print(f"✅ 迁移完成！")
            print(f"   总记录数: {total_records}")
            print("⚠️  注意：需要手动设置现有记录的category_code值")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def rollback_category_code():
    """
    回滚类别代码字段迁移
    """
    print("开始回滚类别代码字段迁移...")
    
    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
        
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)
        
        rollback_sql = """
        -- 删除索引
        DROP INDEX IF EXISTS idx_technical_manual_price_category_code ON technical_manual_price;
        
        -- 删除字段
        ALTER TABLE technical_manual_price DROP COLUMN IF EXISTS category_code;
        """
        
        async with engine.begin() as conn:
            await conn.execute(text(rollback_sql))
            print("✅ 回滚完成")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def update_category_codes():
    """
    更新现有记录的category_code值
    这需要根据实际业务逻辑来实现
    """
    print("开始更新现有记录的category_code值...")
    
    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
        
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)
        
        # 查询现有的技术手册类别
        query_categories_sql = """
        SELECT category_code, category_name 
        FROM technical_manual_category 
        ORDER BY category_code;
        """
        
        # 查询没有category_code的价格记录
        query_prices_sql = """
        SELECT id, method, remark
        FROM technical_manual_price 
        WHERE category_code IS NULL
        LIMIT 10;
        """
        
        async with engine.begin() as conn:
            # 查询可用的类别
            result = await conn.execute(text(query_categories_sql))
            categories = result.fetchall()
            
            print("可用的技术手册类别:")
            for category in categories:
                print(f"  {category[0]}: {category[1]}")
            
            # 查询需要更新的记录
            result = await conn.execute(text(query_prices_sql))
            prices = result.fetchall()
            
            print(f"\n需要更新category_code的记录数: {len(prices)}")
            for price in prices:
                print(f"  ID: {price[0]}, Method: {price[1]}, Remark: {price[2]}")
            
            print("\n⚠️  请手动执行以下SQL来设置category_code值:")
            print("UPDATE technical_manual_price SET category_code = '适当的类别代码' WHERE id = 记录ID;")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "rollback":
            success = await rollback_category_code()
        elif command == "update":
            success = await update_category_codes()
        else:
            success = await migrate_category_code()
    else:
        success = await migrate_category_code()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    print("技术手册价格表类别代码字段迁移工具")
    print("用法:")
    print("  python migrate_category_code.py        # 执行迁移")
    print("  python migrate_category_code.py rollback # 回滚迁移")
    print("  python migrate_category_code.py update   # 查看需要更新的记录")
    print()
    
    asyncio.run(main())
