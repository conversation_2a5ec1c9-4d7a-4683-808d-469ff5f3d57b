"""测试项目成单确认审批功能"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
from module_admin.entity.vo.user_vo import CurrentUserModel, UserModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport
from module_admin.entity.do.user_do import SysUser
from datetime import datetime


class TestProjectQuotationApprovalOrderConfirm:
    """项目成单确认审批测试类"""

    @pytest.mark.asyncio
    async def test_can_user_approve_order_confirm_as_creator(self, db_session: AsyncSession):
        """
        测试创建人是否可以审批项目成单确认
        """
        # 创建测试用户
        test_user = SysUser(
            user_id=999,
            user_name="test_user",
            nick_name="测试用户",
            email="<EMAIL>",
            phonenumber="***********",
            sex="1",
            status="0",
            del_flag="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(test_user)
        
        # 创建测试项目报价
        test_quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST_001",
            customer_name="测试客户",
            business_type="sampling",
            status="1",
            create_by="test_user",  # 使用用户名
            create_time=datetime.now()
        )
        db_session.add(test_quotation)
        await db_session.flush()
        
        # 创建项目成单确认审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=test_quotation.id,
            approver_type="order_confirm",
            approval_stage=2,
            approval_status="pending",
            is_required="1",
            approval_round=1,
            is_current_round="1",
            create_by=999,
            create_time=datetime.now()
        )
        db_session.add(approval_record)
        await db_session.commit()
        
        # 测试服务
        service = ProjectQuotationApprovalService(db_session)
        
        # 测试创建人是否可以审批
        can_approve = await service._can_user_approve_order_confirm(
            test_quotation.id, 
            test_user.user_id
        )
        
        assert can_approve is True, "创建人应该可以审批项目成单确认"

    @pytest.mark.asyncio
    async def test_can_user_approve_order_confirm_as_customer_service(self, db_session: AsyncSession):
        """
        测试客服是否可以审批项目成单确认
        """
        # 创建测试用户（创建人）
        creator_user = SysUser(
            user_id=998,
            user_name="creator_user",
            nick_name="创建用户",
            email="<EMAIL>",
            phonenumber="13800138001",
            sex="1",
            status="0",
            del_flag="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(creator_user)
        
        # 创建测试用户（客服）
        cs_user = SysUser(
            user_id=997,
            user_name="cs_user",
            nick_name="客服用户",
            email="<EMAIL>",
            phonenumber="***********",
            sex="1",
            status="0",
            del_flag="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cs_user)
        
        # 创建测试项目报价
        test_quotation = ProjectQuotation(
            project_name="测试项目2",
            project_code="TEST_002",
            customer_name="测试客户2",
            business_type="sampling",
            status="1",
            create_by="creator_user",  # 使用创建人用户名
            create_time=datetime.now()
        )
        db_session.add(test_quotation)
        await db_session.flush()
        
        # 创建项目客服关联
        customer_support = ProjectQuotationCustomerSupport(
            project_quotation_id=test_quotation.id,
            user_id=cs_user.user_id,
            create_by="creator_user",
            create_time=datetime.now()
        )
        db_session.add(customer_support)
        
        # 创建项目成单确认审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=test_quotation.id,
            approver_type="order_confirm",
            approval_stage=2,
            approval_status="pending",
            is_required="1",
            approval_round=1,
            is_current_round="1",
            create_by=998,
            create_time=datetime.now()
        )
        db_session.add(approval_record)
        await db_session.commit()
        
        # 测试服务
        service = ProjectQuotationApprovalService(db_session)
        
        # 测试客服是否可以审批
        can_approve = await service._can_user_approve_order_confirm(
            test_quotation.id, 
            cs_user.user_id
        )
        
        assert can_approve is True, "项目客服应该可以审批项目成单确认"
        
        # 测试创建人是否可以审批
        can_approve_creator = await service._can_user_approve_order_confirm(
            test_quotation.id, 
            creator_user.user_id
        )
        
        assert can_approve_creator is True, "创建人应该可以审批项目成单确认"

    @pytest.mark.asyncio
    async def test_get_pending_approvals_paginated_order_confirm(self, db_session: AsyncSession):
        """
        测试获取待项目成单确认审批的项目列表
        """
        # 创建测试用户
        test_user = SysUser(
            user_id=996,
            user_name="test_user_3",
            nick_name="测试用户3",
            email="<EMAIL>",
            phonenumber="***********",
            sex="1",
            status="0",
            del_flag="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(test_user)
        
        # 创建测试项目报价
        test_quotation = ProjectQuotation(
            project_name="测试项目3",
            project_code="TEST_003",
            customer_name="测试客户3",
            business_type="sampling",
            status="1",
            create_by="test_user_3",  # 使用用户名
            create_time=datetime.now()
        )
        db_session.add(test_quotation)
        await db_session.flush()
        
        # 创建市场审批记录（已通过）
        market_record = ProjectQuotationApprovalRecord(
            project_quotation_id=test_quotation.id,
            approver_type="market",
            approval_stage=1,
            approval_status="approved",
            is_required="1",
            approval_round=1,
            is_current_round="1",
            create_by=996,
            create_time=datetime.now()
        )
        db_session.add(market_record)
        
        # 创建项目成单确认审批记录（待审批）
        order_confirm_record = ProjectQuotationApprovalRecord(
            project_quotation_id=test_quotation.id,
            approver_type="order_confirm",
            approval_stage=2,
            approval_status="pending",
            is_required="1",
            approval_round=1,
            is_current_round="1",
            create_by=996,
            create_time=datetime.now()
        )
        db_session.add(order_confirm_record)
        await db_session.commit()
        
        # 创建当前用户模型
        current_user = CurrentUserModel(
            user=UserModel(
                user_id=test_user.user_id,
                user_name=test_user.user_name,
                nick_name=test_user.nick_name,
                email=test_user.email,
                phonenumber=test_user.phonenumber,
                sex=test_user.sex,
                status=test_user.status,
                del_flag=test_user.del_flag
            ),
            roles=[]
        )
        
        # 测试服务
        service = ProjectQuotationApprovalService(db_session)
        
        # 测试获取待审批列表
        query_params = {
            'page_num': 1,
            'page_size': 10
        }
        
        result = await service.get_pending_approvals_paginated(current_user, query_params)
        
        assert result is not None, "应该返回结果"
        assert 'list' in result, "结果应该包含list字段"
        assert 'total' in result, "结果应该包含total字段"
        
        # 检查是否包含我们创建的测试项目
        found_project = False
        for item in result['list']:
            if item['id'] == test_quotation.id:
                found_project = True
                assert item['approver_type'] == 'order_confirm', "审批类型应该是order_confirm"
                assert item['approval_stage'] == 2, "审批阶段应该是2"
                break
        
        assert found_project, "应该能找到待项目成单确认审批的项目"

    @pytest.mark.asyncio
    async def test_api_get_pending_approvals(self, async_client: AsyncClient):
        """
        测试API接口获取待审批列表
        """
        # 使用测试token跳过认证
        headers = {"Authorization": "Bearer test_token"}
        
        response = await async_client.get(
            "/quotation/project-quotation-approval/pending",
            headers=headers,
            params={
                "page_num": 1,
                "page_size": 10
            }
        )
        
        assert response.status_code == 200, f"API调用失败: {response.text}"
        
        data = response.json()
        assert data['code'] == 200, f"API返回错误: {data.get('msg', '')}"
        assert 'data' in data, "响应应该包含data字段"
        
        result = data['data']
        assert 'list' in result, "结果应该包含list字段"
        assert 'total' in result, "结果应该包含total字段"
        
        print(f"获取到 {result['total']} 个待审批项目")
        
        # 检查是否有待项目成单确认审批的项目
        order_confirm_projects = [
            item for item in result['list'] 
            if item.get('approver_type') == 'order_confirm'
        ]
        
        if order_confirm_projects:
            print(f"找到 {len(order_confirm_projects)} 个待项目成单确认审批的项目:")
            for project in order_confirm_projects:
                print(f"  - 项目ID: {project['id']}, 项目名称: {project['project_name']}")
        else:
            print("没有找到待项目成单确认审批的项目")