-- 添加采样执行任务查看所有权限
-- 为采样执行列表添加"所有执行任务"权限

-- 1. 查找采样执行菜单ID
SET @execution_menu_id = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE path = 'execution' 
    AND parent_id = (
        SELECT menu_id 
        FROM sys_menu 
        WHERE path = 'sampling' 
        AND parent_id = 0
    )
);

-- 2. 如果采样执行菜单不存在，先创建
INSERT IGNORE INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
    `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
    `status`, `perms`, `icon`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES (
    '采样执行', 
    (SELECT menu_id FROM sys_menu WHERE path = 'sampling' AND parent_id = 0), 
    4, 'execution', 'sampling/execution/index', 
    NULL, 1, 0, 'C', '0', 
    '0', 'sampling:execution:list', 'execution', 'admin', NOW(), 
    '', NULL, '采样执行菜单'
);

-- 3. 重新获取采样执行菜单ID
SET @execution_menu_id = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE path = 'execution' 
    AND parent_id = (
        SELECT menu_id 
        FROM sys_menu 
        WHERE path = 'sampling' 
        AND parent_id = 0
    )
);

-- 4. 添加"查看所有执行任务"权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
    `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
    `status`, `perms`, `icon`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES (
    '查看所有执行任务', @execution_menu_id, 1, '', '', 
    NULL, 1, 0, 'F', '0', 
    '0', 'assignment-execution:all', '#', 'admin', NOW(), 
    '', NULL, '查看所有执行任务权限'
);

-- 5. 为超级管理员角色分配权限（假设超级管理员角色ID为1）
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 1, menu_id 
FROM sys_menu 
WHERE perms = 'assignment-execution:all';

-- 6. 显示添加的权限信息
SELECT 
    m.menu_id,
    m.menu_name,
    m.perms,
    p.menu_name as parent_menu_name
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.perms = 'assignment-execution:all';
