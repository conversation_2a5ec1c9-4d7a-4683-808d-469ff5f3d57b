# 报价单导出方法-一览表功能说明

## 📋 功能概述

在报价单导出功能中新增了"方法-一览表"sheet的渲染功能，该功能会在导出报价单时自动生成一个包含所有检测方法汇总信息的工作表。

## 🎯 功能特性

### 1. 自动渲染方法-一览表
- **触发时机**：每次导出报价单时自动执行
- **数据来源**：从报价明细数据中提取检测方法信息
- **去重处理**：相同的检测方法（类别+项目+方法）只显示一次

### 2. 包含字段信息
| 字段名称 | 数据来源 | 说明 |
|---------|---------|------|
| 序号 | 自动生成 | 从1开始的连续序号 |
| 检测类别 | category | 检测项目的分类 |
| 检测项目 | parameter | 具体的检测参数 |
| 检测方法 | method | 检测使用的方法 |
| 是否盖章 | qualification_code查询技术手册 | 根据资质状态判断 |
| 是否分包 | is_subcontract | 是否为分包项目 |

### 3. 资质状态判断逻辑
- **有资质（盖章）**：`has_qualification = "0"` → 显示"是"
- **无资质（不盖章）**：`has_qualification = "1"` → 显示"否"
- **空资质编号**：默认显示"否"

## 🔧 技术实现

### 1. Excel模板引擎扩展
```python
# 在render_template方法中添加方法-一览表渲染
self._render_method_overview_sheet(wb, template_data)
```

### 2. 方法-一览表渲染逻辑
```python
def _render_method_overview_sheet(self, wb, template_data):
    """渲染方法-一览表sheet"""
    # 1. 检查模板中是否存在"方法-一览表"工作表
    # 2. 准备方法一览表数据（去重处理）
    # 3. 渲染表头和数据行
    # 4. 设置单元格样式和边框
```

### 3. 数据准备和去重
```python
def _prepare_method_overview_data(self, items):
    """准备方法-一览表数据"""
    # 使用set进行去重
    seen_methods = set()
    for item in items:
        method_key = (category, parameter, method)
        if method_key not in seen_methods:
            # 添加到结果列表
```

### 4. 资质信息查询
```python
async def _enrich_items_with_qualification_info(self, items):
    """为报价明细项目补充技术手册资质信息"""
    # 1. 收集所有qualification_code
    # 2. 批量查询技术手册表
    # 3. 构建资质映射表
    # 4. 为每个项目补充资质信息
```

## 📊 数据流程

### 1. 导出触发
```
用户点击导出 → 项目报价服务 → 准备模板数据 → Excel模板引擎
```

### 2. 资质查询流程
```
报价明细数据 → 提取qualification_code → 批量查询技术手册 → 构建资质映射 → 补充has_qualification字段
```

### 3. 方法-一览表生成流程
```
补充资质的明细数据 → 去重处理 → 生成方法一览数据 → 渲染到Excel工作表
```

## 🎨 样式设计

### 1. 表头样式
- **字体**：宋体，12号，加粗
- **对齐**：水平居中，垂直居中
- **边框**：四周细线边框

### 2. 数据行样式
- **字体**：宋体，11号，常规
- **对齐**：水平居中，垂直居中
- **边框**：四周细线边框

## 📝 使用说明

### 1. 模板文件要求
- 模板文件：`quotation_price_export_tpl_new.xlsx`
- 必须包含名为"方法-一览表"的工作表
- 建议在第2行设置表头，从第3行开始填充数据

### 2. 数据库字段要求
- **技术手册表**：必须包含`qualification_code`和`has_qualification`字段
- **报价明细**：必须包含`qualification_code`和`is_subcontract`字段

### 3. 导出操作
1. 在项目报价管理页面选择要导出的报价单
2. 点击"导出"按钮
3. 选择导出格式（Excel）
4. 系统自动生成包含方法-一览表的报价单文件

## 🔍 故障排除

### 1. 方法-一览表不显示
- **检查模板文件**：确认模板中存在"方法-一览表"工作表
- **检查数据**：确认报价明细数据不为空
- **查看日志**：检查后端日志是否有错误信息

### 2. 资质状态显示错误
- **检查数据库**：确认技术手册表中的资质数据正确
- **检查字段映射**：确认`qualification_code`字段正确关联
- **验证查询逻辑**：检查资质查询SQL是否正确

### 3. 数据重复显示
- **检查去重逻辑**：确认去重键值（类别+项目+方法）正确
- **验证数据源**：检查报价明细数据是否有重复记录

## 🚀 性能优化

### 1. 批量查询优化
- 使用`IN`查询批量获取资质信息，避免N+1查询问题
- 构建内存映射表，减少重复查询

### 2. 数据处理优化
- 使用set进行去重，时间复杂度O(1)
- 预先准备所有数据，减少Excel操作次数

### 3. 内存管理
- 及时释放不需要的数据结构
- 使用生成器处理大量数据

## 📈 扩展功能

### 1. 可配置字段
- 支持配置显示哪些字段
- 支持自定义字段顺序
- 支持字段别名设置

### 2. 样式定制
- 支持自定义表头样式
- 支持条件格式化（如资质状态用颜色区分）
- 支持自定义列宽

### 3. 数据统计
- 添加统计行（总计、有资质数量等）
- 支持分类统计
- 支持图表展示

## 📞 技术支持

### 相关文件
- **Excel模板引擎**：`utils/excel_template_engine.py`
- **项目报价服务**：`module_quotation/service/project_quotation_service.py`
- **技术手册模型**：`module_basedata/entity/do/technical_manual_do.py`

### 关键方法
- `_render_method_overview_sheet()` - 方法-一览表渲染
- `_prepare_method_overview_data()` - 数据准备和去重
- `_enrich_items_with_qualification_info()` - 资质信息补充
- `_check_qualification_status()` - 资质状态判断

### 调试建议
1. 开启详细日志记录
2. 检查模板文件结构
3. 验证数据库字段和数据
4. 测试小数据集导出功能
