# 报价审批功能测试说明

## 功能概述
新增的报价审批管理页面允许不同角色的用户查看和处理待审批的报价单。

## 测试角色
1. **市场审批人** (角色编码: market-approver)
   - 可以看到状态为 `pending_market` 的报价单
   - 可以对这些报价单进行审批操作

2. **实验室审批人** (角色编码: lab-approver)
   - 可以看到状态为 `pending_lab` 的报价单
   - 可以对这些报价单进行审批操作

3. **现场审批人** (角色编码: field-approver)
   - 可以看到状态为 `pending_field` 的报价单
   - 可以对这些报价单进行审批操作

## 审批流程

### 一般采样业务类型
1. 市场审批 → 实验室审批/现场审批（并行）
2. 所有审批完成后，报价单状态变为已审批

### 送样业务类型
1. 市场审批 → 实验室审批
2. 不需要现场审批

## 测试步骤

### 1. 权限测试
- 使用不同角色的用户登录系统
- 访问 `/quotation/project-quotation-approval/index` 页面
- 验证只有具有审批权限的用户才能访问

### 2. 列表显示测试
- 验证不同角色用户看到的待审批列表是否正确
- 市场审批人只能看到待市场审批的项目
- 实验室审批人只能看到待实验室审批的项目
- 现场审批人只能看到待现场审批的项目

### 3. 审批操作测试
- 点击"审批"按钮，打开审批弹框
- 查看项目基本信息是否正确显示
- 查看审批历史记录是否正确显示
- 填写审批意见，选择审批结果（通过/拒绝）
- 提交审批，验证操作是否成功

### 4. 查看报价测试
- 点击"查看报价"按钮
- 验证报价详情弹框是否正常显示

## API 接口

### 后端接口路径
- 获取审批状态: `GET /quotation/project-quotation-approval/status/{id}`
- 提交审批: `POST /quotation/project-quotation-approval/submit/{id}`
- 执行审批: `POST /quotation/project-quotation-approval/approve/{id}`
- 获取待审批列表: `GET /quotation/project-quotation-approval/pending`
- 初始化审批记录: `POST /quotation/project-quotation-approval/init/{id}`

### 前端API文件
- `/src/api/quotation/projectQuotationApproval.js`

## 页面文件
- 主页面: `/src/views/quotation/project-quotation-approval/index.vue`
- 审批弹框: `/src/views/quotation/project-quotation-approval/components/ApprovalDialog.vue`
- 路由配置: `/src/router/modules/projectQuotation.js`

## 注意事项
1. 确保用户具有正确的角色权限
2. 审批操作需要填写审批意见
3. 审批后页面会自动刷新列表
4. 不同业务类型的审批流程不同，需要分别测试