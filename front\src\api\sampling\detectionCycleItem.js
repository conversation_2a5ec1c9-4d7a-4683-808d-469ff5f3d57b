import request from '@/utils/request'

// 查询检测周期条目列表
export function listDetectionCycleItem(query) {
  return request({
    url: '/sampling/cycle-item/list',
    method: 'get',
    params: query
  })
}

// 查询检测周期条目详细
export function getDetectionCycleItem(itemId) {
  return request({
    url: '/sampling/cycle-item/' + itemId,
    method: 'get'
  })
}

// 根据项目报价ID查询检测周期条目
export function getCycleItemsByProjectQuotationId(projectQuotationId) {
  return request({
    url: '/sampling/cycle-item/project/' + projectQuotationId,
    method: 'get'
  })
}

// 查询未分配的检测周期条目
export function getUnassignedCycleItems(query) {
  return request({
    url: '/sampling/cycle-item/unassigned',
    method: 'get',
    params: query
  })
}

// 根据项目报价ID查询未分配的检测周期条目
export function getUnassignedCycleItemsByProjectId(projectQuotationId) {
  return request({
    url: '/sampling/cycle-item/project/' + projectQuotationId + '/unassigned',
    method: 'get'
  })
}

// 生成检测周期条目
export function generateCycleItems(projectQuotationId) {
  return request({
    url: '/sampling/cycle-item/generate/' + projectQuotationId,
    method: 'post'
  })
}

// 重新生成检测周期条目
export function regenerateCycleItems(projectQuotationId) {
  return request({
    url: '/sampling/cycle-item/regenerate/' + projectQuotationId,
    method: 'post'
  })
}

// 更新检测周期条目状态
export function updateCycleItemStatus(itemId, status) {
  return request({
    url: '/sampling/cycle-item/' + itemId + '/status',
    method: 'put',
    data: { status: status }
  })
}

// 批量更新检测周期条目状态
export function batchUpdateCycleItemStatus(itemIds, status) {
  return request({
    url: '/sampling/cycle-item/batch-status',
    method: 'put',
    data: { 
      itemIds: itemIds,
      status: status 
    }
  })
}

// 删除检测周期条目（根据项目报价ID）
export function deleteCycleItemsByProjectQuotationId(projectQuotationId) {
  return request({
    url: '/sampling/cycle-item/project/' + projectQuotationId,
    method: 'delete'
  })
}