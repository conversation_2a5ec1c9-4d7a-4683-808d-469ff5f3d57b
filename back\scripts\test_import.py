#!/usr/bin/env python3
"""
简化版客户导入测试脚本
"""

import asyncio
import os
import sys
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from config.database import AsyncSessionLocal


async def test_import():
    """测试导入"""
    print("开始测试导入...")
    
    # 读取Excel文件
    excel_file = "docs/客户示例.xlsx"
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        return
    
    df = pd.read_excel(excel_file)
    print(f"Excel文件加载成功，共{len(df)}行数据")
    
    # 检查数据库连接
    async with AsyncSessionLocal() as db:
        try:
            result = await db.execute(text("SELECT COUNT(*) as count FROM customer"))
            count = result.fetchone()
            print(f"当前客户数量: {count.count}")
            
            # 检查联系人数量
            result = await db.execute(text("SELECT COUNT(*) as count FROM customer_contact"))
            count = result.fetchone()
            print(f"当前联系人数量: {count.count}")
            
        except Exception as e:
            print(f"数据库查询失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_import())
