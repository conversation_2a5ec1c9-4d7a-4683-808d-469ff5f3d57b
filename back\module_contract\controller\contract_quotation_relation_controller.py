"""
合同关联报价单控制器
"""

from fastapi import APIRouter, Depends, Path
from typing import Dict, Any

from module_contract.service.contract_quotation_relation_service import ContractQuotationRelationService
from module_contract.entity.vo.contract_quotation_relation_vo import (
    ContractQuotationRelationCreateModel,
    ContractQuotationRelationUpdateModel,
    ContractQuotationRelationListModel
)
from utils.response_util import ResponseUtil
from utils.current_user import get_current_user, CurrentUserModel
from utils.log_util import LogUtil

# 创建路由
contract_quotation_relation_controller = APIRouter(prefix="/contract/quotation-relation", tags=["合同关联报价单"])

# 创建服务实例
contract_quotation_relation_service = ContractQuotationRelationService()

# 创建日志实例
logger = LogUtil(__name__)


@contract_quotation_relation_controller.get("/{contract_id}", response_model=Dict[str, Any])
async def get_contract_quotation_relations(
    contract_id: int = Path(..., description="合同ID"),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    查询合同关联的报价单列表
    
    Args:
        contract_id: 合同ID
        current_user: 当前用户
        
    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        logger.info(f"查询合同关联报价单列表，合同ID: {contract_id}")
        
        result = contract_quotation_relation_service.get_contract_quotation_relations(contract_id)
        
        logger.info(f"查询合同关联报价单列表成功，合同ID: {contract_id}, 数量: {result.total}")
        return ResponseUtil.success(data=result.model_dump())
        
    except Exception as e:
        logger.error(f"查询合同关联报价单列表失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(message=f"查询合同关联报价单列表失败: {str(e)}")


@contract_quotation_relation_controller.post("/{contract_id}", response_model=Dict[str, Any])
async def create_contract_quotation_relations(
    create_model: ContractQuotationRelationCreateModel,
    contract_id: int = Path(..., description="合同ID"),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    创建合同关联报价单
    
    Args:
        create_model: 创建模型
        contract_id: 合同ID
        current_user: 当前用户
        
    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        logger.info(f"创建合同关联报价单，合同ID: {contract_id}, 项目编号: {create_model.project_codes}")
        
        result = contract_quotation_relation_service.create_contract_quotation_relations(
            contract_id, create_model, current_user
        )
        
        if result:
            logger.info(f"创建合同关联报价单成功，合同ID: {contract_id}")
            return ResponseUtil.success(message="创建合同关联报价单成功")
        else:
            logger.error(f"创建合同关联报价单失败，合同ID: {contract_id}")
            return ResponseUtil.error(message="创建合同关联报价单失败")
            
    except Exception as e:
        logger.error(f"创建合同关联报价单失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(message=f"创建合同关联报价单失败: {str(e)}")


@contract_quotation_relation_controller.put("/{contract_id}", response_model=Dict[str, Any])
async def update_contract_quotation_relations(
    update_model: ContractQuotationRelationUpdateModel,
    contract_id: int = Path(..., description="合同ID"),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    更新合同关联报价单
    
    Args:
        update_model: 更新模型
        contract_id: 合同ID
        current_user: 当前用户
        
    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        logger.info(f"更新合同关联报价单，合同ID: {contract_id}, 项目编号: {update_model.project_codes}")
        
        result = contract_quotation_relation_service.update_contract_quotation_relations(
            contract_id, update_model, current_user
        )
        
        if result:
            logger.info(f"更新合同关联报价单成功，合同ID: {contract_id}")
            return ResponseUtil.success(message="更新合同关联报价单成功")
        else:
            logger.error(f"更新合同关联报价单失败，合同ID: {contract_id}")
            return ResponseUtil.error(message="更新合同关联报价单失败")
            
    except Exception as e:
        logger.error(f"更新合同关联报价单失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(message=f"更新合同关联报价单失败: {str(e)}")


@contract_quotation_relation_controller.delete("/{contract_id}", response_model=Dict[str, Any])
async def delete_contract_quotation_relations(
    contract_id: int = Path(..., description="合同ID"),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    删除合同关联报价单
    
    Args:
        contract_id: 合同ID
        current_user: 当前用户
        
    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        logger.info(f"删除合同关联报价单，合同ID: {contract_id}")
        
        result = contract_quotation_relation_service.delete_contract_quotation_relations(contract_id)
        
        if result:
            logger.info(f"删除合同关联报价单成功，合同ID: {contract_id}")
            return ResponseUtil.success(message="删除合同关联报价单成功")
        else:
            logger.error(f"删除合同关联报价单失败，合同ID: {contract_id}")
            return ResponseUtil.error(message="删除合同关联报价单失败")
            
    except Exception as e:
        logger.error(f"删除合同关联报价单失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(message=f"删除合同关联报价单失败: {str(e)}")
