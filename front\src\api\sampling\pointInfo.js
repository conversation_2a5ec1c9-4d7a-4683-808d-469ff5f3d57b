import request from '@/utils/request'

// 创建点位信息
export function createPointInfo(data) {
  return request({
    url: '/sampling/point-info/create',
    method: 'post',
    data: data
  })
}

// 根据ID获取点位信息
export function getPointInfoById(pointInfoId) {
  return request({
    url: `/sampling/point-info/get/${pointInfoId}`,
    method: 'get'
  })
}

// 根据分组ID获取点位信息
export function getPointInfoByGroupId(groupId) {
  return request({
    url: `/sampling/point-info/group/${groupId}`,
    method: 'get'
  })
}

// 更新点位信息
export function updatePointInfo(pointInfoId, data) {
  return request({
    url: `/sampling/point-info/update/${pointInfoId}`,
    method: 'put',
    data: data
  })
}

// 删除点位信息
export function deletePointInfo(pointInfoId) {
  return request({
    url: `/sampling/point-info/delete/${pointInfoId}`,
    method: 'delete'
  })
}

// 上传点位照片
export function uploadPointPhoto(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/sampling/point-info/upload-photo',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 根据任务ID获取所有点位信息
export function getPointInfoListByTaskId(taskId) {
  return request({
    url: `/sampling/point-info/task/${taskId}/points`,
    method: 'get'
  })
}
