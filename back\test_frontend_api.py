#!/usr/bin/env python3
"""
测试前端API调用的完整流程
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_complete_workflow():
    """测试完整的前端工作流程"""
    
    print("🔄 测试完整的前端工作流程...")
    
    # 1. 获取执行任务列表（模拟前端页面加载）
    print("\n1. 获取执行任务列表:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list",
            headers=HEADERS,
            params={"pageNum": 1, "pageSize": 10},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('rows', [])
            print(f"   找到 {len(groups)} 个任务分组")
            
            if groups:
                # 选择第一个分组进行测试
                test_group = groups[0]
                group_id = test_group.get('id')
                task_id = test_group.get('samplingTaskId')
                
                print(f"   测试分组: ID={group_id}, 任务ID={task_id}")
                
                # 2. 获取分组的样品记录
                print(f"\n2. 获取分组 {group_id} 的样品记录:")
                
                sample_response = requests.get(
                    f"{BASE_URL}/sampling/sample-records/group/{group_id}",
                    headers=HEADERS,
                    timeout=10
                )
                
                print(f"   状态码: {sample_response.status_code}")
                
                if sample_response.status_code == 200:
                    sample_data = sample_response.json()
                    samples = sample_data.get('data', [])
                    print(f"   找到 {len(samples)} 个样品记录")
                    
                    if samples:
                        # 选择第一个样品进行测试
                        test_sample = samples[0]
                        sample_id = test_sample.get('id')
                        
                        print(f"   测试样品: ID={sample_id}, 编号={test_sample.get('sampleNumber')}")
                        
                        # 3. 获取样品关联的瓶组（这是关键步骤）
                        print(f"\n3. 获取样品 {sample_id} 关联的瓶组:")
                        
                        bottle_response = requests.get(
                            f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                            headers=HEADERS,
                            timeout=10
                        )
                        
                        print(f"   状态码: {bottle_response.status_code}")
                        
                        if bottle_response.status_code == 200:
                            bottle_data = bottle_response.json()
                            bottles = bottle_data.get('data', [])
                            print(f"   ✅ 成功获取 {len(bottles)} 个瓶组")
                            
                            if bottles:
                                print("   前3个瓶组信息:")
                                for i, bottle in enumerate(bottles[:3], 1):
                                    print(f"     {i}. {bottle.get('bottleGroupCode', 'N/A')}")
                                    print(f"        状态: {bottle.get('status', 'N/A')}")
                                    print(f"        类型: {bottle.get('bottleType', '默认瓶组')}")
                                    print(f"        容量: {bottle.get('bottleVolume', '-')}")
                                    
                                # 4. 验证数据结构
                                print(f"\n4. 验证数据结构:")
                                first_bottle = bottles[0]
                                required_fields = ['id', 'bottleGroupCode', 'status']
                                optional_fields = ['bottleType', 'bottleVolume', 'storageStyles', 'detectionMethod']
                                
                                print("   必需字段检查:")
                                for field in required_fields:
                                    if field in first_bottle:
                                        print(f"     ✅ {field}: {first_bottle[field]}")
                                    else:
                                        print(f"     ❌ {field}: 缺失")
                                
                                print("   可选字段检查:")
                                for field in optional_fields:
                                    if field in first_bottle:
                                        print(f"     ✅ {field}: {first_bottle[field]}")
                                    else:
                                        print(f"     ⚠️  {field}: 缺失")
                                
                                # 5. 模拟前端条件判断
                                print(f"\n5. 模拟前端条件判断:")
                                
                                # 模拟前端的条件判断逻辑
                                bottle_groups = bottles  # 这是从API获取的数据
                                
                                print(f"   bottle_groups 类型: {type(bottle_groups)}")
                                print(f"   是否为数组: {isinstance(bottle_groups, list)}")
                                print(f"   数组长度: {len(bottle_groups) if isinstance(bottle_groups, list) else 'N/A'}")
                                
                                # 前端条件判断
                                if isinstance(bottle_groups, list) and len(bottle_groups) > 0:
                                    print("   ✅ 前端应该显示瓶组信息")
                                elif isinstance(bottle_groups, list) and len(bottle_groups) == 0:
                                    print("   ⚠️  前端应该显示'暂无关联的瓶组信息'")
                                else:
                                    print("   ❌ 前端应该显示'点击展开查看瓶组信息'")
                                
                                print(f"\n✅ 工作流程测试完成 - 数据正常")
                                return True
                            else:
                                print("   ❌ 瓶组数据为空")
                        else:
                            print(f"   ❌ 获取瓶组失败: {bottle_response.text}")
                    else:
                        print("   ❌ 样品记录为空")
                else:
                    print(f"   ❌ 获取样品记录失败: {sample_response.text}")
            else:
                print("   ❌ 任务分组为空")
        else:
            print(f"   ❌ 获取任务分组失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
    
    print(f"\n❌ 工作流程测试失败")
    return False

if __name__ == "__main__":
    success = test_complete_workflow()
    if success:
        print("\n🎉 所有测试通过，瓶组信息应该能正常显示")
    else:
        print("\n💥 测试失败，需要进一步调试")
