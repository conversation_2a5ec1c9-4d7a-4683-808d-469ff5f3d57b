#!/usr/bin/env python3
"""
测试分组编号显示功能
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_group_code_display():
    """测试分组编号显示功能"""
    
    print("🔍 测试分组编号显示功能...")
    
    # 1. 获取执行任务列表
    print("\n1. 获取执行任务列表:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list",
            headers=HEADERS,
            params={"pageNum": 1, "pageSize": 10},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('rows', [])
            print(f"   找到 {len(groups)} 个任务分组")
            
            if groups:
                # 检查分组数据结构
                test_group = groups[0]
                print(f"\n   第一个分组的数据结构:")
                print(f"     ID: {test_group.get('id')}")
                print(f"     分组编号: {test_group.get('groupCode')}")
                print(f"     任务编号: {test_group.get('taskCode')}")
                print(f"     任务名称: {test_group.get('taskName')}")
                print(f"     项目名称: {test_group.get('projectName')}")
                print(f"     客户名称: {test_group.get('customerName')}")
                
                # 2. 获取分组详情（模拟H5页面调用）
                group_id = test_group.get('id')
                print(f"\n2. 获取分组 {group_id} 的详情:")
                
                detail_response = requests.get(
                    f"{BASE_URL}/sampling/task-group/{group_id}",
                    headers=HEADERS,
                    timeout=10
                )
                
                print(f"   状态码: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    group_detail = detail_data.get('data', {})
                    
                    print(f"   分组详情:")
                    print(f"     ID: {group_detail.get('id')}")
                    print(f"     分组编号: {group_detail.get('groupCode')}")
                    print(f"     任务编号: {group_detail.get('taskCode')}")
                    print(f"     任务名称: {group_detail.get('taskName')}")
                    
                    # 验证前端显示逻辑
                    print(f"\n3. 验证前端显示逻辑:")
                    
                    # PC端采样管理弹窗应该显示的内容
                    print(f"   PC端采样管理弹窗:")
                    print(f"     分组编号: {group_detail.get('groupCode', '未设置')}")
                    print(f"     任务名称: {group_detail.get('taskName', '-')}")
                    print(f"     项目名称: {group_detail.get('projectName', '-')}")
                    print(f"     客户名称: {group_detail.get('customerName', '-')}")
                    
                    # H5页面应该显示的内容
                    print(f"   H5页面头部:")
                    print(f"     分组编号: {group_detail.get('groupCode', '未设置')}")
                    print(f"     任务状态: {group_detail.get('status', 0)}")
                    
                    print(f"   H5页面基础信息:")
                    print(f"     分组编号: {group_detail.get('groupCode', '-')}")
                    print(f"     任务名称: {group_detail.get('taskName', '-')}")
                    print(f"     项目名称: {group_detail.get('projectName', '-')}")
                    print(f"     客户名称: {group_detail.get('customerName', '-')}")
                    
                    # 检查分组编号是否存在
                    if group_detail.get('groupCode'):
                        print(f"\n   ✅ 分组编号存在: {group_detail.get('groupCode')}")
                        print(f"   ✅ 前端应该能正确显示分组编号")
                    else:
                        print(f"\n   ❌ 分组编号不存在或为空")
                        print(f"   ❌ 前端可能显示空白或'-'")
                    
                    return True
                else:
                    print(f"   ❌ 获取分组详情失败: {detail_response.text}")
            else:
                print("   ❌ 任务分组列表为空")
        else:
            print(f"   ❌ 获取任务分组列表失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
    
    return False

def test_group_code_generation():
    """测试分组编号生成规则"""
    
    print("\n🔧 测试分组编号生成规则...")
    
    try:
        # 查询数据库中的分组编号
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list",
            headers=HEADERS,
            params={"pageNum": 1, "pageSize": 5},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('rows', [])
            
            print(f"   检查前5个分组的编号生成规则:")
            for i, group in enumerate(groups[:5], 1):
                group_id = group.get('id')
                task_code = group.get('taskCode')
                group_code = group.get('groupCode')
                expected_code = f"{task_code}-{group_id}" if task_code else f"TASK-{group_id}"
                
                print(f"     {i}. 分组ID: {group_id}")
                print(f"        任务编号: {task_code}")
                print(f"        分组编号: {group_code}")
                print(f"        期望编号: {expected_code}")
                
                if group_code == expected_code:
                    print(f"        ✅ 编号生成正确")
                else:
                    print(f"        ⚠️  编号可能不符合预期")
                print()
        
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")

if __name__ == "__main__":
    success = test_group_code_display()
    test_group_code_generation()
    
    if success:
        print("\n🎉 分组编号显示功能测试通过")
        print("   - PC端采样管理弹窗将显示分组编号")
        print("   - H5页面将显示分组编号")
        print("   - 数据结构正确，前端应该能正常显示")
    else:
        print("\n💥 分组编号显示功能测试失败")
        print("   - 请检查后端API是否正常")
        print("   - 请检查分组编号字段是否存在")
