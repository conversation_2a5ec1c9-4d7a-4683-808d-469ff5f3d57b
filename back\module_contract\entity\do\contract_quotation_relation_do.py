"""
合同关联报价单数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from config.database import Base


class ContractQuotationRelation(Base):
    """
    合同关联报价单表
    """

    __tablename__ = "contract_quotation_relation"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    project_code = Column(String(50), nullable=False, comment="项目编号")
    
    # 创建人和创建时间
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment="创建时间")

    # 关联合同
    contract = relationship("Contract", back_populates="quotation_relations")

    def __repr__(self):
        return f"<ContractQuotationRelation(id={self.id}, contract_id={self.contract_id}, project_code={self.project_code})>"
