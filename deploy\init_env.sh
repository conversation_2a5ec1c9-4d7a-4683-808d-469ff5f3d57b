#!/bin/bash
# 初始化部署环境的脚本
# 作用：安装必要的软件包，配置环境

set -e

echo "===== 开始初始化部署环境 ====="

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要以root用户运行" 1>&2
   exit 1
fi

# 检测系统类型
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu系统
    echo "检测到Debian/Ubuntu系统"
    PKG_MANAGER="apt"
    $PKG_MANAGER update
elif [ -f /etc/redhat-release ]; then
    # CentOS/RHEL系统
    echo "检测到CentOS/RHEL系统"
    PKG_MANAGER="yum"
    $PKG_MANAGER update -y
else
    echo "不支持的系统类型"
    exit 1
fi

# 安装基础软件包
echo "正在安装基础软件包..."
$PKG_MANAGER install -y python3 python3-pip python3-venv nginx supervisor

# 安装MySQL（如果需要）
read -p "是否安装MySQL? (y/n): " install_mysql
if [ "$install_mysql" = "y" ]; then
    echo "正在安装MySQL..."
    if [ "$PKG_MANAGER" = "apt" ]; then
        $PKG_MANAGER install -y mysql-server
        systemctl enable mysql
        systemctl start mysql
    else
        $PKG_MANAGER install -y mysql-server
        systemctl enable mysqld
        systemctl start mysqld
    fi
    
    # 设置MySQL root密码和创建数据库
    echo "请设置MySQL root密码和创建数据库..."
    read -p "MySQL root密码: " mysql_root_password
    read -p "应用数据库名称: " db_name
    read -p "应用数据库用户: " db_user
    read -p "应用数据库密码: " db_password
    
    # 创建数据库和用户
    mysql -u root -p"$mysql_root_password" -e "CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    mysql -u root -p"$mysql_root_password" -e "CREATE USER IF NOT EXISTS '$db_user'@'localhost' IDENTIFIED BY '$db_password';"
    mysql -u root -p"$mysql_root_password" -e "GRANT ALL PRIVILEGES ON $db_name.* TO '$db_user'@'localhost';"
    mysql -u root -p"$mysql_root_password" -e "FLUSH PRIVILEGES;"
    
    echo "MySQL数据库和用户已创建"
fi

# 安装Redis（如果需要）
read -p "是否安装Redis? (y/n): " install_redis
if [ "$install_redis" = "y" ]; then
    echo "正在安装Redis..."
    $PKG_MANAGER install -y redis-server
    systemctl enable redis-server
    systemctl start redis-server
    echo "Redis已安装并启动"
fi

# 安装Node.js（用于前端构建）
read -p "是否安装Node.js? (y/n): " install_nodejs
if [ "$install_nodejs" = "y" ]; then
    echo "正在安装Node.js..."
    if [ "$PKG_MANAGER" = "apt" ]; then
        curl -fsSL https://deb.nodesource.com/setup_16.x | bash -
        $PKG_MANAGER install -y nodejs
    else
        curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
        $PKG_MANAGER install -y nodejs
    fi
    echo "Node.js已安装"
fi

# 创建应用目录
read -p "应用部署路径: " app_path
mkdir -p $app_path
echo "应用目录已创建: $app_path"

# 创建日志目录
mkdir -p /var/log/lims
echo "日志目录已创建: /var/log/lims"

# 配置防火墙
read -p "是否配置防火墙? (y/n): " config_firewall
if [ "$config_firewall" = "y" ]; then
    echo "正在配置防火墙..."
    if [ "$PKG_MANAGER" = "apt" ]; then
        $PKG_MANAGER install -y ufw
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw --force enable
    else
        $PKG_MANAGER install -y firewalld
        systemctl enable firewalld
        systemctl start firewalld
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
    fi
    echo "防火墙已配置"
fi

echo "===== 部署环境初始化完成 ====="
echo "请继续运行前端和后端部署脚本"
