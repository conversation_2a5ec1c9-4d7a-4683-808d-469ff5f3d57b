"""
样品记录数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SampleRecord(Base):
    """样品记录表"""
    __tablename__ = 'sample_record'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_group_id = Column(BigInteger, ForeignKey('sampling_task_group.id'), nullable=False, comment='采样任务分组ID')
    
    # 样品信息
    sample_number = Column(Integer, nullable=False, comment='样品序号（从1开始）')
    sample_type = Column(String(50), comment='样品类型')
    sample_source = Column(String(50), comment='样品来源')
    point_name = Column(String(200), comment='点位名称')
    
    # 周期信息
    cycle_number = Column(Integer, nullable=False, comment='周期序号')
    cycle_type = Column(String(50), comment='周期类型')
    
    # 检测信息
    detection_category = Column(String(100), comment='检测类别')
    detection_parameter = Column(Text, comment='检测参数')
    detection_method = Column(Text, comment='检测方法')
    
    # 状态和时间
    status = Column(Integer, default=0, comment='样品状态：0-待采集，1-已采集，2-已送检，3-检测中，4-已完成')
    collection_time = Column(DateTime, comment='采集时间')
    submission_time = Column(DateTime, comment='送检时间')
    completion_time = Column(DateTime, comment='完成时间')
    
    # 备注
    remark = Column(Text, comment='备注')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # sampling_task_group = relationship("SamplingTaskGroup", foreign_keys=[sampling_task_group_id])

    # 创建人和更新人关系（暂时注释，避免循环导入）
    # creator = relationship("SysUser", foreign_keys=[create_by])
    # updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sample_record_group_id', 'sampling_task_group_id'),
        Index('idx_sample_record_status', 'status'),
        Index('idx_sample_record_sample_number', 'sample_number'),
        {'comment': '样品记录表'}
    )
    
    def __repr__(self):
        return f"<SampleRecord(id={self.id}, sample_number={self.sample_number}, status={self.status})>"
    
    @property
    def status_label(self):
        """状态标签"""
        status_map = {
            0: '待采集',
            1: '已采集',
            2: '已送检',
            3: '检测中',
            4: '已完成'
        }
        return status_map.get(self.status, '未知状态')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sample_number': self.sample_number,
            'sample_type': self.sample_type,
            'sample_source': self.sample_source,
            'point_name': self.point_name,
            'cycle_number': self.cycle_number,
            'cycle_type': self.cycle_type,
            'detection_category': self.detection_category,
            'detection_parameter': self.detection_parameter,
            'detection_method': self.detection_method,
            'status': self.status,
            'status_label': self.status_label,
            'collection_time': self.collection_time.isoformat() if self.collection_time else None,
            'submission_time': self.submission_time.isoformat() if self.submission_time else None,
            'completion_time': self.completion_time.isoformat() if self.completion_time else None,
            'remark': self.remark,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
