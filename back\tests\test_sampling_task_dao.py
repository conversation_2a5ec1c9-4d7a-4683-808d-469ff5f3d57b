from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from tests.conftest import SamplingTaskSequenceModel


class SamplingTaskDaoForTest:
    """测试用的采样任务数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def generate_task_code(self) -> str:
        """生成任务编号
        格式：YYMMXXXX（8位数字）
        YY：年份后两位
        MM：月份两位
        XXXX：四位流水号（每月从0001开始）
        """
        now = datetime.now()
        year_month = now.strftime('%y%m')  # 年份后两位 + 月份两位
        
        # 获取或创建序列记录
        sequence_result = await self.db.execute(
            select(SamplingTaskSequenceModel).filter(
                SamplingTaskSequenceModel.year_month == year_month
            )
        )
        sequence = sequence_result.scalars().first()
        
        if not sequence:
            sequence = SamplingTaskSequenceModel(year_month=year_month, sequence_number=0)
            self.db.add(sequence)
            await self.db.flush()
        
        # 递增序列号
        sequence.sequence_number += 1
        await self.db.merge(sequence)
        await self.db.flush()
        
        # 生成任务编号：YYMMXXXX（8位数字）
        task_code = f"{year_month}{sequence.sequence_number:04d}"
        return task_code