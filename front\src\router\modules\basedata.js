import Layout from '@/layout'

export const basedataRoutes = {
    path: '/basedata',
    component: Layout,
    name: 'BaseData',
    meta: { title: '基础数据', icon: 'dict' },
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'technicalManual',
        component: () => import('@/views/basedata/technicalManual/index'),
        name: 'TechnicalManual',
        meta: { title: '技术手册', icon: 'list' }
      },
      {
        path: 'technicalManualCategory',
        component: () => import('@/views/basedata/technicalManualCategory/index'),
        name: 'TechnicalManualCategory',
        meta: { title: '类目类别', icon: 'tree' }
      },
      {
        path: 'technicalManualPrice',
        component: () => import('@/views/basedata/technicalManualPrice/index'),
        name: 'TechnicalManualPrice',
        meta: { title: '技术价格', icon: 'money' }
      },
      {
        path: 'equipment-management',
        component: () => import('@/views/basedata/equipment-management/index'),
        name: 'EquipmentManagement',
        meta: { title: '设备管理', icon: 'monitor' }
      }
    ]
}