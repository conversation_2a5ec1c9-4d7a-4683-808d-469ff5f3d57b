#!/bin/bash

# 网络连接测试脚本
# 用于测试 Docker 镜像拉取和网络连接

echo "===== Docker 网络连接测试 ====="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 测试 Docker Hub 连接
test_docker_hub() {
    log_info "测试 Docker Hub 连接..."
    if curl -s --connect-timeout 10 https://registry-1.docker.io/v2/ > /dev/null; then
        log_info "Docker Hub 连接正常"
        return 0
    else
        log_error "Docker Hub 连接失败"
        return 1
    fi
}

# 测试 Xuanyuan 镜像仓库连接
test_xuanyuan_registry() {
    log_info "测试 Xuanyuan 镜像仓库连接..."
    if curl -s --connect-timeout 10 https://docker.xuanyuan.me/v2/ > /dev/null; then
        log_info "Xuanyuan 镜像仓库连接正常"
        return 0
    else
        log_error "Xuanyuan 镜像仓库连接失败"
        return 1
    fi
}

# 测试镜像拉取
test_image_pull() {
    local image=$1
    log_info "测试拉取镜像: $image"
    
    if docker pull $image > /dev/null 2>&1; then
        log_info "镜像拉取成功: $image"
        docker rmi $image > /dev/null 2>&1
        return 0
    else
        log_error "镜像拉取失败: $image"
        return 1
    fi
}

# 主测试函数
main() {
    echo "开始网络连接测试..."
    echo ""
    
    # 测试网络连接
    docker_hub_ok=false
    xuanyuan_ok=false

    if test_docker_hub; then
        docker_hub_ok=true
    fi

    if test_xuanyuan_registry; then
        xuanyuan_ok=true
    fi
    
    echo ""
    
    # 根据网络情况推荐镜像源
    if $xuanyuan_ok; then
        log_info "推荐使用 Xuanyuan 镜像源（已配置）"

        # 测试关键镜像拉取
        log_info "测试关键镜像拉取..."
        test_image_pull "docker.xuanyuan.me/python:3.12-slim"
        test_image_pull "docker.xuanyuan.me/mysql:8.0"
        test_image_pull "docker.xuanyuan.me/redis:7-alpine"
        test_image_pull "docker.xuanyuan.me/nginx:alpine"
        
    elif $docker_hub_ok; then
        log_warn "Docker Hub 可用，但建议配置镜像加速器以提高速度"
        
        # 测试关键镜像拉取
        log_info "测试关键镜像拉取..."
        test_image_pull "python:3.12-slim"
        test_image_pull "mysql:8.0"
        test_image_pull "redis:7-alpine"
        test_image_pull "nginx:alpine"
        
    else
        log_error "所有镜像仓库都无法连接，请检查网络设置"
        echo ""
        echo "建议解决方案："
        echo "1. 检查网络连接"
        echo "2. 配置 Docker 镜像加速器"
        echo "3. 使用企业内网镜像仓库"
        exit 1
    fi
    
    echo ""
    log_info "网络测试完成"
}

# 执行测试
main "$@"
