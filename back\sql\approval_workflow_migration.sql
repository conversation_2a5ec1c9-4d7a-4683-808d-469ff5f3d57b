-- 审批流程相关数据库迁移脚本

-- 1. 为项目报价表添加业务类别字段
ALTER TABLE project_quotation 
ADD COLUMN business_type VARCHAR(20) NOT NULL DEFAULT 'sampling' COMMENT '业务类别：sampling-一般采样，sample-送样' 
AFTER commission_date;

-- 2. 创建项目报价审批记录表
DROP TABLE IF EXISTS project_quotation_approval_record;
CREATE TABLE project_quotation_approval_record (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_quotation_id INT NOT NULL COMMENT '项目报价ID',
    approver_type VARCHAR(20) NOT NULL COMMENT '审批人类型：market-市场审批，lab-实验室审批，field-现场审批',
    approver_user_id INT NOT NULL COMMENT '审批人用户ID',
    approval_status VARCHAR(10) NOT NULL DEFAULT 'pending' COMMENT '审批状态：pending-待审批，approved-已通过，rejected-已拒绝',
    approval_opinion TEXT COMMENT '审批意见',
    approval_time DATETIME COMMENT '审批时间',
    approval_stage INT NOT NULL COMMENT '审批阶段：1-市场审批阶段，2-技术审批阶段',
    is_required CHAR(1) NOT NULL DEFAULT '1' COMMENT '是否必需：0-否，1-是',
    create_by INT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by INT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (project_quotation_id) REFERENCES project_quotation(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_user_id) REFERENCES sys_user(user_id),
    FOREIGN KEY (create_by) REFERENCES sys_user(user_id),
    FOREIGN KEY (update_by) REFERENCES sys_user(user_id),
    
    INDEX idx_project_quotation_id (project_quotation_id),
    INDEX idx_approver_user_id (approver_user_id),
    INDEX idx_approval_status (approval_status),
    INDEX idx_approval_stage (approval_stage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目报价审批记录表';

-- 3. 创建审批角色（如果不存在）
INSERT IGNORE INTO sys_role (role_name, role_key, role_sort, status, create_by, create_time, remark) VALUES
('市场审批人员', 'market-approver', 10, '0', 'admin', NOW(), '负责项目报价的市场审批'),
('实验室审批人员', 'lab-approver', 11, '0', 'admin', NOW(), '负责项目报价的实验室审批'),
('现场审批人员', 'field-approver', 12, '0', 'admin', NOW(), '负责项目报价的现场审批');

-- 4. 为现有项目报价数据初始化业务类别（可选，根据实际情况调整）
-- UPDATE project_quotation SET business_type = 'sampling' WHERE business_type IS NULL OR business_type = '';

-- 5. 创建审批流程相关的菜单（可选）
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('报价审批', 2000, 3, 'approval', 'quotation/approval/index', '', 1, 0, 'C', '0', '0', 'quotation:approval:list', 'check', 'admin', NOW(), '报价审批菜单'),
('审批操作', (SELECT menu_id FROM sys_menu WHERE perms = 'quotation:approval:list'), 1, '', '', '', 1, 0, 'F', '0', '0', 'quotation:approval:approve', '#', 'admin', NOW(), ''),
('审批查看', (SELECT menu_id FROM sys_menu WHERE perms = 'quotation:approval:list'), 2, '', '', '', 1, 0, 'F', '0', '0', 'quotation:approval:view', '#', 'admin', NOW(), '');

-- 6. 为审批角色分配相关权限（可选，根据实际需求调整）
-- 这里需要根据实际的菜单ID来分配权限，建议在系统中手动配置
