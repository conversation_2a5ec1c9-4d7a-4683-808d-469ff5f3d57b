import Layout from '@/layout'

const projectQuotationRouter = {
  path: '/quotation',
  component: Layout,
  redirect: '/quotation/project-quotation/index',
  name: 'Quotation',
  meta: { title: '报价管理', icon: 'money' },
  children: [
    {
      path: 'project-quotation/index',
      component: () => import('@/views/quotation/project-quotation/index'),
      name: 'ProjectQuotation',
      meta: { title: '项目报价', icon: 'list' }
    },
    {
      path: 'project-quotation/add',
      component: () => import('@/views/quotation/project-quotation/add'),
      name: 'AddProjectQuotation',
      meta: { title: '新增项目报价', icon: 'form', activeMenu: '/quotation/project-quotation/index' },
      hidden: true
    },
    {
      path: 'project-quotation-approval/index',
      component: () => import('@/views/quotation/project-quotation-approval/index'),
      name: 'ProjectQuotationApproval',
      meta: { title: '报价审批管理', icon: 'check' }
    },
  ]
}

export default projectQuotationRouter
