<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瓶组信息显示测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .bottle-info-item {
            margin-bottom: 6px;
            display: flex;
            align-items: flex-start;
            font-size: 12px;
            line-height: 1.4;
        }
        .bottle-info-item .label {
            font-weight: 600;
            color: var(--el-text-color-regular);
            min-width: 50px;
            margin-right: 6px;
            flex-shrink: 0;
            font-size: 12px;
        }
        .bottle-info-item .value {
            color: var(--el-text-color-primary);
            flex: 1;
            word-break: break-word;
            font-size: 12px;
        }
        .no-bottle-groups {
            padding: 40px 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>🧪 瓶组信息显示测试</h1>
        
        <div class="test-section">
            <h3>📋 测试数据状态</h3>
            <el-button @click="testNullState">测试 null 状态</el-button>
            <el-button @click="testEmptyArrayState">测试空数组状态</el-button>
            <el-button @click="testLoadingState">测试加载状态</el-button>
            <el-button @click="testWithDataState">测试有数据状态</el-button>
            <el-button @click="loadRealData">加载真实数据</el-button>
        </div>
        
        <div class="test-section">
            <h3>🍶 瓶组信息显示</h3>
            <p>当前状态: {{ currentState }}</p>
            
            <!-- 模拟样品记录展开的瓶组显示 -->
            <div style="padding: 12px 16px; background-color: var(--el-bg-color-page); border: 1px solid #e4e7ed; border-radius: 8px;">
                <h4 style="margin: 0 0 8px 0; color: var(--el-color-primary); font-size: 14px;">
                    <el-icon style="margin-right: 4px;"><Box /></el-icon>
                    瓶组信息
                </h4>

                <!-- 加载状态 -->
                <div v-if="sampleRow.bottleGroupsLoading" class="bottle-groups-loading">
                    <el-skeleton :rows="2" animated>
                        <template #template>
                            <el-skeleton-item variant="rect" style="width: 100%; height: 120px; margin-bottom: 16px;" />
                            <el-skeleton-item variant="rect" style="width: 100%; height: 120px;" />
                        </template>
                    </el-skeleton>
                </div>

                <!-- 瓶组信息卡片式布局 -->
                <div v-else-if="Array.isArray(sampleRow.bottleGroups) && sampleRow.bottleGroups.length > 0" class="bottle-groups-container">
                    <el-row :gutter="12">
                        <el-col :span="24" v-for="(bottle, index) in sampleRow.bottleGroups.slice(0, 3)" :key="bottle.id" style="margin-bottom: 12px;">
                            <el-card shadow="hover" :body-style="{ padding: '12px' }" style="border-radius: 6px;">
                                <template #header>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px;">
                                        <span style="font-weight: 600; color: var(--el-color-primary); font-size: 13px;">
                                            <el-icon style="margin-right: 4px; font-size: 12px;"><Box /></el-icon>
                                            {{ bottle.bottleGroupCode }}
                                        </span>
                                        <el-tag :type="getBottleStatusType(bottle.status)" size="small">
                                            {{ getBottleStatusLabel(bottle.status) }}
                                        </el-tag>
                                    </div>
                                </template>

                                <el-row :gutter="12">
                                    <el-col :span="8">
                                        <div class="bottle-info-item">
                                            <span class="label">类型：</span>
                                            <span class="value">{{ bottle.bottleType || '默认瓶组' }}</span>
                                        </div>
                                        <div class="bottle-info-item">
                                            <span class="label">容量：</span>
                                            <span class="value">{{ bottle.bottleVolume || '-' }}</span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="bottle-info-item">
                                            <span class="label">存储：</span>
                                            <span class="value">
                                                {{ bottle.storageStyles && bottle.storageStyles.length > 0 ? bottle.storageStyles.join(', ') : '-' }}
                                            </span>
                                        </div>
                                        <div class="bottle-info-item">
                                            <span class="label">时效：</span>
                                            <span class="value">
                                                {{ bottle.sampleAge ? `${bottle.sampleAge}${bottle.sampleAgeUnit}` : '-' }}
                                            </span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="bottle-info-item">
                                            <span class="label">检测方法：</span>
                                            <span class="value" style="word-break: break-all; font-size: 12px;">{{ bottle.detectionMethod || '-' }}</span>
                                        </div>
                                    </el-col>
                                </el-row>
                            </el-card>
                        </el-col>
                    </el-row>
                    <div v-if="sampleRow.bottleGroups.length > 3" style="text-align: center; margin-top: 10px; color: #909399;">
                        还有 {{ sampleRow.bottleGroups.length - 3 }} 个瓶组...
                    </div>
                </div>

                <!-- 无瓶组信息 -->
                <div v-else-if="Array.isArray(sampleRow.bottleGroups) && sampleRow.bottleGroups.length === 0" class="no-bottle-groups">
                    <el-empty description="暂无关联的瓶组信息" :image-size="80">
                        <template #image>
                            <el-icon size="80" color="var(--el-text-color-placeholder)"><Box /></el-icon>
                        </template>
                    </el-empty>
                </div>

                <!-- 未加载瓶组信息 -->
                <div v-else class="no-bottle-groups">
                    <el-empty description="点击展开查看瓶组信息" :image-size="80">
                        <template #image>
                            <el-icon size="80" color="var(--el-text-color-placeholder)"><Box /></el-icon>
                        </template>
                    </el-empty>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 调试信息</h3>
            <pre>{{ JSON.stringify(sampleRow, null, 2) }}</pre>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const currentState = ref('初始状态');
                const sampleRow = reactive({
                    id: 12,
                    sampleNumber: 1,
                    bottleGroups: null,
                    bottleGroupsLoading: false
                });

                const testNullState = () => {
                    currentState.value = 'null 状态';
                    sampleRow.bottleGroups = null;
                    sampleRow.bottleGroupsLoading = false;
                };

                const testEmptyArrayState = () => {
                    currentState.value = '空数组状态';
                    sampleRow.bottleGroups = [];
                    sampleRow.bottleGroupsLoading = false;
                };

                const testLoadingState = () => {
                    currentState.value = '加载状态';
                    sampleRow.bottleGroups = null;
                    sampleRow.bottleGroupsLoading = true;
                };

                const testWithDataState = () => {
                    currentState.value = '有数据状态';
                    sampleRow.bottleGroups = [
                        {
                            id: 5,
                            bottleGroupCode: '25090003-B001',
                            status: 0,
                            bottleType: '默认瓶组',
                            bottleVolume: null,
                            storageStyles: [],
                            sampleAge: null,
                            sampleAgeUnit: null,
                            detectionMethod: '测试检测方法'
                        },
                        {
                            id: 6,
                            bottleGroupCode: '25090003-B002',
                            status: 0,
                            bottleType: '默认瓶组',
                            bottleVolume: null,
                            storageStyles: [],
                            sampleAge: null,
                            sampleAgeUnit: null,
                            detectionMethod: '测试检测方法'
                        }
                    ];
                    sampleRow.bottleGroupsLoading = false;
                };

                const loadRealData = async () => {
                    currentState.value = '加载真实数据中...';
                    sampleRow.bottleGroupsLoading = true;
                    
                    try {
                        const response = await fetch('/sampling/bottle-groups/sample/12', {
                            headers: {
                                'Authorization': 'Bearer test_token'
                            }
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            sampleRow.bottleGroups = data.data || [];
                            currentState.value = `真实数据加载成功 (${sampleRow.bottleGroups.length} 个瓶组)`;
                        } else {
                            sampleRow.bottleGroups = [];
                            currentState.value = '真实数据加载失败';
                        }
                    } catch (error) {
                        sampleRow.bottleGroups = [];
                        currentState.value = `真实数据加载异常: ${error.message}`;
                    } finally {
                        sampleRow.bottleGroupsLoading = false;
                    }
                };

                const getBottleStatusType = (status) => {
                    const statusMap = {
                        0: 'warning',  // 待采集
                        1: 'success',  // 已采集
                        2: 'info'      // 已送检
                    };
                    return statusMap[status] || 'info';
                };

                const getBottleStatusLabel = (status) => {
                    const statusMap = {
                        0: '待采集',
                        1: '已采集',
                        2: '已送检'
                    };
                    return statusMap[status] || '未知';
                };

                return {
                    currentState,
                    sampleRow,
                    testNullState,
                    testEmptyArrayState,
                    testLoadingState,
                    testWithDataState,
                    loadRealData,
                    getBottleStatusType,
                    getBottleStatusLabel
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
