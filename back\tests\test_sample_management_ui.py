"""
样品管理UI功能测试
验证样品管理弹窗的功能是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from server import app

client = TestClient(app)

def test_sample_management_workflow():
    """测试样品管理完整工作流程"""
    
    print("=== 样品管理工作流程测试 ===")
    
    # 1. 开始执行任务（自动生成样品记录）
    print("1. 开始执行任务...")
    response = client.post(
        '/sampling/sample-records/generate/1',
        headers={'Authorization': 'Bearer test_token'}
    )
    
    if response.status_code != 200:
        print(f"   ❌ 请求失败：{response.status_code} - {response.text}")
        return

    data = response.json()
    print(f"   ✅ 样品记录生成成功，共生成 {len(data['data'])} 条记录")
    
    # 2. 获取样品记录列表（样品管理弹窗功能）
    print("2. 获取样品记录列表...")
    response = client.get(
        '/sampling/sample-records/assignment/1',
        headers={'Authorization': 'Bearer test_token'}
    )
    
    if response.status_code != 200:
        print(f"   ❌ 获取样品记录失败：{response.status_code}")
        return

    data = response.json()
    sample_records = data['data']
    print(f"   ✅ 获取样品记录成功，共 {len(sample_records)} 条记录")
    
    if sample_records:
        sample = sample_records[0]
        print(f"   📋 样品信息：序号={sample['sampleNumber']}, 类型={sample['sampleType']}, 状态={sample['status']}")
    
    # 3. 获取样品统计信息
    print("3. 获取样品统计信息...")
    response = client.get(
        '/sampling/sample-records/statistics/assignment/1',
        headers={'Authorization': 'Bearer test_token'}
    )
    
    if response.status_code != 200:
        print(f"   ❌ 获取统计信息失败：{response.status_code}")
        return

    data = response.json()
    stats = data['data']
    print(f"   ✅ 统计信息：总数={stats['totalCount']}, 待采集={stats['pendingCount']}")
    
    # 4. 更新样品状态（模拟采集操作）
    if sample_records:
        sample_id = sample_records[0]['id']
        print(f"4. 更新样品状态（样品ID={sample_id}）...")
        
        response = client.put(
            f'/sampling/sample-records/status/{sample_id}',
            headers={'Authorization': 'Bearer test_token'},
            json=1  # 状态：已采集
        )
        
        if response.status_code == 200:
            print("   ✅ 样品状态更新成功：待采集 → 已采集")
        else:
            print(f"   ❌ 样品状态更新失败：{response.status_code}")
    
    print("=== 样品管理工作流程测试完成 ===")

def test_sample_management_features():
    """测试样品管理功能特性"""
    
    print("\n=== 样品管理功能特性验证 ===")
    
    # 验证样品记录字段正确性
    response = client.get(
        '/sampling/sample-records/assignment/1',
        headers={'Authorization': 'Bearer test_token'}
    )
    
    if response.status_code == 200:
        data = response.json()
        sample_records = data['data']
        
        if sample_records:
            sample = sample_records[0]
            
            # 验证关键字段
            required_fields = [
                'id', 'sampleNumber', 'sampleType', 'sampleSource', 
                'pointName', 'cycleNumber', 'cycleType', 'detectionCategory',
                'detectionParameter', 'detectionMethod', 'status'
            ]
            
            missing_fields = [field for field in required_fields if field not in sample]
            
            if missing_fields:
                print(f"   ❌ 缺少字段：{missing_fields}")
            else:
                print("   ✅ 样品记录字段完整")
            
            # 验证样品类型和检测类别的对应关系
            if sample['sampleType'] == sample['detectionCategory']:
                print(f"   ✅ 样品类型与检测类别一致：{sample['sampleType']}")
            else:
                print(f"   ❌ 样品类型与检测类别不一致：{sample['sampleType']} vs {sample['detectionCategory']}")
            
            # 验证检测参数和方法不为空
            if sample['detectionParameter'] and sample['detectionMethod']:
                print("   ✅ 检测参数和方法已正确合并")
            else:
                print("   ❌ 检测参数或方法为空")
    
    print("=== 样品管理功能特性验证完成 ===")

if __name__ == "__main__":
    test_sample_management_workflow()
    test_sample_management_features()
