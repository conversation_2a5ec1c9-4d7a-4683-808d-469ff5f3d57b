<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="检测类别" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入检测类别"
          clearable
          @blur="handleCategoryChange"
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="检测参数" prop="parameter">
        <el-input
          v-model="queryParams.parameter"
          placeholder="请输入检测参数"
          clearable
          @blur="handleParameterChange"
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="检测方法" prop="method">
        <el-input
          v-model="queryParams.method"
          placeholder="请输入检测方法"
          clearable
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="唯一编号" prop="qualificationCode">
        <el-input
          v-model="queryParams.qualificationCode"
          placeholder="唯一编号"
          clearable
          style="width: 200px; min-width: 100px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分析类型" prop="analysisType">
        <el-select v-model="queryParams.analysisType" placeholder="请选择分析类型" clearable style="min-width: 100px;">
          <el-option label="实验室分析" value="LAB_CHECK" />
          <el-option label="现场直读" value="ON_SITE_CHECK" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
           <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['basedata:technicalManual:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basedata:technicalManual:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basedata:technicalManual:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <technical-manual-import @import-success="handleImportSuccess" />
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Edit"
          :disabled="multiple"
          @click="handleBatchUpdate"
          v-hasPermi="['basedata:technicalManual:edit']"
        >批量修改</el-button>
      </el-col> -->
      <!-- 导出按钮 -->
      <!-- <el-col :span="1.5">
        <export-button export-url="/api/basedata/technical-manual/export" :query-params="queryParams" />
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="technicalManualList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="系统编号" align="center" prop="qualificationCode" width="120" /> -->
      <el-table-column label="分类" align="center" prop="classification" />
      <el-table-column label="检测类别" align="center" prop="category" />
      <el-table-column label="检测参数" align="center" prop="parameter" />
      <el-table-column label="检测方法" align="center" prop="method" show-overflow-tooltip />
      <el-table-column label="唯一编号" align="center" prop="qualificationCode" width="120" />
      <el-table-column label="限制范围" align="center" prop="limitationScope" show-overflow-tooltip />
      <el-table-column label="常用别名" align="center" prop="aliasList" show-overflow-tooltip>
        <template #default="scope">
          <div v-if="scope.row.aliasList && scope.row.aliasList.length > 0">
            <el-tag
              v-for="(alias, index) in scope.row.aliasList"
              :key="index"
              size="small"
              style="margin: 2px"
              type="info"
            >
              {{ alias }}
            </el-tag>
          </div>
          <span v-else style="color: #999;">暂无别名</span>
        </template>
      </el-table-column>
      <el-table-column label="取得资质时间" align="center" prop="qualificationDate" width="120" />
      <el-table-column label="是否有资质" align="center" prop="hasQualification" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.hasQualification === '0' ? 'success' : 'warning'">
            {{ scope.row.hasQualification === '0' ? '有' : '无' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分析类型" align="center" prop="analysisType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.analysisType" :type="scope.row.analysisType === 'LAB_CHECK' ? 'primary' : 'info'">
            {{ scope.row.analysisType === 'LAB_CHECK' ? '实验室分析' : '现场直读' }}
          </el-tag>
          <span v-else style="color: #999;">未设置</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basedata:technicalManual:edit']"
          >修改</el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basedata:technicalManual:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改技术手册对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="technicalManualRef" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="系统编号" prop="testCode" v-if="form.testCode">
          <el-input v-model="form.testCode" disabled placeholder="系统自动生成" />
        </el-form-item> -->
        <el-form-item label="分类" prop="classification">
          <el-input v-model="form.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="检测类别" prop="category">
          <el-input
            v-model="form.category"
            placeholder="请输入检测类别"
            @blur="handleFormCategoryChange"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测参数" prop="parameter">
          <el-input
            v-model="form.parameter"
            placeholder="请输入检测参数"
            @blur="handleFormParameterChange"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测方法" prop="method">
          <el-input
            v-model="form.method"
            placeholder="请输入检测方法"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="唯一编号" prop="qualificationCode">
          <el-input v-model="form.qualificationCode" placeholder="请输入唯一编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="form.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="aliasList">
          <div class="alias-input-container">
            <!-- 别名输入框 -->
            <div class="alias-input-wrapper">
              <el-input
                v-model="currentAlias"
                placeholder="请输入别名，按回车添加"
                @keyup.enter="addAlias"
                @blur="addAlias"
                clearable
                style="width: 300px; min-width: 100px;"
              />
              <el-button
                type="primary"
                @click="addAlias"
                :disabled="!currentAlias || !currentAlias.trim()"
                style="margin-left: 10px"
                size="small"
              >
                添加
              </el-button>
            </div>

            <!-- 别名标签显示 -->
            <div class="alias-tags-container" v-if="form.aliasList && form.aliasList.length > 0">
              <el-tag
                v-for="(alias, index) in form.aliasList"
                :key="index"
                closable
                @close="removeAlias(index)"
                style="margin: 5px 5px 0 0"
                type="info"
              >
                {{ alias }}
              </el-tag>
            </div>

            <!-- 提示信息 -->
            <div class="alias-hint" v-if="!form.aliasList || form.aliasList.length === 0">
              <span style="color: #999; font-size: 12px;">暂无别名，请在上方输入框中添加</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="form.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="是否有资质" prop="hasQualification">
          <el-radio-group v-model="form.hasQualification">
            <el-radio label="0">有</el-radio>
            <el-radio label="1">无</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分析类型" prop="analysisType">
          <el-select v-model="form.analysisType" placeholder="请选择分析类型" style="min-width: 100px;" clearable>
            <el-option label="实验室分析" value="LAB_CHECK" />
            <el-option label="现场直读" value="ON_SITE_CHECK" />
          </el-select>
        </el-form-item>
        <el-form-item label="技术描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入技术描述" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-divider content-position="left">其他价格</el-divider>
        <el-form-item label="分析特殊耗材单价">
          <el-input-number v-model="form.specialConsumablesPrice" :precision="2" :min="0" style="width: 40%;" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" style="min-width: 100px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量录入技术手册对话框 -->
    <el-dialog title="批量录入技术手册" v-model="batchInputOpen" width="600px" append-to-body>
      <el-form ref="batchInputRef" :model="batchInputForm" :rules="batchInputRules" label-width="100px">
        <el-form-item label="检测类别" prop="categories">
          <el-input
            v-model="batchInputForm.categories"
            type="textarea"
            placeholder="请输入检测类别，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测参数" prop="parameters">
          <el-input
            v-model="batchInputForm.parameters"
            type="textarea"
            placeholder="请输入检测参数，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测方法" prop="methods">
          <el-input
            v-model="batchInputForm.methods"
            type="textarea"
            placeholder="请输入检测方法，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="分类" prop="classification">
          <el-input v-model="batchInputForm.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="唯一编号" prop="qualificationCode">
          <el-input v-model="batchInputForm.qualificationCode" placeholder="请输入唯一编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="batchInputForm.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="commonAlias">
          <el-input v-model="batchInputForm.commonAlias" placeholder="请输入常用别名" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="batchInputForm.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="batchInputForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchInputForm.remark" type="textarea" placeholder="请输入备注" style="min-width: 100px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchInputForm">确 定</el-button>
          <el-button @click="cancelBatchInput">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量更新技术手册对话框 -->
    <el-dialog title="批量更新技术手册" v-model="batchUpdateOpen" width="500px" append-to-body>
      <el-form ref="batchUpdateRef" :model="batchUpdateForm" label-width="100px">
        <el-form-item label="分类" prop="classification">
          <el-input v-model="batchUpdateForm.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="唯一编号" prop="qualificationCode">
          <el-input v-model="batchUpdateForm.qualificationCode" placeholder="请输入唯一编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="batchUpdateForm.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="commonAlias">
          <el-input v-model="batchUpdateForm.commonAlias" placeholder="请输入常用别名" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="batchUpdateForm.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="batchUpdateForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchUpdateForm">确 定</el-button>
          <el-button @click="cancelBatchUpdate">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="TechnicalManual">
import {
  listTechnicalManual,
  pageTechnicalManual,
  getTechnicalManual,
  addTechnicalManual,
  updateTechnicalManual,
  delTechnicalManual,
  batchInputTechnicalManual,
  batchUpdateTechnicalManual,
  getCategoryOptions,
  getParameterOptions,
  getMethodOptions
} from "@/api/basedata/technicalManual";
import TechnicalManualImport from './components/TechnicalManualImport/index.vue';

const { proxy } = getCurrentInstance();
// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 技术手册表格数据
const technicalManualList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示批量录入弹出层
const batchInputOpen = ref(false);
// 是否显示批量更新弹出层
const batchUpdateOpen = ref(false);

// 日期范围
const dateRange = ref([]);
// 检测类别选项
const categoryOptions = ref([]);
// 检测参数选项
const parameterOptions = ref([]);
// 检测方法选项
const methodOptions = ref([]);
// 当前输入的别名
const currentAlias = ref("");

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  category: undefined,
  parameter: undefined,
  method: undefined,
  qualificationCode: undefined,
  analysisType: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  testCode: undefined,
  category: undefined,
  parameter: undefined,
  method: undefined,
  description: undefined,
  // 新增字段
  classification: undefined,
  qualificationCode: undefined,
  limitationScope: undefined,
  commonAlias: undefined,
  aliasList: [],
  qualificationDate: undefined,
  hasQualification: "1",
  analysisType: undefined,
  // 原有字段
  status: "0",
  specialConsumablesPrice: undefined,
  remark: undefined
});

// 表单校验
const rules = ref({
  classification: [
    { required: true, message: "分类不能为空", trigger: "blur" }
  ],
  category: [
    { required: true, message: "检测类别不能为空", trigger: "blur" }
  ],
  parameter: [
    { required: true, message: "检测参数不能为空", trigger: "blur" }
  ],
  method: [
    { required: true, message: "检测方法不能为空", trigger: "blur" }
  ]
});

// 批量录入表单参数
const batchInputForm = ref({
  categories: "",
  parameters: "",
  methods: "",
  // 新增字段
  classification: "",
  qualificationCode: "",
  limitationScope: "",
  commonAlias: "",
  qualificationDate: undefined,
  hasQualification: "0",
  // 原有字段
  status: "0",
  remark: ""
});

// 批量录入表单校验
const batchInputRules = ref({
  categories: [
    { required: true, message: "检测类别不能为空", trigger: "blur" }
  ],
  parameters: [
    { required: true, message: "检测参数不能为空", trigger: "blur" }
  ],
  methods: [
    { required: true, message: "检测方法不能为空", trigger: "blur" }
  ]
});

// 批量更新表单参数
const batchUpdateForm = ref({
  ids: [],
  // 新增字段
  classification: "",
  qualificationCode: "",
  limitationScope: "",
  commonAlias: "",
  qualificationDate: undefined,
  hasQualification: "",
  // 原有字段
  status: "",
  remark: ""
});

/** 查询技术手册列表 */
function getList() {
  loading.value = true;
  // 处理日期范围
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.beginTime = undefined;
    queryParams.value.endTime = undefined;
  }
  pageTechnicalManual(queryParams.value).then(response => {
    technicalManualList.value = response.data.rows;
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 获取检测类别选项 */
function getCategoryOptionsList() {
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data || [];
  });
}

/** 获取检测参数选项 */
function getParameterOptionsList(category) {
  getParameterOptions(category).then(response => {
    parameterOptions.value = response.data || [];
  });
}

/** 获取检测方法选项 */
function getMethodOptionsList(category, parameter) {
  getMethodOptions(category, parameter).then(response => {
    methodOptions.value = response.data || [];
  });
}

/** 检测类别变更 */
function handleCategoryChange() {
  // 输入框不需要清空参数和方法
  // 也不需要获取参数选项列表
}

/** 检测参数变更 */
function handleParameterChange() {
  // 输入框不需要清空方法
  // 也不需要获取方法选项列表
}

/** 表单检测类别变更 */
function handleFormCategoryChange() {
  // 输入框不需要清空参数和方法
  // 也不需要获取参数选项列表
}

/** 表单检测参数变更 */
function handleFormParameterChange() {
  // 输入框不需要清空方法
  // 也不需要获取方法选项列表
}

/** 添加别名 */
function addAlias() {
  if (currentAlias.value && currentAlias.value.trim()) {
    const alias = currentAlias.value.trim();
    // 检查是否已存在
    if (!form.value.aliasList) {
      form.value.aliasList = [];
    }
    if (!form.value.aliasList.includes(alias)) {
      form.value.aliasList.push(alias);
      currentAlias.value = "";
    } else {
      proxy.$modal.msgWarning("该别名已存在");
    }
  }
}

/** 删除别名 */
function removeAlias(index) {
  if (form.value.aliasList && index >= 0 && index < form.value.aliasList.length) {
    form.value.aliasList.splice(index, 1);
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    testCode: undefined,
    category: undefined,
    parameter: undefined,
    method: undefined,
    description: undefined,
    // 新增字段
    classification: undefined,
    qualificationCode: undefined,
    limitationScope: undefined,
    commonAlias: undefined,
    aliasList: [],
    qualificationDate: undefined,
    // 原有字段
    status: "0",
    remark: undefined
  };
  proxy.resetForm("technicalManualRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加技术手册";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getTechnicalManual(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改技术手册";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["technicalManualRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateTechnicalManual(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTechnicalManual(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const manualIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除技术手册编号为"' + manualIds + '"的数据项?').then(function() {
    return delTechnicalManual(manualIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导入成功处理 */
function handleImportSuccess() {
  proxy.$modal.msgSuccess("导入成功");
  getList();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('basedata/technical-manual/export', {
    ...queryParams.value
  }, `技术手册_${new Date().getTime()}.xlsx`);
}

/** 批量录入按钮操作 */
function handleBatchInput() {
  resetBatchInputForm();
  batchInputOpen.value = true;
}

/** 批量录入表单重置 */
function resetBatchInputForm() {
  batchInputForm.value = {
    categories: "",
    parameters: "",
    methods: "",
    // 新增字段
    classification: "",
    qualificationCode: "",
    limitationScope: "",
    commonAlias: "",
    qualificationDate: undefined,
    // 原有字段
    status: "0",
    remark: ""
  };
  proxy.resetForm("batchInputRef");
}

/** 取消批量录入操作 */
function cancelBatchInput() {
  batchInputOpen.value = false;
  resetBatchInputForm();
}

/** 提交批量录入表单 */
function submitBatchInputForm() {
  proxy.$refs["batchInputRef"].validate(valid => {
    if (valid) {
      batchInputTechnicalManual(batchInputForm.value).then(response => {
        proxy.$modal.msgSuccess(response.data.message || "批量录入成功");
        batchInputOpen.value = false;
        getList();
      });
    }
  });
}

/** 批量更新按钮操作 */
function handleBatchUpdate() {
  const ids = selection.value.map(item => item.id);
  if (ids.length === 0) {
    proxy.$modal.msgError("请至少选择一条记录");
    return;
  }
  resetBatchUpdateForm();
  batchUpdateForm.value.ids = ids;
  batchUpdateOpen.value = true;
}

/** 批量更新表单重置 */
function resetBatchUpdateForm() {
  batchUpdateForm.value = {
    ids: [],
    pointCount: null,
    cycleType: "",
    cycleCount: null,
    frequency: null,
    sampleCount: null,
    serviceType: ""
  };
}

/** 取消批量更新操作 */
function cancelBatchUpdate() {
  batchUpdateOpen.value = false;
  resetBatchUpdateForm();
}

/** 提交批量更新表单 */
function submitBatchUpdateForm() {
  // 检查是否有要更新的字段
  const hasUpdateField =
    batchUpdateForm.value.pointCount !== null ||
    batchUpdateForm.value.cycleType !== "" ||
    batchUpdateForm.value.cycleCount !== null ||
    batchUpdateForm.value.frequency !== null ||
    batchUpdateForm.value.sampleCount !== null ||
    batchUpdateForm.value.serviceType !== "";

  if (!hasUpdateField) {
    proxy.$modal.msgError("请至少填写一个要更新的字段");
    return;
  }

  batchUpdateTechnicalManual(batchUpdateForm.value).then(response => {
    proxy.$modal.msgSuccess(response.data.message || "批量更新成功");
    batchUpdateOpen.value = false;
    getList();
  });
}


onMounted(() => {
  getList();
  getCategoryOptionsList();
});
</script>

<style scoped>
.alias-input-container {
  width: 100%;
}

.alias-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.alias-tags-container {
  margin-top: 10px;
  min-height: 32px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.alias-hint {
  margin-top: 10px;
  padding: 8px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
