"""
合同关联报价单VO模型
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class ContractQuotationRelationModel(BaseModel):
    """
    合同关联报价单模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    contract_id: int = Field(..., description="合同ID")
    project_code: str = Field(..., description="项目编号", max_length=50)
    create_by: Optional[str] = Field(default=None, description="创建人", max_length=50)
    create_time: Optional[datetime] = Field(default=None, description="创建时间")


class ContractQuotationRelationDetailModel(BaseModel):
    """
    合同关联报价单详情模型（包含项目报价详情）
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="关联ID")
    contract_id: int = Field(..., description="合同ID")
    project_code: str = Field(..., description="项目编号")
    project_name: Optional[str] = Field(default=None, description="项目名称")
    customer_name: Optional[str] = Field(default=None, description="客户名称")
    project_manager: Optional[str] = Field(default=None, description="项目负责人")
    status: Optional[str] = Field(default=None, description="项目状态")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")


class ContractQuotationRelationCreateModel(BaseModel):
    """
    合同关联报价单创建模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    project_codes: List[str] = Field(..., description="项目编号列表")


class ContractQuotationRelationUpdateModel(BaseModel):
    """
    合同关联报价单更新模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    project_codes: List[str] = Field(..., description="项目编号列表")


class ContractQuotationRelationQueryModel(BaseModel):
    """
    合同关联报价单查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    contract_id: Optional[int] = Field(default=None, description="合同ID")
    project_code: Optional[str] = Field(default=None, description="项目编号")


class ContractQuotationRelationListModel(BaseModel):
    """
    合同关联报价单列表模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    total: int = Field(..., description="总数")
    rows: List[ContractQuotationRelationDetailModel] = Field(..., description="数据列表")
