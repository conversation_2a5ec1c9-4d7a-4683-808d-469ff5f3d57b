#!/usr/bin/env python3
"""
为任务编号25080007生成测试点位信息（简化版）
"""

import asyncio
import sys
import os
from decimal import Decimal
from datetime import datetime
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from sqlalchemy import text


async def generate_point_info_with_sql():
    """使用SQL直接查询和插入数据"""
    async for db in get_db():
        try:
            # 1. 查询任务编号为25080007的任务
            task_query = text("SELECT id, task_name, task_code FROM sampling_task WHERE task_code = '25080007'")
            task_result = await db.execute(task_query)
            task = task_result.fetchone()
            
            if not task:
                print("未找到任务编号为25080007的任务")
                return
            
            task_id = task[0]
            task_name = task[1]
            task_code = task[2]
            print(f'找到任务: ID={task_id}, 名称={task_name}, 编号={task_code}')
            
            # 2. 查询该任务的分组信息
            group_query = text("""
                SELECT id, cycle_number, cycle_type, detection_category, point_name 
                FROM sampling_task_group 
                WHERE sampling_task_id = :task_id
            """)
            group_result = await db.execute(group_query, {"task_id": task_id})
            task_groups = group_result.fetchall()
            
            print(f'找到 {len(task_groups)} 个任务分组:')
            for group in task_groups:
                print(f'分组ID: {group[0]}, 周期: {group[1]}, 类型: {group[2]}, 检测类别: {group[3]}, 点位: {group[4]}')
            
            if not task_groups:
                print("该任务没有分组信息")
                return
            
            # 3. 为每个分组生成点位信息
            for i, group in enumerate(task_groups):
                group_id = group[0]
                group_name = group[4] or f"分组{i+1}"
                
                # 检查是否已存在点位信息
                existing_query = text("SELECT id FROM sampling_point_info WHERE sampling_task_group_id = :group_id")
                existing_result = await db.execute(existing_query, {"group_id": group_id})
                existing_point = existing_result.fetchone()
                
                if existing_point:
                    print(f'分组ID {group_id} 已存在点位信息，跳过')
                    continue
                
                # 生成随机数据
                longitude = round(116.3 + random.uniform(-0.1, 0.1), 6)
                latitude = round(39.9 + random.uniform(-0.1, 0.1), 6)
                temperature = round(random.uniform(15, 30), 1)
                humidity = round(random.uniform(40, 80), 1)
                wind_speed = round(random.uniform(0, 5), 1)
                altitude = round(random.uniform(30, 100), 1)
                
                weather_conditions = ['晴天', '多云', '阴天', '小雨', '雾霾']
                wind_directions = ['东风', '南风', '西风', '北风', '东南风', '西南风', '东北风', '西北风']
                
                # 生成照片URL
                base_url = "http://127.0.0.1:9099/profile/upload/2024/12/29"
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                point_photo_name = f"point_{group_id}_{timestamp}A{random.randint(100, 999)}.jpg"
                east_photo_name = f"east_{group_id}_{timestamp}A{random.randint(100, 999)}.jpg"
                south_photo_name = f"south_{group_id}_{timestamp}A{random.randint(100, 999)}.jpg"
                west_photo_name = f"west_{group_id}_{timestamp}A{random.randint(100, 999)}.jpg"
                north_photo_name = f"north_{group_id}_{timestamp}A{random.randint(100, 999)}.jpg"
                
                # 插入点位信息
                insert_query = text("""
                    INSERT INTO sampling_point_info (
                        sampling_task_group_id, point_name,
                        point_photo_url, point_photo_name,
                        east_photo_url, east_photo_name,
                        south_photo_url, south_photo_name,
                        west_photo_url, west_photo_name,
                        north_photo_url, north_photo_name,
                        longitude, latitude, coordinate_system, altitude,
                        sampling_time, weather_condition, temperature, humidity,
                        wind_speed, wind_direction, remarks,
                        create_by, update_by, create_time, update_time
                    ) VALUES (
                        :group_id, :point_name,
                        :point_photo_url, :point_photo_name,
                        :east_photo_url, :east_photo_name,
                        :south_photo_url, :south_photo_name,
                        :west_photo_url, :west_photo_name,
                        :north_photo_url, :north_photo_name,
                        :longitude, :latitude, :coordinate_system, :altitude,
                        :sampling_time, :weather_condition, :temperature, :humidity,
                        :wind_speed, :wind_direction, :remarks,
                        :create_by, :update_by, :create_time, :update_time
                    )
                """)
                
                await db.execute(insert_query, {
                    "group_id": group_id,
                    "point_name": f"测试点位-{group_name}",
                    "point_photo_url": f"{base_url}/{point_photo_name}",
                    "point_photo_name": point_photo_name,
                    "east_photo_url": f"{base_url}/{east_photo_name}",
                    "east_photo_name": east_photo_name,
                    "south_photo_url": f"{base_url}/{south_photo_name}",
                    "south_photo_name": south_photo_name,
                    "west_photo_url": f"{base_url}/{west_photo_name}",
                    "west_photo_name": west_photo_name,
                    "north_photo_url": f"{base_url}/{north_photo_name}",
                    "north_photo_name": north_photo_name,
                    "longitude": longitude,
                    "latitude": latitude,
                    "coordinate_system": "WGS84",
                    "altitude": altitude,
                    "sampling_time": datetime.now(),
                    "weather_condition": random.choice(weather_conditions),
                    "temperature": temperature,
                    "humidity": humidity,
                    "wind_speed": wind_speed,
                    "wind_direction": random.choice(wind_directions),
                    "remarks": f"这是为任务编号25080007的{group_name}生成的测试点位信息。包含模拟的照片、位置和环境数据。",
                    "create_by": 1,
                    "update_by": 1,
                    "create_time": datetime.now(),
                    "update_time": datetime.now()
                })
                
                print(f'✅ 为分组ID {group_id} 生成点位信息: 测试点位-{group_name}')
                print(f'   经纬度: ({longitude}, {latitude})')
                print(f'   天气: {random.choice(weather_conditions)}, 温度: {temperature}°C, 湿度: {humidity}%')
                print(f'   风速: {wind_speed}m/s')
                print(f'   照片数量: 5张 (1张点位照片 + 4张环境照片)')
                print()
            
            # 提交事务
            await db.commit()
            print("🎉 所有点位信息生成完成！")
            
        except Exception as e:
            await db.rollback()
            print(f'❌ 生成失败: {e}')
            import traceback
            traceback.print_exc()
        finally:
            await db.close()


async def create_mock_photos():
    """创建模拟照片文件"""
    from pathlib import Path
    
    # 创建上传目录
    upload_dir = Path("vf_admin/upload_path/upload/2024/12/29")
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建一个简单的测试图片内容（1x1像素的PNG，但保存为jpg）
    # 这是一个最小的JPEG文件的二进制数据
    jpeg_data = bytes([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
        0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
        0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
        0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
        0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
    ])
    
    # 生成一些测试照片文件
    photo_types = ['point', 'east', 'south', 'west', 'north']
    for i in range(1, 10):  # 生成更多测试照片
        for photo_type in photo_types:
            filename = f"{photo_type}_{i}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
            filepath = upload_dir / filename
            with open(filepath, 'wb') as f:
                f.write(jpeg_data)
    
    print(f"✅ 在 {upload_dir} 创建了测试照片文件")


if __name__ == "__main__":
    print("开始为任务编号25080007生成测试点位信息...\n")
    
    # 创建模拟照片
    asyncio.run(create_mock_photos())
    
    # 生成点位信息
    asyncio.run(generate_point_info_with_sql())
