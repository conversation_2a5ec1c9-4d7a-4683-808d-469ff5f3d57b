from sqlalchemy import Column, BigInteger, Integer, DateTime, Text, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class DetectionCycleItem(Base):
    """检测周期条目表"""
    __tablename__ = 'detection_cycle_item'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    project_quotation_item_id = Column(Integer, ForeignKey('project_quotation_item.id'), nullable=False, comment='项目报价明细ID')
    
    # 业务字段
    cycle_number = Column(Integer, nullable=False, comment='周期序号（1,2,3...）')
    status = Column(Integer, default=0, comment='状态：0-未分配，1-已分配，2-已完成')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    project_quotation = relationship("ProjectQuotation", back_populates="detection_cycle_items")
    project_quotation_item = relationship("ProjectQuotationItem", back_populates="detection_cycle_items")
    sampling_task_relations = relationship("SamplingTaskCycleItem", back_populates="detection_cycle_item", cascade="all, delete-orphan")
    
    # 创建人和更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引和约束
    __table_args__ = (
        Index('idx_detection_cycle_item_project_quotation_id', 'project_quotation_id'),
        Index('idx_detection_cycle_item_project_quotation_item_id', 'project_quotation_item_id'),
        Index('idx_detection_cycle_item_status', 'status'),
        UniqueConstraint('project_quotation_item_id', 'cycle_number', name='uk_quotation_item_cycle'),
        {'comment': '检测周期条目表'}
    )
    
    def __repr__(self):
        return f"<DetectionCycleItem(id={self.id}, project_quotation_id={self.project_quotation_id}, cycle_number={self.cycle_number}, status={self.status})>"
    
    @property
    def status_label(self):
        """状态标签"""
        status_map = {
            0: '未分配',
            1: '已分配',
            2: '已完成'
        }
        return status_map.get(self.status, '未知状态')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'project_quotation_id': self.project_quotation_id,
            'project_quotation_item_id': self.project_quotation_item_id,
            'cycle_number': self.cycle_number,
            'status': self.status,
            'status_label': self.status_label,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }