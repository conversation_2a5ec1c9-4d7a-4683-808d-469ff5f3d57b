"""
测试采样任务分组编号生成功能
"""
import pytest
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.service.sampling_task_group_service import SamplingTaskGroupService


class TestSamplingTaskGroupCode:
    """测试采样任务分组编号生成"""
    
    @pytest.mark.asyncio
    async def test_group_code_generation(self, db_session: AsyncSession):
        """测试分组编号生成"""
        # 创建测试任务
        test_task = SamplingTask(
            task_name="测试任务",
            task_code="TEST001",
            project_quotation_id=1,
            description="测试任务描述",
            create_by=1
        )
        
        db_session.add(test_task)
        await db_session.flush()
        
        # 创建测试分组
        test_group = SamplingTaskGroup(
            sampling_task_id=test_task.id,
            cycle_number=1,
            cycle_type="月度",
            detection_category="废气",
            point_name="排放口1",
            cycle_item_ids='[1,2,3]',
            assigned_user_ids='[10,20]',
            status=0,
            create_by=1
        )
        
        db_session.add(test_group)
        await db_session.flush()
        
        # 生成分组编号
        group_code = f"{test_task.task_code}-{test_group.id}"
        test_group.group_code = group_code
        
        await db_session.commit()
        
        # 验证分组编号
        stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.id == test_group.id)
        result = await db_session.execute(stmt)
        saved_group = result.scalar_one()
        
        assert saved_group.group_code == f"TEST001-{test_group.id}"
        assert saved_group.group_code.startswith("TEST001-")
        
    @pytest.mark.asyncio
    async def test_group_service_creates_code(self, db_session: AsyncSession):
        """测试服务层创建分组时自动生成编号"""
        # 这里需要模拟完整的服务层调用
        # 由于依赖较多，这个测试需要在实际环境中运行
        pass
        
    def test_group_code_format(self):
        """测试分组编号格式"""
        task_code = "241200010475"
        group_id = 123
        expected_code = f"{task_code}-{group_id}"
        
        assert expected_code == "241200010475-123"
        assert "-" in expected_code
        assert expected_code.startswith(task_code)
        assert expected_code.endswith(str(group_id))


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
