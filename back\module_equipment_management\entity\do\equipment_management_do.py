"""
设备管理数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, JSON
from config.database import Base


class EquipmentManagement(Base):
    """
    设备管理表
    """

    __tablename__ = "equipment_management"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")

    # 基础信息类
    equipment_number = Column(String(100), nullable=False, unique=True, comment="设备编号（唯一值）")
    equipment_name = Column(String(200), nullable=False, comment="测量设备（器具）名称")
    enable_date = Column(Date, nullable=True, comment="启用日期")
    equipment_type = Column(String(100), nullable=True, comment="设备类型")
    model_specification = Column(String(200), nullable=True, comment="型号规格")
    factory_number = Column(String(100), nullable=True, comment="出厂编号")
    manufacturer = Column(String(200), nullable=True, comment="制造商")

    # 技术参数类
    indicator_characteristics = Column(Text, nullable=True, comment="指标特性")

    # 校准/溯源管理类
    calibration_institution = Column(String(200), nullable=True, comment="检定/校准机构")
    traceability_method = Column(String(100), nullable=True, comment="溯源方式")
    current_calibration_date = Column(Date, nullable=True, comment="本次校准/核查日期")
    certificate_number = Column(String(100), nullable=True, comment="证书编号")
    next_calibration_date = Column(Date, nullable=True, comment="下次校准/核查日期")
    interval_days = Column(String(20), nullable=True, comment="间隔日期")
    interim_check_date = Column(Date, nullable=True, comment="期间核查日期")
    calibration_remark = Column(Text, nullable=True, comment="备注（校准确认）")
    calibration_content = Column(Text, nullable=True, comment="校准内容")
    attachments = Column(JSON, nullable=True, comment="附件信息JSON数组，包含文件路径和文件名称")

    # 管理责任类
    manager = Column(String(100), nullable=True, comment="管理者")
    equipment_status = Column(String(50), nullable=True, comment="设备状态")
    equipment_status_description = Column(Text, nullable=True, comment="设备状态说明")
    location = Column(String(200), nullable=True, comment="放置地点")

    # 财务与合同类
    amount = Column(Numeric(15, 2), nullable=True, comment="金额")
    contract = Column(String(200), nullable=True, comment="合同")
    invoice = Column(String(200), nullable=True, comment="发票")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
