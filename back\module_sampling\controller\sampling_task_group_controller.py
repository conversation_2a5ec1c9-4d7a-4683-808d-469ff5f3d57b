from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_sampling.service.sampling_task_group_service import SamplingTaskGroupService
from module_sampling.dto.sampling_task_group_dto import (
    SamplingTaskGroupDTO,
    TaskGroupAssignmentRequestDTO
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.common_util import CamelCaseUtil

router = APIRouter(prefix="/sampling/task-group", tags=["采样任务分组管理"])


@router.get("/task/{task_id}", response_model=CrudResponseModel, summary="获取任务的分组列表")
async def get_task_groups(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取指定任务的所有分组记录
    
    Args:
        task_id: 采样任务ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        分组列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取任务 {task_id} 的分组列表")
        
        service = SamplingTaskGroupService(db)
        groups = await service.get_groups_by_task_id(task_id)
        
        logger.info(f"获取到 {len(groups)} 个分组记录")
        return ResponseUtil.success(data=groups, msg="获取分组列表成功")
        
    except Exception as e:
        logger.error(f"获取分组列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/assign", response_model=CrudResponseModel, summary="分组执行人指派")
async def assign_executors_to_groups(
    request: TaskGroupAssignmentRequestDTO,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    为分组指派执行人
    
    Args:
        request: 分组执行人指派请求
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        指派结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始为任务 {request.task_id} 的分组指派执行人")
        
        service = SamplingTaskGroupService(db)
        groups = await service.assign_executors_to_groups(request, current_user.user.user_id)
        
        logger.info(f"分组执行人指派成功，共处理 {len(groups)} 个分组")
        return ResponseUtil.success(data=groups, msg="分组执行人指派成功")
        
    except Exception as e:
        logger.error(f"分组执行人指派失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=CrudResponseModel, summary="获取所有分组任务")
async def get_all_groups(
    task_name: Optional[str] = Query(None, description="任务名称"),
    status: Optional[str] = Query(None, description="任务状态"),
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取所有分组任务（管理员权限）

    Args:
        task_name: 任务名称（可选）
        status: 任务状态（可选）
        page_num: 页码
        page_size: 每页数量

    Returns:
        所有分组任务列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取所有分组任务")

        service = SamplingTaskGroupService(db)
        groups = await service.get_all_groups(
            task_name=task_name,
            status=status,
            page_num=page_num,
            page_size=page_size
        )

        logger.info(f"获取到 {len(groups)} 个分组任务")
        return ResponseUtil.success(data=groups, msg="获取所有分组任务成功")

    except Exception as e:
        logger.error(f"获取所有分组任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_id}", response_model=CrudResponseModel, summary="获取用户的分组任务")
async def get_user_groups(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取分配给指定用户的分组任务

    Args:
        user_id: 用户ID
        db: 数据库会话
        current_user: 当前用户

    Returns:
        用户的分组任务列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取用户 {user_id} 的分组任务")

        service = SamplingTaskGroupService(db)
        groups = await service.get_groups_by_user_id(user_id)

        logger.info(f"获取到 {len(groups)} 个分组任务")
        return ResponseUtil.success(data=groups, msg="获取用户分组任务成功")

    except Exception as e:
        logger.error(f"获取用户分组任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{group_id}/status", response_model=CrudResponseModel, summary="更新分组状态")
async def update_group_status(
    group_id: int,
    status: int = Body(..., description="新状态：0-待执行，1-执行中，2-已完成"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    更新分组状态

    Args:
        group_id: 分组ID
        status: 新状态
        db: 数据库会话
        current_user: 当前用户

    Returns:
        更新结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 更新分组 {group_id} 状态为 {status}")

        service = SamplingTaskGroupService(db)
        result = await service.update_group_status(group_id, status, current_user.user.user_id)

        logger.info(f"分组状态更新成功")
        return ResponseUtil.success(data=result, msg="分组状态更新成功")

    except Exception as e:
        logger.error(f"更新分组状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{group_id}", summary="获取分组详情")
async def get_group_detail(
    group_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """获取分组详情"""
    try:
        logger.info(f"获取分组详情，分组ID: {group_id}")
        service = SamplingTaskGroupService(db)
        group = await service.get_group_by_id(group_id)

        if not group:
            raise HTTPException(status_code=404, detail="分组不存在")

        # 转换为驼峰命名
        camel_group = CamelCaseUtil.transform_result(group)
        return ResponseUtil.success(data=camel_group, msg="获取分组详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分组详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{group_id}/assign-users", summary="为单个分组分配执行人")
async def assign_users_to_group(
    group_id: int,
    request: dict,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """为单个分组分配执行人"""
    try:
        logger.info(f"为分组 {group_id} 分配执行人: {request.get('assigned_user_ids')}")

        service = SamplingTaskGroupService(db)

        # 获取分组
        group = await service.get_group_by_id(group_id)
        if not group:
            raise HTTPException(status_code=404, detail="分组不存在")

        # 更新执行人
        from module_sampling.dto.sampling_task_group_dto import TaskGroupAssignmentRequestDTO, SamplingTaskGroupUpdateDTO

        group_update = SamplingTaskGroupUpdateDTO(
            id=group_id,
            assigned_user_ids=request.get('assigned_user_ids', [])
        )

        assignment_request = TaskGroupAssignmentRequestDTO(
            task_id=group.sampling_task_id,
            groups=[group_update]
        )

        updated_groups = await service.assign_executors_to_groups(assignment_request, current_user.user.user_id)

        return ResponseUtil.success(data=updated_groups[0].dict() if updated_groups else None, msg="执行人分配成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配执行人失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
