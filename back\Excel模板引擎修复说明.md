# Excel模板引擎修复说明

## 📋 修复问题总览

本次修复解决了报价单导出模板的4个主要问题，并新增了方法-一览表功能。

## 🎯 问题修复详情

### 问题1：第一个sheet不能正确渲染

**问题描述**：模板包含多个工作表时，无法正确选择"项目报价单"工作表进行渲染。

**修复方案**：
```python
# 确保选择第一个sheet（项目报价单）
if "项目报价单" in wb.sheetnames:
    ws = wb["项目报价单"]
    logger.debug("选择了'项目报价单'工作表")
else:
    ws = wb.active
    logger.debug(f"使用默认工作表: {ws.title}")
```

**修复效果**：
- ✅ 优先选择"项目报价单"工作表
- ✅ 如果不存在则使用默认工作表
- ✅ 添加选择日志便于调试

### 问题2：标记行删除问题

**问题描述**：当没有项目明细或其他费用时，$itemEnd和$feeEnd标记行不能正确删除。

**修复方案**：
```python
# 项目明细为空时的处理
if not items:
    # 清空标记行并删除$itemEnd标记行
    for col_idx in range(1, ws.max_column + 1):
        cell = ws.cell(row=start_marker_row, column=col_idx)
        if cell.value and isinstance(cell.value, str) and cell.value.startswith("$"):
            cell.value = ""
    ws.delete_rows(end_marker_row)
    logger.debug(f"删除了$itemEnd标记行: {end_marker_row}")
    return

# 其他费用为空时的处理
if not other_fees:
    # 清空标记行并删除结束标记行
    for col_idx in range(1, ws.max_column + 1):
        cell = ws.cell(row=start_marker_row, column=col_idx)
        if cell.value and isinstance(cell.value, str) and cell.value.startswith("$"):
            cell.value = ""
    ws.delete_rows(end_marker_row)
    logger.debug(f"删除了$feeEnd标记行: {end_marker_row}")
    return
```

**修复效果**：
- ✅ 空数据时正确删除$itemEnd标记行
- ✅ 空数据时正确删除$feeEnd标记行
- ✅ 添加删除操作日志

### 问题3：检测费用合计合并单元格

**问题描述**：检测费用合计需要占据10列并居中显示。

**修复方案**：
```python
# 定义需要特别处理的合并单元格
special_merges = [
    {"keywords": ["监测费小计", "检测费小计", "检测费用合计"], "cols": 10, "alignment": "center"},
    {"keywords": ["优惠后小计"], "cols": 10, "alignment": "center"},
    {"keywords": ["总计", "合计"], "cols": 10, "alignment": "center"},
    # ... 其他合并规则
]

# 应用合并单元格和对齐方式
if not is_already_merged:
    try:
        end_col = min(col_idx + special_merge["cols"] - 1, ws.max_column)
        if end_col > col_idx:
            ws.merge_cells(
                start_row=row_idx,
                start_column=col_idx,
                end_row=row_idx,
                end_column=end_col,
            )
            
            # 设置对齐方式
            alignment_type = special_merge.get("alignment", "center")
            from openpyxl.styles import Alignment
            if alignment_type == "center":
                cell.alignment = Alignment(horizontal="center", vertical="center")
            elif alignment_type == "left":
                cell.alignment = Alignment(horizontal="left", vertical="center")
```

**修复效果**：
- ✅ 检测费用合计占据10列
- ✅ 文字居中对齐
- ✅ 支持多种对齐方式配置

### 问题4：其他费用合并单元格

**问题描述**：其他费用的类别、单价、数量需要各占用2个单元格，新增行也需要正确合并。

**修复方案**：
```python
def _fill_other_fee_row(self, ws, row_idx: int, fee: Dict[str, Any]):
    """填充单行其他费用数据"""
    # C列：费用名称（合并C:D）
    cell_c = ws.cell(row=row_idx, column=3)
    cell_c.value = fee.get("fee_name", fee.get("feeName", ""))
    self._ensure_other_fee_merge(ws, row_idx, 3, 4, "费用名称")

    # E列：单价（合并E:F）
    cell_e = ws.cell(row=row_idx, column=5)
    cell_e.value = f"{float(fee.get('unit_price', fee.get('unitPrice', 0))):.2f}"
    self._ensure_other_fee_merge(ws, row_idx, 5, 6, "单价")

    # G列：数量（合并G:H）
    cell_g = ws.cell(row=row_idx, column=7)
    cell_g.value = fee.get("quantity", 1)
    self._ensure_other_fee_merge(ws, row_idx, 7, 8, "数量")

def _ensure_other_fee_merge(self, ws, row_idx: int, start_col: int, end_col: int, field_name: str):
    """确保其他费用的合并单元格存在"""
    # 检查是否已经合并
    is_already_merged = False
    for merged_range in ws.merged_cells.ranges:
        if (merged_range.min_row == row_idx and merged_range.max_row == row_idx and
            merged_range.min_col == start_col and merged_range.max_col == end_col):
            is_already_merged = True
            break
    
    if not is_already_merged:
        try:
            ws.merge_cells(
                start_row=row_idx,
                start_column=start_col,
                end_row=row_idx,
                end_column=end_col
            )
            logger.debug(f"创建其他费用合并单元格: {field_name} 行{row_idx} 列{start_col}-{end_col}")
        except Exception as e:
            logger.warning(f"创建其他费用合并单元格失败 {field_name}: {e}")
```

**修复效果**：
- ✅ 费用名称占用2个单元格（C:D）
- ✅ 单价占用2个单元格（E:F）
- ✅ 数量占用2个单元格（G:H）
- ✅ 新增行自动应用合并单元格
- ✅ 重复合并检查避免错误

## 🎨 新增功能：方法-一览表

### 功能描述
在报价单导出时自动生成"方法-一览表"工作表，包含所有检测方法的汇总信息。

### 实现方案
```python
def _render_method_overview_sheet(self, wb, template_data: Dict[str, Any]):
    """渲染方法-一览表sheet"""
    # 检查是否存在"方法-一览表"sheet
    method_sheet_name = "方法-一览表"
    if method_sheet_name not in wb.sheetnames:
        logger.warning(f"模板中不存在'{method_sheet_name}'工作表，跳过渲染")
        return
    
    # 获取方法-一览表工作表
    method_ws = wb[method_sheet_name]
    
    # 准备方法一览表数据（去重处理）
    method_overview_data = self._prepare_method_overview_data(items)
    
    # 渲染表头和数据行
    headers = ["序号", "检测类别", "检测项目", "检测方法", "是否盖章", "是否分包"]
    # ... 渲染逻辑
```

### 功能特性
- ✅ 自动去重相同的检测方法
- ✅ 根据技术手册资质判断是否盖章
- ✅ 支持分包状态显示
- ✅ 完整的表格样式和边框

## 📊 数据结构说明

### 项目明细数据结构
```python
{
    "category": "水和废水",           # 检测类别
    "pointName": "进水口",           # 点位名称
    "parameters": ["pH值", "COD"],   # 监测项目
    "method": "玻璃电极法",          # 检测方法
    "qualification_code": "CMA001",  # 资质编号
    "has_qualification": "0",        # 资质状态：0=有资质，1=无资质
    "is_subcontract": "0"           # 分包状态：0=不分包，1=分包
}
```

### 其他费用数据结构
```python
{
    "fee_name": "差旅费",           # 费用名称
    "unit_price": 100.00,          # 单价
    "quantity": 2,                 # 数量
    "total_price": 200.00,         # 总价
    "remark": "往返交通费"         # 备注
}
```

### 方法-一览表数据结构
```python
{
    "序号": "1",
    "检测类别": "水和废水",
    "检测项目": "pH值",
    "检测方法": "玻璃电极法",
    "是否盖章": "是",              # 根据has_qualification判断
    "是否分包": "否"               # 根据is_subcontract判断
}
```

## 🔧 模板文件要求

### 工作表结构
1. **项目报价单**（第一个工作表）
   - 包含基本信息区域
   - 包含$样品类别和$itemEnd标记
   - 包含$差旅费和$feeEnd标记
   - 包含费用汇总区域

2. **方法-一览表**（第二个工作表）
   - 表头从第2行开始
   - 数据从第3行开始填充

### 标记说明
- `$样品类别`：项目明细开始标记
- `$itemEnd`：项目明细结束标记
- `$差旅费`：其他费用开始标记
- `$feeEnd`：其他费用结束标记

## 🚀 使用说明

### 1. 模板准备
- 确保模板文件包含"项目报价单"和"方法-一览表"两个工作表
- 在项目报价单中正确放置标记
- 设置好基础的单元格样式和合并

### 2. 数据准备
- 项目明细数据包含完整的字段信息
- 其他费用数据包含费用名称、单价、数量等
- 技术手册资质信息已正确关联

### 3. 导出操作
- 调用Excel模板引擎的render_template方法
- 传入完整的模板数据
- 系统自动处理所有渲染逻辑

## 📝 注意事项

1. **模板兼容性**：确保模板文件格式正确，工作表名称准确
2. **数据完整性**：确保传入的数据结构完整，字段名称正确
3. **资质查询**：方法-一览表功能需要技术手册资质数据支持
4. **错误处理**：系统包含完善的错误处理和日志记录
5. **性能考虑**：大量数据时注意内存使用和处理时间

## 🔍 故障排除

### 常见问题
1. **工作表选择错误**：检查模板文件是否包含"项目报价单"工作表
2. **标记行未删除**：检查标记文本是否正确，数据是否为空
3. **合并单元格失败**：检查单元格是否已被占用或合并
4. **方法-一览表为空**：检查模板数据中是否包含items字段

### 调试方法
1. 开启详细日志记录
2. 检查模板文件结构
3. 验证数据格式和内容
4. 测试小数据集功能
