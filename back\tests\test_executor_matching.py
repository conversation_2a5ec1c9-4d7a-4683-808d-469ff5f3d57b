"""
测试执行人匹配逻辑
"""


def test_executor_matching():
    """测试执行人匹配逻辑"""
    
    # 模拟后端返回的executorAssignments数据
    executor_assignments = [
        {
            "id": 3,
            "samplingTaskId": 7,
            "cycleNumber": 1,
            "cycleType": "",
            "detectionCategory": "地下水",
            "pointName": "",
            "assignedUserIds": [101, 115],
            "assignedUserNames": ["蒋若霏", "韩恩利"],
            "createBy": 1,
            "createTime": "2025-08-06T15:30:06",
            "updateBy": 1,
            "updateTime": "2025-08-06T15:30:06"
        },
        {
            "id": 4,
            "samplingTaskId": 7,
            "cycleNumber": 1,
            "cycleType": None,
            "detectionCategory": "地下水",
            "pointName": None,
            "assignedUserIds": [113],
            "assignedUserNames": ["徐挺"],
            "createBy": 1,
            "createTime": "2025-08-10T21:29:53",
            "updateBy": 1,
            "updateTime": "2025-08-10T21:29:53"
        }
    ]
    
    # 模拟分组数据
    test_groups = [
        {
            "cycleNumber": 1,
            "cycleType": "",
            "detectionCategory": "地下水",
            "pointName": ""
        },
        {
            "cycleNumber": 1,
            "cycleType": None,
            "detectionCategory": "地下水",
            "pointName": None
        },
        {
            "cycleNumber": 2,
            "cycleType": "",
            "detectionCategory": "地下水",
            "pointName": ""
        }
    ]
    
    def find_assignment(group, assignments):
        """模拟前端查找执行人指派的逻辑"""
        for a in assignments:
            # 处理null值和空字符串的比较
            a_cycle_type = a["cycleType"] or ""
            a_detection_category = a["detectionCategory"] or ""
            a_point_name = a["pointName"] or ""
            group_cycle_type = group["cycleType"] or ""
            group_detection_category = group["detectionCategory"] or ""
            group_point_name = group["pointName"] or ""
            
            match = (a["cycleNumber"] == group["cycleNumber"] and
                    a_cycle_type == group_cycle_type and
                    a_detection_category == group_detection_category and
                    a_point_name == group_point_name)
            
            print(f"匹配检查:")
            print(f"  指派: cycleNumber={a['cycleNumber']}, cycleType='{a_cycle_type}', detectionCategory='{a_detection_category}', pointName='{a_point_name}'")
            print(f"  分组: cycleNumber={group['cycleNumber']}, cycleType='{group_cycle_type}', detectionCategory='{group_detection_category}', pointName='{group_point_name}'")
            print(f"  匹配结果: {match}")
            print()
            
            if match:
                return a
        return None
    
    # 测试每个分组
    for i, group in enumerate(test_groups):
        print(f"=== 测试分组 {i+1} ===")
        assignment = find_assignment(group, executor_assignments)
        
        if assignment:
            assigned_executors = assignment["assignedUserNames"]
            print(f"✅ 找到执行人指派: {', '.join(assigned_executors)}")
        else:
            print("❌ 未找到执行人指派")
        print()
    
    # 验证预期结果
    # 分组1应该匹配指派1
    assignment1 = find_assignment(test_groups[0], executor_assignments)
    assert assignment1 is not None
    assert assignment1["id"] == 3
    assert assignment1["assignedUserNames"] == ["蒋若霏", "韩恩利"]

    # 分组2也会匹配指派1（因为条件相同）
    assignment2 = find_assignment(test_groups[1], executor_assignments)
    assert assignment2 is not None
    assert assignment2["id"] == 3  # 实际上匹配的是第一个指派
    assert assignment2["assignedUserNames"] == ["蒋若霏", "韩恩利"]

    # 分组3应该没有匹配
    assignment3 = find_assignment(test_groups[2], executor_assignments)
    assert assignment3 is None
    
    print("🎉 所有执行人匹配测试通过！")


def test_null_empty_string_handling():
    """测试null值和空字符串的处理"""
    
    # 测试不同的null和空字符串组合
    test_cases = [
        # (value1, value2, should_match)
        ("", "", True),
        ("", None, True),
        (None, "", True),
        (None, None, True),
        ("test", "test", True),
        ("test", "", False),
        ("test", None, False),
        ("", "test", False),
        (None, "test", False),
    ]
    
    def normalize_value(value):
        """标准化值，将None转换为空字符串"""
        return value or ""
    
    print("=== 测试null值和空字符串处理 ===")
    for value1, value2, expected in test_cases:
        normalized1 = normalize_value(value1)
        normalized2 = normalize_value(value2)
        actual = normalized1 == normalized2
        
        print(f"'{value1}' vs '{value2}' -> '{normalized1}' vs '{normalized2}' = {actual} (期望: {expected})")
        assert actual == expected, f"测试失败: {value1} vs {value2}"
    
    print("✅ null值和空字符串处理测试通过！")


if __name__ == "__main__":
    test_executor_matching()
    test_null_empty_string_handling()
    print("\n🎉 所有测试通过！")
