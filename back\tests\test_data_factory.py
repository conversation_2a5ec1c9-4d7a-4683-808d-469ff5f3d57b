"""
测试数据工厂
用于生成审批流程测试所需的各种测试数据
"""

from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord


class TestDataFactory:
    """
    测试数据工厂类
    """

    @staticmethod
    async def create_approval_roles(db: AsyncSession) -> Dict[str, SysRole]:
        """
        创建审批相关的角色
        
        :param db: 数据库会话
        :return: 角色字典
        """
        roles = {
            "market": SysRole(
                role_id=1,
                role_name="市场审批人员",
                role_key="market-approver",
                role_sort=10,
                status="0",
                create_by="admin",
                create_time=datetime.now()
            ),
            "lab": SysRole(
                role_id=2,
                role_name="实验室审批人员", 
                role_key="lab-approver",
                role_sort=11,
                status="0",
                create_by="admin",
                create_time=datetime.now()
            ),
            "field": SysRole(
                role_id=3,
                role_name="现场审批人员",
                role_key="field-approver", 
                role_sort=12,
                status="0",
                create_by="admin",
                create_time=datetime.now()
            )
        }
        
        db.add_all(roles.values())
        await db.flush()
        return roles

    @staticmethod
    async def create_approval_users(db: AsyncSession) -> Dict[str, SysUser]:
        """
        创建审批相关的用户
        
        :param db: 数据库会话
        :return: 用户字典
        """
        users = {
            "market": SysUser(
                user_id=1,
                user_name="market_user",
                nick_name="市场用户",
                password="$2b$12$test_password_hash",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            ),
            "lab": SysUser(
                user_id=2,
                user_name="lab_user",
                nick_name="实验室用户",
                password="$2b$12$test_password_hash",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            ),
            "field": SysUser(
                user_id=3,
                user_name="field_user",
                nick_name="现场用户",
                password="$2b$12$test_password_hash",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            ),
            "creator": SysUser(
                user_id=4,
                user_name="creator_user",
                nick_name="创建用户",
                password="$2b$12$test_password_hash",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
        }
        
        db.add_all(users.values())
        await db.flush()
        return users

    @staticmethod
    async def create_user_role_associations(
        db: AsyncSession, 
        users: Dict[str, SysUser], 
        roles: Dict[str, SysRole]
    ) -> List[SysUserRole]:
        """
        创建用户角色关联
        
        :param db: 数据库会话
        :param users: 用户字典
        :param roles: 角色字典
        :return: 用户角色关联列表
        """
        user_roles = [
            SysUserRole(user_id=users["market"].user_id, role_id=roles["market"].role_id),
            SysUserRole(user_id=users["lab"].user_id, role_id=roles["lab"].role_id),
            SysUserRole(user_id=users["field"].user_id, role_id=roles["field"].role_id)
        ]
        
        db.add_all(user_roles)
        await db.flush()
        return user_roles

    @staticmethod
    async def create_test_quotations(
        db: AsyncSession, 
        creator_user: SysUser
    ) -> Dict[str, ProjectQuotation]:
        """
        创建测试项目报价
        
        :param db: 数据库会话
        :param creator_user: 创建用户
        :return: 项目报价字典
        """
        quotations = {
            "sampling": ProjectQuotation(
                id=1,
                project_name="一般采样测试项目",
                project_code="SAMPLING001",
                business_type="sampling",
                status="0",
                customer_name="测试客户A",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            ),
            "sample": ProjectQuotation(
                id=2,
                project_name="送样测试项目",
                project_code="SAMPLE001",
                business_type="sample",
                status="0",
                customer_name="测试客户B",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            ),
            "complex": ProjectQuotation(
                id=3,
                project_name="复杂项目测试",
                project_code="COMPLEX001",
                business_type="sampling",
                status="0",
                customer_name="测试客户C",
                customer_address="测试地址",
                customer_contact="测试联系人",
                customer_phone="***********",
                inspected_party="被检方",
                inspected_contact="被检联系人",
                inspected_phone="***********",
                inspected_address="被检地址",
                project_manager="项目经理",
                market_manager="市场经理",
                technical_manager="技术经理",
                commission_date=datetime.now().date(),
                remark="测试备注",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            )
        }
        
        db.add_all(quotations.values())
        await db.flush()
        return quotations

    @staticmethod
    def create_current_user_model(user: SysUser) -> CurrentUserModel:
        """
        创建当前用户模型
        
        :param user: 用户实体
        :return: 当前用户模型
        """
        return CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(
                user_id=user.user_id,
                user_name=user.user_name,
                nick_name=user.nick_name,
                email=getattr(user, 'email', None),
                phonenumber=getattr(user, 'phonenumber', None),
                status=user.status,
                dept_id=getattr(user, 'dept_id', None),
                user_type=getattr(user, 'user_type', None),
                avatar=getattr(user, 'avatar', None),
                del_flag=user.del_flag,
                login_ip=getattr(user, 'login_ip', None),
                login_date=getattr(user, 'login_date', None),
                create_by=user.create_by,
                create_time=user.create_time,
                update_by=getattr(user, 'update_by', None),
                update_time=getattr(user, 'update_time', None),
                remark=getattr(user, 'remark', None)
            )
        )

    @staticmethod
    async def create_approval_records(
        db: AsyncSession,
        quotation: ProjectQuotation,
        users: Dict[str, SysUser],
        creator_user: SysUser
    ) -> List[ProjectQuotationApprovalRecord]:
        """
        创建审批记录
        
        :param db: 数据库会话
        :param quotation: 项目报价
        :param users: 用户字典
        :param creator_user: 创建用户
        :return: 审批记录列表
        """
        records = [
            ProjectQuotationApprovalRecord(
                project_quotation_id=quotation.id,
                approver_type="market",
                approver_user_id=users["market"].user_id,
                approval_stage=1,
                is_required="1",
                approval_status="pending",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            ),
            ProjectQuotationApprovalRecord(
                project_quotation_id=quotation.id,
                approver_type="lab",
                approver_user_id=users["lab"].user_id,
                approval_stage=2,
                is_required="1",
                approval_status="pending",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            ),
            ProjectQuotationApprovalRecord(
                project_quotation_id=quotation.id,
                approver_type="field",
                approver_user_id=users["field"].user_id,
                approval_stage=2,
                is_required="1" if quotation.business_type == "sampling" else "0",
                approval_status="pending",
                create_by=creator_user.user_id,
                create_time=datetime.now()
            )
        ]
        
        db.add_all(records)
        await db.flush()
        return records

    @staticmethod
    async def create_complete_test_environment(db: AsyncSession) -> Dict[str, Any]:
        """
        创建完整的测试环境
        
        :param db: 数据库会话
        :return: 包含所有测试数据的字典
        """
        # 创建角色
        roles = await TestDataFactory.create_approval_roles(db)
        
        # 创建用户
        users = await TestDataFactory.create_approval_users(db)
        
        # 创建用户角色关联
        user_roles = await TestDataFactory.create_user_role_associations(db, users, roles)
        
        # 创建项目报价
        quotations = await TestDataFactory.create_test_quotations(db, users["creator"])
        
        # 提交所有更改
        await db.commit()
        
        return {
            "roles": roles,
            "users": users,
            "user_roles": user_roles,
            "quotations": quotations,
            "current_users": {
                key: TestDataFactory.create_current_user_model(user)
                for key, user in users.items()
            }
        }
