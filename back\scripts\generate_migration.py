"""
生成数据库迁移脚本
"""
import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def generate_migration(message: str = "auto migration"):
    """
    生成数据库迁移脚本

    :param message: 迁移消息
    :return: None
    """
    try:
        # 确保migrations/versions目录存在
        versions_dir = project_root / "migrations" / "versions"
        os.makedirs(versions_dir, exist_ok=True)

        # 执行alembic命令生成迁移脚本
        cmd = ["alembic", "revision", "--autogenerate", "-m", message]
        subprocess.run(cmd, check=True)
        print(f"成功生成迁移脚本: {message}")
    except subprocess.CalledProcessError as e:
        print(f"生成迁移脚本失败: {e}")
    except Exception as e:
        print(f"发生错误: {e}")


def apply_migration():
    """
    应用数据库迁移

    :return: None
    """
    try:
        # 执行alembic命令应用迁移
        cmd = ["alembic", "upgrade", "head"]
        subprocess.run(cmd, check=True)
        print("成功应用迁移")
    except subprocess.CalledProcessError as e:
        print(f"应用迁移失败: {e}")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="数据库迁移工具")
    parser.add_argument("action", choices=["generate", "apply"], help="执行的操作: generate(生成迁移) 或 apply(应用迁移)")
    parser.add_argument("-m", "--message", default="auto migration", help="迁移消息 (仅用于生成迁移)")

    args = parser.parse_args()

    if args.action == "generate":
        generate_migration(args.message)
    elif args.action == "apply":
        apply_migration()
