#!/usr/bin/env python3
"""
简单测试点位信息功能
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_simple_point_info():
    """简单测试点位信息功能"""
    
    print("🗺️ 简单测试点位信息功能...")
    
    # 使用一个存在的分组ID
    group_id = 20  # 从之前的测试中我们知道这个分组存在
    
    print(f"\n1. 测试获取分组 {group_id} 的点位信息:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/point-info/group/{group_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 200:
                point_info = data.get('data')
                if point_info:
                    print(f"   ✅ 已有点位信息")
                    return point_info
                else:
                    print(f"   ⚠️  分组 {group_id} 暂无点位信息")
                    return None
            else:
                print(f"   ❌ API调用失败: {data.get('msg')}")
                return None
        else:
            print(f"   ❌ HTTP请求失败")
            return None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

def test_create_simple_point_info():
    """测试创建简单点位信息"""
    
    print(f"\n2. 测试创建简单点位信息:")
    
    group_id = 20
    
    # 创建最简单的点位信息数据
    point_data = {
        "samplingTaskGroupId": group_id,
        "pointName": "测试点位",
        "longitude": 116.397428,
        "latitude": 39.90923
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/point-info/create",
            headers=HEADERS,
            json=point_data,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 200:
                point_info = data.get('data')
                print(f"   ✅ 点位信息创建成功")
                print(f"     点位ID: {point_info.get('id')}")
                print(f"     点位名称: {point_info.get('pointName')}")
                print(f"     经度: {point_info.get('longitude')}")
                print(f"     纬度: {point_info.get('latitude')}")
                return point_info
            else:
                print(f"   ❌ 创建失败: {data.get('msg')}")
                return None
        else:
            print(f"   ❌ HTTP请求失败")
            return None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

if __name__ == "__main__":
    # 测试获取现有点位信息
    existing_point_info = test_simple_point_info()
    
    # 如果没有现有点位信息，创建新的
    if not existing_point_info:
        point_info = test_create_simple_point_info()
    else:
        point_info = existing_point_info
    
    if point_info:
        print("\n🎯 点位信息功能测试成功！")
    else:
        print("\n💥 点位信息功能测试失败")
