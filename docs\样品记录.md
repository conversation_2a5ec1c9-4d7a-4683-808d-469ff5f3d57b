# 样品记录管理
1. 对于分配给用户的执行任务（SamplingTaskAssignment），在采样执行列表(/sampling/execution),包括我的执行任务和所有执行任务列表中，点击开始执行的时候，创建采样样品记录。
2. 每条执行任务，需要根据该记录关联的周期条目，找到关联的项目报价明细，然后根据报价明细中的采样频次和样品数，计算需要生成的采样记录数，采样记录数为频次*样品数。由于关联的周期条目数量可以有很多，取频次*样品数的最大值，在采样执行列表(/sampling/execution),包括我的执行任务和所有执行任务列表中，点击开始执行的时候，创建采样样品记录。比如执行任务A，关联了3个周期条目，分别为A1,A2,A3，A1关联的项目报价明细的采样频次为1，样品数为2，A2关联的项目报价明细的采样频次为2，样品数为1，A3关联的项目报价明细的采样频次为1，样品数为1，则需要生成的采样记录数为2*1=2条。
3. 样品记录表需要有样品序号，从1开始计数。
2. 项目报价明细的检测信息关联技术手册如果是现场直读类型的,对应字段TechnicalManual.analysis_type = ON_SITE_CHECK 的明细需排除生成样品记录，不用生成样品记录。
3. 在采样执行列表（/sampling/execution）的操作中增加样品管理按钮，点击后查看和管理该任务的样品记录列表，列表展示样品序号、样品类型、采样周期、样品状态、采集时间、点位名称等信息， 管理界面弹窗显示。
