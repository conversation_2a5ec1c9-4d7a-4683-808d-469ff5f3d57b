# 采样分瓶组功能使用说明

## 功能概述

采样分瓶组功能是LIMS系统中的重要模块，用于将样品记录按照检测方法自动分配到相应的瓶组中，实现样品的科学管理和追踪。

## 核心功能

### 1. 瓶组自动分配
- 根据样品关联的检测方法自动匹配对应的瓶组
- 相同瓶组的样品归为一组，减少瓶组数量
- 未匹配到瓶组的样品使用默认瓶组，每个样品单独生成记录

### 2. 瓶组编号生成
- 自动生成瓶组编号：任务编号-B序号（如：ST2408001-B001）
- 确保编号唯一性和规范性

### 3. 瓶组信息管理
- 展示瓶组类型、容器容量、存储方式等详细信息
- 支持瓶组状态管理（待采集、已采集、已送检）
- 提供瓶组与样品的关联关系查看

## 数据库设计

### 主要数据表

1. **bottle_maintenance_method** - 瓶组与检测方法关联表
   - 建立瓶组管理与检测方法的对应关系
   - 支持一个瓶组对应多个检测方法

2. **sampling_bottle_group** - 采样瓶组表
   - 存储瓶组基本信息和状态
   - 记录关联的样品数量和检测方法

3. **sampling_bottle_group_sample** - 瓶组样品关联表
   - 建立瓶组与样品记录的多对多关系

4. **sampling_bottle_group_sequence** - 瓶组编号序列表
   - 用于生成唯一的瓶组序号

## API接口

### 后端接口

1. **POST** `/sampling/bottle-groups/generate/{task_id}`
   - 为指定任务生成瓶组
   - 返回生成的瓶组列表和统计信息

2. **GET** `/sampling/bottle-groups/task/{task_id}`
   - 获取任务的瓶组列表
   - 包含瓶组详细信息和关联样品

3. **GET** `/sampling/bottle-groups/{bottle_group_id}`
   - 获取瓶组详情
   - 包含瓶组信息和关联样品列表

4. **PUT** `/sampling/bottle-groups/{bottle_group_id}/status`
   - 更新瓶组状态
   - 支持批量状态更新

5. **GET** `/sampling/bottle-groups/{bottle_group_id}/samples`
   - 获取瓶组关联的样品列表

### 前端接口

1. **generateBottleGroups(taskId)** - 生成瓶组
2. **getBottleGroupsByTask(taskId)** - 获取任务瓶组列表
3. **updateBottleGroupStatus(bottleGroupId, status)** - 更新瓶组状态
4. **getBottleGroupStatistics(taskId)** - 获取瓶组统计信息

## 使用流程

### 1. 配置瓶组与检测方法关联
在瓶组管理模块中，为每个瓶组配置对应的检测方法：
```sql
INSERT INTO bottle_maintenance_method (bottle_maintenance_id, detection_method) 
VALUES (1, 'GB/T 5750.4-2006');
```

### 2. 生成样品记录
在采样执行过程中，系统会自动生成样品记录，包含检测方法信息。

### 3. 生成瓶组
在样品管理弹窗的"瓶组信息"tab中，点击"生成瓶组"按钮：
- 系统自动分析样品记录的检测方法
- 匹配对应的瓶组信息
- 生成瓶组记录和编号
- 建立瓶组与样品的关联关系

### 4. 瓶组管理
- 查看瓶组列表和详细信息
- 更新瓶组状态（采集、送检）
- 查看瓶组关联的样品
- 批量操作瓶组

## 前端界面

### 样品管理弹窗增强
在采样执行列表的样品管理弹窗中，新增"瓶组信息"tab页：

#### 瓶组操作区
- **生成瓶组**：为当前任务生成瓶组
- **批量采集**：批量更新瓶组状态为已采集
- **批量送检**：批量更新瓶组状态为已送检
- **刷新**：刷新瓶组列表

#### 瓶组列表
显示瓶组的详细信息：
- 瓶组编号
- 瓶组类型
- 容器容量
- 检测方法
- 样品数量
- 瓶组状态
- 存储方式
- 样品时效

#### 瓶组统计
显示瓶组的统计信息：
- 瓶组总数
- 待采集数量
- 已采集数量
- 已送检数量

## 业务规则

### 瓶组分配规则
1. **检测方法匹配**：根据样品的检测方法查找对应的瓶组
2. **相同瓶组合并**：相同瓶组ID的样品归为一组
3. **默认瓶组处理**：未匹配到瓶组的样品使用默认瓶组（ID=0），每个样品单独生成记录
4. **编号生成**：按任务编号+瓶组序号生成唯一编号

### 状态管理
- **0-待采集**：瓶组已生成，等待采集
- **1-已采集**：瓶组已完成采集
- **2-已送检**：瓶组已送检

## 注意事项

1. **数据库迁移**：首次使用需要执行数据库迁移脚本创建相关表
2. **瓶组配置**：需要预先配置瓶组与检测方法的关联关系
3. **权限控制**：瓶组操作需要相应的权限
4. **数据一致性**：删除瓶组时会自动删除关联的样品关系

## 故障排除

### 常见问题

1. **瓶组生成失败**
   - 检查样品记录是否存在
   - 确认检测方法信息是否完整
   - 验证数据库连接是否正常

2. **瓶组匹配不准确**
   - 检查瓶组与检测方法关联配置
   - 确认检测方法名称是否一致

3. **前端界面异常**
   - 检查API接口是否正常
   - 确认前端路由配置
   - 验证权限设置

## 扩展功能

### 未来可扩展的功能
1. 瓶组模板管理
2. 瓶组批次追踪
3. 瓶组库存管理
4. 瓶组使用统计分析
5. 瓶组二维码生成

## 技术架构

### 后端技术栈
- FastAPI：Web框架
- SQLAlchemy：ORM框架
- Pydantic：数据验证
- AsyncIO：异步处理

### 前端技术栈
- Vue 3：前端框架
- Element Plus：UI组件库
- Axios：HTTP客户端

### 数据库
- MySQL/PostgreSQL：关系型数据库
- 支持事务处理和外键约束
