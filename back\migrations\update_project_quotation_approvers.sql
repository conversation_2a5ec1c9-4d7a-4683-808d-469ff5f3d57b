-- 修改项目报价表的审核人字段结构
-- 1. 创建项目报价审核人关联表
CREATE TABLE IF NOT EXISTS project_quotation_approver (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_quotation_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    create_by VARCHAR(50),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_quotation_id) REFERENCES project_quotation(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    UNIQUE(project_quotation_id, user_id)
);

-- 2. 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_project_quotation_approver_quotation_id ON project_quotation_approver(project_quotation_id);
CREATE INDEX IF NOT EXISTS idx_project_quotation_approver_user_id ON project_quotation_approver(user_id);

-- 3. 迁移现有数据（如果approvers字段有数据的话）
-- 注意：这里假设现有的approvers字段存储的是用户名，需要根据实际情况调整
-- 由于当前是字符串格式，暂时保留原字段，后续可以手动清理

-- 4. 添加注释
COMMENT ON TABLE project_quotation_approver IS '项目报价审核人关联表';
COMMENT ON COLUMN project_quotation_approver.id IS '主键ID';
COMMENT ON COLUMN project_quotation_approver.project_quotation_id IS '项目报价ID';
COMMENT ON COLUMN project_quotation_approver.user_id IS '审核人用户ID';
COMMENT ON COLUMN project_quotation_approver.create_by IS '创建人';
COMMENT ON COLUMN project_quotation_approver.create_time IS '创建时间';
COMMENT ON COLUMN project_quotation_approver.update_by IS '更新人';
COMMENT ON COLUMN project_quotation_approver.update_time IS '更新时间';