"""
调试瓶组生成问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# 导入必要的模块
from config.database import AsyncSessionLocal
from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.sample_record_do import SampleRecord


async def debug_task_data(task_id: int):
    """调试任务数据"""
    print(f"调试任务 {task_id} 的数据...")
    
    try:
        async with AsyncSessionLocal() as session:
            # 1. 检查任务是否存在
            from module_sampling.entity.do.sampling_task_do import SamplingTask
            task_stmt = select(SamplingTask).where(SamplingTask.id == task_id)
            task_result = await session.execute(task_stmt)
            task = task_result.scalar_one_or_none()
            
            if not task:
                print(f"✗ 任务 {task_id} 不存在")
                return False
            
            print(f"✓ 任务存在: {task.task_code} - {task.task_name}")
            
            # 2. 检查任务分组
            group_stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.sampling_task_id == task_id)
            group_result = await session.execute(group_stmt)
            groups = group_result.scalars().all()
            
            if not groups:
                print(f"✗ 任务 {task_id} 没有分组记录")
                return False
            
            print(f"✓ 找到 {len(groups)} 个分组:")
            for group in groups:
                print(f"  - 分组ID: {group.id}, 项目: {group.project_name}, 客户: {group.customer_name}")
            
            # 3. 检查样品记录
            group_ids = [group.id for group in groups]
            sample_stmt = select(SampleRecord).where(SampleRecord.sampling_task_group_id.in_(group_ids))
            sample_result = await session.execute(sample_stmt)
            samples = sample_result.scalars().all()
            
            if not samples:
                print(f"✗ 任务 {task_id} 没有样品记录")
                return False
            
            print(f"✓ 找到 {len(samples)} 个样品记录:")
            for i, sample in enumerate(samples[:5]):  # 只显示前5个
                print(f"  - 样品{i+1}: ID={sample.id}, 序号={sample.sample_number}, 方法={sample.detection_method}")
            
            if len(samples) > 5:
                print(f"  ... 还有 {len(samples) - 5} 个样品")
            
            return True
            
    except Exception as e:
        print(f"✗ 调试任务数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def debug_bottle_group_service(task_id: int):
    """调试瓶组服务"""
    print(f"\n调试瓶组服务...")
    
    try:
        async with AsyncSessionLocal() as session:
            service = SamplingBottleGroupService(session)
            
            # 测试获取样品记录方法
            print("测试获取样品记录方法...")
            sample_records = await service._get_sample_records_with_detection(task_id)
            
            if not sample_records:
                print("✗ 没有获取到样品记录")
                return False
            
            print(f"✓ 获取到 {len(sample_records)} 个样品记录:")
            for i, record in enumerate(sample_records[:3]):  # 只显示前3个
                print(f"  - 样品{i+1}: ID={record.sample_id}, 方法={record.detection_method}")
            
            # 测试瓶组分组方法
            print("\n测试瓶组分组方法...")
            bottle_group_map = await service._group_samples_by_bottle(sample_records)
            
            print(f"✓ 分组完成，生成 {len(bottle_group_map)} 个瓶组:")
            for group_key, group_data in bottle_group_map.items():
                print(f"  - {group_key}: {len(group_data['samples'])} 个样品, 瓶组ID={group_data['bottle_maintenance_id']}")
            
            # 测试获取任务编号
            print("\n测试获取任务编号...")
            task_code = await service._get_task_code(task_id)
            print(f"✓ 任务编号: {task_code}")
            
            return True
            
    except Exception as e:
        print(f"✗ 调试瓶组服务失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bottle_group_generation(task_id: int):
    """测试瓶组生成"""
    print(f"\n测试瓶组生成...")
    
    try:
        async with AsyncSessionLocal() as session:
            service = SamplingBottleGroupService(session)
            
            # 尝试生成瓶组
            result = await service.generate_bottle_groups_for_task(task_id, 1)
            
            print(f"✓ 瓶组生成成功:")
            print(f"  - 总瓶组数: {result.total_groups}")
            print(f"  - 默认瓶组数: {result.default_groups}")
            print(f"  - 匹配瓶组数: {result.matched_groups}")
            
            for i, group in enumerate(result.bottle_groups[:3]):  # 只显示前3个
                print(f"  - 瓶组{i+1}: {group.bottle_group_code} ({group.sample_count}个样品)")
            
            return True
            
    except Exception as e:
        print(f"✗ 瓶组生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主调试函数"""
    print("=" * 60)
    print("瓶组功能调试")
    print("=" * 60)
    
    task_id = 15
    
    # 1. 调试任务数据
    if not await debug_task_data(task_id):
        print("任务数据检查失败，无法继续")
        return
    
    # 2. 调试瓶组服务
    if not await debug_bottle_group_service(task_id):
        print("瓶组服务调试失败，无法继续")
        return
    
    # 3. 测试瓶组生成
    if await test_bottle_group_generation(task_id):
        print("\n✅ 瓶组功能调试成功！")
    else:
        print("\n❌ 瓶组生成仍有问题")


if __name__ == "__main__":
    asyncio.run(main())
