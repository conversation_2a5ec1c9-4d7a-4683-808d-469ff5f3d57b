#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全配置检查脚本

用于检查测试环境跳过认证功能的安全配置，确保生产环境下不会意外启用此功能。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.env import AppConfig, GetConfig
from utils.log_util import logger


class SecurityChecker:
    """安全配置检查器"""
    
    def __init__(self):
        self.issues: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def check_auth_skip_config(self) -> bool:
        """检查认证跳过配置的安全性"""
        logger.info("开始检查认证跳过配置...")
        
        # 检查当前配置值
        skip_auth_enabled = getattr(AppConfig, 'app_skip_auth_in_test', False)
        
        if skip_auth_enabled:
            # 检查当前运行环境
            current_env = getattr(AppConfig, 'app_env', 'unknown')
            
            if current_env.lower() in ['prod', 'production']:
                self.issues.append(
                    f"严重安全问题：生产环境({current_env})启用了认证跳过功能！"
                )
                return False
            elif current_env.lower() in ['dev', 'development', 'test']:
                self.warnings.append(
                    f"警告：{current_env}环境启用了认证跳过功能，请确保这是预期行为。"
                )
            else:
                self.warnings.append(
                    f"警告：未知环境({current_env})启用了认证跳过功能！"
                )
        else:
            self.info.append("认证跳过功能已禁用，配置安全。")
        
        return True
    
    def check_env_files(self) -> bool:
        """检查环境配置文件"""
        logger.info("检查环境配置文件...")
        
        env_files = {
            '.env.prod': '生产环境',
            '.env.dev': '开发环境',
            '.env.test': '测试环境'
        }
        
        for env_file, env_name in env_files.items():
            file_path = project_root / env_file
            if file_path.exists():
                self._check_env_file(file_path, env_name)
            else:
                self.info.append(f"{env_name}配置文件({env_file})不存在。")
        
        return len(self.issues) == 0
    
    def _check_env_file(self, file_path: Path, env_name: str) -> None:
        """检查单个环境配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含跳过认证的配置
            if 'APP_SKIP_AUTH_IN_TEST' in content:
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if 'APP_SKIP_AUTH_IN_TEST' in line and not line.strip().startswith('#'):
                        if '=true' in line.lower() or '=1' in line:
                            if 'prod' in file_path.name.lower():
                                self.issues.append(
                                    f"严重安全问题：{env_name}配置文件第{line_num}行启用了认证跳过功能！"
                                )
                            else:
                                self.warnings.append(
                                    f"警告：{env_name}配置文件第{line_num}行启用了认证跳过功能。"
                                )
                        else:
                            self.info.append(
                                f"{env_name}配置文件中认证跳过功能已禁用。"
                            )
            else:
                self.info.append(f"{env_name}配置文件中未找到认证跳过配置。")
                
        except Exception as e:
            self.warnings.append(f"读取{env_name}配置文件时出错：{str(e)}")
    
    def check_code_security(self) -> bool:
        """检查代码中的安全实现"""
        logger.info("检查代码安全实现...")
        
        # 检查login_service.py中的实现
        login_service_path = project_root / 'module_admin' / 'service' / 'login_service.py'
        
        if login_service_path.exists():
            try:
                with open(login_service_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有适当的安全检查
                if 'app_skip_auth_in_test' in content:
                    if 'test_token' in content:
                        self.info.append("代码中包含测试token检查，实现正确。")
                    else:
                        self.warnings.append("代码中缺少测试token验证。")
                    
                    # 检查是否有日志记录
                    if 'logger.info' in content and '测试环境跳过认证' in content:
                        self.info.append("代码中包含适当的日志记录。")
                    else:
                        self.warnings.append("建议在跳过认证时添加日志记录。")
                else:
                    self.info.append("代码中未找到认证跳过逻辑。")
                    
            except Exception as e:
                self.warnings.append(f"读取login_service.py时出错：{str(e)}")
        else:
            self.warnings.append("未找到login_service.py文件。")
        
        return True
    
    def generate_report(self) -> str:
        """生成安全检查报告"""
        report = []
        report.append("=" * 60)
        report.append("认证跳过功能安全检查报告")
        report.append("=" * 60)
        report.append(f"检查时间：{os.popen('date').read().strip()}")
        report.append(f"当前环境：{getattr(AppConfig, 'app_env', 'unknown')}")
        report.append("")
        
        if self.issues:
            report.append("🚨 严重安全问题：")
            for issue in self.issues:
                report.append(f"  ❌ {issue}")
            report.append("")
        
        if self.warnings:
            report.append("⚠️  警告信息：")
            for warning in self.warnings:
                report.append(f"  ⚠️  {warning}")
            report.append("")
        
        if self.info:
            report.append("ℹ️  信息：")
            for info in self.info:
                report.append(f"  ✅ {info}")
            report.append("")
        
        # 安全建议
        report.append("🔒 安全建议：")
        report.append("  1. 确保生产环境配置文件中 APP_SKIP_AUTH_IN_TEST=false")
        report.append("  2. 定期检查环境配置，防止意外启用")
        report.append("  3. 在CI/CD流程中集成此安全检查")
        report.append("  4. 监控生产环境中test_token的使用情况")
        report.append("  5. 考虑添加IP白名单限制测试功能")
        report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def run_all_checks(self) -> bool:
        """运行所有安全检查"""
        logger.info("开始运行安全检查...")
        
        checks = [
            self.check_auth_skip_config,
            self.check_env_files,
            self.check_code_security
        ]
        
        all_passed = True
        for check in checks:
            try:
                if not check():
                    all_passed = False
            except Exception as e:
                self.issues.append(f"检查过程中出错：{str(e)}")
                all_passed = False
        
        return all_passed and len(self.issues) == 0


def main():
    """主函数"""
    checker = SecurityChecker()
    
    # 运行所有检查
    is_secure = checker.run_all_checks()
    
    # 生成并打印报告
    report = checker.generate_report()
    print(report)
    
    # 保存报告到文件
    report_file = project_root / 'logs' / 'security_check_report.txt'
    report_file.parent.mkdir(exist_ok=True)
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n报告已保存到：{report_file}")
    except Exception as e:
        print(f"\n保存报告时出错：{str(e)}")
    
    # 根据检查结果设置退出码
    if not is_secure:
        print("\n❌ 发现安全问题，请立即处理！")
        sys.exit(1)
    else:
        print("\n✅ 安全检查通过。")
        sys.exit(0)


if __name__ == "__main__":
    main()