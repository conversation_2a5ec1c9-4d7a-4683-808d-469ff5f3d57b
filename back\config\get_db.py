from typing import List, Type

from pydantic import BaseModel
from sqlalchemy import (
    and_,
    asc,
    desc,
    inspect,
    select,
)
from sqlalchemy.dialects.mysql import insert as mysql_insert
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import Session

from config.database import async_engine, AsyncSessionLocal, Base
from exceptions.exception import DbValidatorException
from utils.log_util import logger


async def get_db():
    """
    每一个请求处理完毕后会关闭当前连接，不同的请求使用不同的连接

    :return:
    """
    async with AsyncSessionLocal() as current_db:
        yield current_db


async def init_create_table():
    """
    应用启动时初始化数据库连接

    :return:
    """
    logger.info('初始化数据库连接...')
    async with async_engine.begin() as conn:
        try:
            await conn.run_sync(Base.metadata.create_all)
        except OperationalError as e:
            #  多Worker、容器多副本的情况，新建表会冲突报错
            # (1050, "Table 'xxx' already exists")
            if "1050," in str(e):
                logger.info("ignore duplicate create table error: %s", e)
            else:
                raise e
    logger.info('数据库连接成功')


class ORMClient:
    def __init__(self, model: Type[BaseModel], session: Session):
        self.model = model
        self.session = session

    @property
    def column_keys(self) -> List[str]:
        return list(inspect(self.model).columns.keys())

    @property
    def column_and_relationship_keys(self) -> List[str]:
        """
        返回所有的字段，包括 relationship
        """
        return list(inspect(self.model).all_orm_descriptors.keys())

    def get_one_or_exception(self, **kwargs):
        return self.session.execute(select(self.model).filter_by(**kwargs)).scalar_one()

    def get_one_or_none(self, **kwargs):
        return self.session.execute(
            select(self.model).filter_by(**kwargs)
        ).scalar_one_or_none()

    def list_all(self, **kwargs):
        return (
            self.session.execute(select(self.model).filter_by(**kwargs)).scalars().all()
        )

    def list_all_by_like_search(
            self, order_by: str = "created_at", order: str = "desc", **kwargs
    ):
        exact_params = dict()
        no_exact_params = dict()
        for k, v in kwargs.items():
            if isinstance(v, str) and v.startswith("%") and v.endswith("%"):
                no_exact_params[getattr(self.model, k)] = v
            else:
                exact_params[getattr(self.model, k)] = v
        base_query = self.session.query(self.model).filter(
            *(key == value for key, value in exact_params.items()),
            and_(key.like(value) for key, value in no_exact_params.items()),
        )
        if order == "asc":
            return base_query.order_by(asc(order_by)).all()
        return base_query.order_by(desc(order_by)).all()

    # 由于批量操作数据库的时候要根据用户传入的多个数据来从数据库中捞记录，所以得用 filter 中的 in_ 函数，原来的 list_all 不支持
    def list_all_by_field_alternatives(self, field, values):
        column = getattr(self.model, field)
        return self.session.query(self.model).filter(column.in_(values)).all()

    def create(self, data: dict):
        """
        创建并返回该实例
        """
        clean_kwargs = {
            k: v
            for k, v in list(data.items())
            if k in self.column_and_relationship_keys
        }
        entity = self.model(**clean_kwargs)  # 实例化数据
        self.session.add(entity)  # 将实例存入数据库
        self.commit()  # 最后提交
        return entity

    def batch_create(self, datas: list, allow_duplicate=False):
        entities = []
        for data in datas:
            clean_kwargs = {
                k: v
                for k, v in list(data.items())
                if k in self.column_and_relationship_keys
            }
            entity = self.get_one_or_none(**clean_kwargs)
            if entity:
                if allow_duplicate:
                    entities.append(entity)
                    continue
                raise DbValidatorException(message=f"duplicate key: {clean_kwargs}")
            entity = self.model(**clean_kwargs)
            self.session.add(entity)
            entities.append(entity)
        self.commit()
        return entities

    def update_entity(self, entity: BaseModel, data: dict):
        for k, v in data.items():
            if hasattr(entity, k):
                setattr(entity, k, v)  # 更新属性
        self.session.commit()
        return entity

    def upsert(self, data: dict):
        valid_payload = {k: v for k, v in data.items() if k in self.column_keys}
        statement = (
            mysql_insert(self.model)
            .values(valid_payload)
            .on_duplicate_key_update(valid_payload)
        )
        self.session.execute(statement)
        self.session.commit()
        return self.get_one_or_exception(**valid_payload)

    def delete_by_id(self, pk_id: int):
        entity = self.session.get(self.model, pk_id)
        if entity:
            self.delete_entity(entity)

    def delete_by_unique_filter(self, **kwargs):
        entity = self.get_one_or_none(**kwargs)
        if entity:
            self.delete_entity(entity)

    def delete_entity(self, entity: BaseModel):
        self.session.delete(entity)
        self.commit()

    def delete_by_filter(self, **kwargs):
        entities = self.list_all(**kwargs)
        for entity in entities:
            self.session.delete(entity)
        self.commit()

    def commit(self):
        self.session.commit()

    def rollback(self):
        self.session.rollback()
