-- 添加审批轮次和当前轮次字段到项目报价审批记录表
-- 用于保留审批历史记录

-- 添加审批轮次字段
ALTER TABLE project_quotation_approval_record 
ADD COLUMN approval_round INT NOT NULL DEFAULT 1 COMMENT '审批轮次：1-第一轮，2-第二轮，以此类推';

-- 添加是否为当前轮次字段
ALTER TABLE project_quotation_approval_record 
ADD COLUMN is_current_round VARCHAR(1) NOT NULL DEFAULT '1' COMMENT '是否为当前轮次：0-否，1-是';

-- 更新现有记录，将所有现有记录标记为第一轮且为当前轮次
UPDATE project_quotation_approval_record 
SET approval_round = 1, is_current_round = '1' 
WHERE approval_round IS NULL OR is_current_round IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_project_quotation_approval_record_round 
ON project_quotation_approval_record (project_quotation_id, approval_round, is_current_round);

-- 添加复合索引以优化常用查询
CREATE INDEX idx_project_quotation_approval_record_current 
ON project_quotation_approval_record (project_quotation_id, is_current_round, approval_stage);