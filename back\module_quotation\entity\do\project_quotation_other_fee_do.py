"""
项目报价其他费用数据模型
"""
from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationOtherFee(Base):
    """
    项目报价其他费用表
    """
    __tablename__ = 'project_quotation_other_fee'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    fee_name = Column(String(50), nullable=False, comment='费用名称')
    quantity = Column(Integer, nullable=False, default=1, comment='数量')
    unit_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment='单价')
    total_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment='总价')
    remark = Column(Text, nullable=True, comment='备注')
    
    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment='创建人')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(50), nullable=True, comment='更新人')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    
    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="other_fees")
