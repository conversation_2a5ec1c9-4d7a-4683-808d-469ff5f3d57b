<template>
  <el-dialog
    title="批量导入_瓶组维护"
    v-model="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="import-content">
      <!-- 导入说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template #default>
          <div>
            <p>1. 请先下载导入模板，按照模板格式填写数据</p>
            <p>2. 支持的字段：容器类型、容器容量、存储方式、固定方式、样品时效、样品时效单位、备注、技术手册参数、技术手册方法、技术手册类别</p>
            <p>3. 存储方式、固定方式、技术手册相关字段支持多个值，请用逗号分隔</p>
            <p>4. 容器类型和容器容量为必填字段</p>
          </div>
        </template>
      </el-alert>

      <!-- 文件上传 -->
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :limit="1"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        accept=".xlsx,.xls"
      >
        <div class="el-icon--upload">📁</div>
        <div class="el-upload__text">
          将Excel文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传xlsx/xls文件，且不超过10MB
          </div>
        </template>
      </el-upload>

      <!-- 上传的文件信息 -->
      <div v-if="uploadFile" class="file-info">
        <el-tag type="success" style="margin-top: 10px">
          📄 {{ uploadFile.name }}
        </el-tag>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <el-divider content-position="left">导入结果</el-divider>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="成功导入">
            <el-tag type="success">{{ importResult.successCount }} 条</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="导入失败">
            <el-tag type="danger">{{ importResult.errorCount }} 条</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 错误信息 -->
        <div v-if="importResult.errors && importResult.errors.length > 0" style="margin-top: 20px">
          <h4>错误详情：</h4>
          <el-table :data="importResult.errors" border size="small" max-height="200">
            <el-table-column prop="row" label="行号" width="80" />
            <el-table-column prop="field" label="字段" width="120" />
            <el-table-column prop="message" label="错误信息" />
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleDownloadTemplate">
          📥 下载模板
        </el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleImport" 
          :loading="importing"
          :disabled="!uploadFile"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { batchImportBottleMaintenance, downloadImportTemplate } from '@/api/basedata/bottleMaintenance'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const uploadRef = ref()
const uploadFile = ref(null)
const importing = ref(false)
const importResult = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 方法
const handleFileChange = (file) => {
  uploadFile.value = file.raw
  importResult.value = null
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleDownloadTemplate = async () => {
  try {
    const response = await downloadImportTemplate()
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '瓶组管理导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
  }
}

const handleImport = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入选中的文件吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    importing.value = true
    
    const response = await batchImportBottleMaintenance(uploadFile.value)
    importResult.value = response.data
    
    if (response.data.errorCount === 0) {
      ElMessage.success(`导入成功！共导入 ${response.data.successCount} 条记录`)
      emit('success')
    } else {
      ElMessage.warning(`导入完成！成功 ${response.data.successCount} 条，失败 ${response.data.errorCount} 条`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('导入失败')
    }
  } finally {
    importing.value = false
  }
}

const handleClose = () => {
  // 重置状态
  uploadFile.value = null
  importResult.value = null
  uploadRef.value?.clearFiles()
  
  emit('update:visible', false)
}
</script>

<style scoped>
.import-content {
  padding: 0 20px;
}

.upload-demo {
  margin: 20px 0;
}

.file-info {
  text-align: center;
}

.import-result {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-alert__content) {
  text-align: left;
}

:deep(.el-alert__content p) {
  margin: 5px 0;
}
</style>
