<template>
  <div class="h5-equipment-detail">
    <!-- 头部 -->
    <div class="header">
      <h2>设备详情</h2>
      <div class="equipment-number">设备编号：{{ equipmentData.equipmentNumber }}</div>
      <div class="equipment-number">
          <label>设备状态：</label>
          <span class="status" :class="getStatusClass(equipmentData.equipmentStatus)">
            {{ equipmentData.equipmentStatus || '-' }}
          </span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
    </div>

    <!-- 设备详情内容 -->
    <div v-else-if="equipmentData.id" class="content">
      <!-- 基础信息 -->
      <div class="section">
        <h3 class="section-title">基础信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>设备编号：</label>
            <span>{{ equipmentData.equipmentNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <label>设备名称：</label>
            <span>{{ equipmentData.equipmentName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>启用日期：</label>
            <span>{{ formatDate(equipmentData.enableDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>设备类型：</label>
            <span>{{ equipmentData.equipmentType || '-' }}</span>
          </div>
          <div class="info-item">
            <label>型号规格：</label>
            <span>{{ equipmentData.modelSpecification || '-' }}</span>
          </div>
          <div class="info-item">
            <label>出厂编号：</label>
            <span>{{ equipmentData.factoryNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <label>制造商：</label>
            <span>{{ equipmentData.manufacturer || '-' }}</span>
          </div>
          <div class="info-item">
            <label>技术指标特性：</label>
            <span>{{ equipmentData.indicatorCharacteristics || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 管理责任 -->
      <div class="section">
        <h3 class="section-title">管理责任</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>管理员：</label>
            <span>{{ equipmentData.manager || '-' }}</span>
          </div>
          <div class="info-item">
            <label>设备状态：</label>
            <span class="status" :class="getStatusClass(equipmentData.equipmentStatus)">
              {{ equipmentData.equipmentStatus || '-' }}
            </span>
          </div>
          <el-descriptions-item label="设备状态说明">
            {{ equipmentData.equipmentStatusDescription }}
          </el-descriptions-item>
          <div class="info-item">
            <label>存放地点：</label>
            <span>{{ equipmentData.location || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 量值溯源管理类 -->
      <div class="section">
        <h3 class="section-title">量值溯源管理类</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>检定/校准/核查机构：</label>
            <span>{{ equipmentData.calibrationInstitution || '-' }}</span>
          </div>
          <div class="info-item">
            <label>溯源方式：</label>
            <span>{{ equipmentData.traceabilityMethod || '-' }}</span>
          </div>
          <div class="info-item">
            <label>本次检定/校准/核查日期：</label>
            <span>{{ formatDate(equipmentData.currentCalibrationDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>证书编号：</label>
            <span>{{ equipmentData.certificateNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <label>下次检定/校准/核查日期：</label>
            <span>{{ formatDate(equipmentData.nextCalibrationDate) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>间隔日期：</label>
            <span>{{ equipmentData.intervalDays || '-' }}天</span>
          </div>
          <div class="info-item">
            <label>期间核查日期：</label>
            <span>{{ formatDate(equipmentData.interimCheckDate) || '-' }}</span>
          </div>
          <div class="info-item full-width">
            <label>检定/校准/核查内容：</label>
            <span>{{ equipmentData.calibrationContent || '-' }}</span>
          </div>
          <div class="info-item full-width">
            <label>备注(溯源结果确认)：</label>
            <span>{{ equipmentData.calibrationRemark || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部信息 -->
    <div class="footer">
      <p>扫码时间：{{ new Date().toLocaleString() }}</p>
      <p>设备管理系统</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Loading, Warning, Document } from '@element-plus/icons-vue'
import { getEquipmentManagement } from '@/api/basedata/equipmentManagement'

const route = useRoute()

// 响应式数据
const loading = ref(true)
const error = ref('')
const equipmentData = ref({})

// 获取设备详情
const fetchEquipmentDetail = async () => {
  try {
    loading.value = true
    error.value = ''

    const equipmentId = route.query.id
    if (!equipmentId) {
      throw new Error('设备ID参数缺失')
    }

    const response = await getEquipmentManagement(equipmentId)
    if (response.code === 200) {
      equipmentData.value = response.data
    } else {
      throw new Error(response.msg || '获取设备详情失败')
    }
  } catch (err) {
    // 如果是认证错误，不显示错误信息，让路由守卫处理重定向
    if (err.message && err.message.includes('401')) {
      console.log('认证失败，等待重定向到登录页')
      return
    }
    error.value = err.message || '获取设备详情失败'
    console.error('获取设备详情失败:', err)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return ''
  return `¥${Number(amount).toLocaleString()}`
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case '正常':
      return 'status-normal'
    case '维修':
      return 'status-repair'
    case '停用':
      return 'status-disabled'
    default:
      return ''
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchEquipmentDetail()
})
</script>

<style scoped>
.h5-equipment-detail {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 20px 15px;
  text-align: center;
}

.header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 500;
}

.equipment-number {
  font-size: 14px;
  opacity: 0.9;
}

.loading, .error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.error .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #f56c6c;
}

.content {
  padding: 0 15px 20px;
}

.section {
  background: white;
  margin: 15px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.info-grid {
  padding: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item.full-width {
  flex-direction: column;
}

.info-item label {
  color: #666;
  font-size: 14px;
  min-width: 100px;
  margin-right: 8px;
}

.info-item.full-width label {
  margin-bottom: 4px;
}

.info-item span {
  color: #333;
  font-size: 14px;
  word-break: break-all;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-normal {
  background: #f0f9ff;
  color: #1890ff;
}

.status-repair {
  background: #fff7e6;
  color: #fa8c16;
}

.status-disabled {
  background: #fff1f0;
  color: #f5222d;
}

.attachments {
  padding: 15px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-item .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.attachment-item span {
  flex: 1;
  font-size: 14px;
}

.file-size {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
  background: white;
  margin-top: 20px;
}

.footer p {
  margin: 4px 0;
}
</style>
