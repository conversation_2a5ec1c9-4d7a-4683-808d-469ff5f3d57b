"""
样品记录服务层
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy import select, and_, func
from datetime import datetime

from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.detection_cycle_item_do import Detection<PERSON>ycleItem
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_customer.entity.do.customer_do import Customer
from module_admin.entity.do.user_do import SysUser

from module_sampling.dao.sample_record_dao import SampleRecordDAO
from module_sampling.dto.sample_record_dto import (
    SampleRecordCreateDTO, SampleRecordUpdateDTO, <PERSON>pleRecordDTO,
    SampleRecordQueryDTO, <PERSON>pleRecordBatchCreateDTO, SampleRecordStatisticsDTO
)
from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from exceptions.exception import ServiceException
from utils.log_util import logger


class SampleRecordService:
    """样品记录服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.sample_record_dao = SampleRecordDAO(db)
    
    async def create_sample_record(self, create_dto: SampleRecordCreateDTO, create_by: int) -> SampleRecordDTO:
        """创建样品记录"""
        try:
            # 获取下一个样品序号
            max_number = await self.sample_record_dao.get_max_sample_number_by_assignment(
                create_dto.sampling_task_assignment_id
            )
            next_number = max_number + 1
            
            sample_record = SampleRecord(
                sampling_task_assignment_id=create_dto.sampling_task_assignment_id,
                detection_cycle_item_id=create_dto.detection_cycle_item_id,
                project_quotation_item_id=create_dto.project_quotation_item_id,
                sample_number=next_number,
                sample_type=create_dto.sample_type,
                sample_source=create_dto.sample_source,
                point_name=create_dto.point_name,
                cycle_number=create_dto.cycle_number,
                cycle_type=create_dto.cycle_type,
                detection_category=create_dto.detection_category,
                detection_parameter=create_dto.detection_parameter,
                detection_method=create_dto.detection_method,
                remark=create_dto.remark,
                create_by=create_by,
                update_by=create_by
            )
            
            created_record = await self.sample_record_dao.create_sample_record(sample_record)
            await self.db.commit()
            
            return await self._convert_to_dto(created_record)
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"创建样品记录失败: {str(e)}")
    


    async def generate_sample_records_for_group(self, group_id: int, create_by: int) -> List[SampleRecordDTO]:
        """为任务分组生成样品记录"""
        try:
            # 获取任务分组信息
            group = await self._get_group_with_details(group_id)
            if not group:
                raise ServiceException(message="任务分组不存在")

            # 检查是否已经存在样品记录
            existing_records = await self.sample_record_dao.get_sample_records_by_group_id(group_id)
            if existing_records:
                # 如果已经存在样品记录，直接返回现有记录
                result_dtos = []
                for record in existing_records:
                    dto = await self._convert_to_dto(record)
                    result_dtos.append(dto)
                return result_dtos

            # 获取关联的周期条目
            cycle_items = await self._get_cycle_items_for_group(group)
            if not cycle_items:
                raise ServiceException(message="未找到关联的周期条目")

            # 计算需要生成的样品记录数
            # 分组系统中，每个分组对应一个特定的周期、类别、点位组合
            # 计算最大样品数量：所有周期条目中 频次 × 样品数 的最大值
            max_sample_count = 0
            for cycle_item in cycle_items:
                quotation_item = cycle_item.project_quotation_item
                if quotation_item:
                    # 跳过现场直读类型
                    if await self._is_on_site_check_type(quotation_item):
                        continue

                    # 计算该条目的样品数量
                    frequency = quotation_item.frequency or 1
                    sample_count = quotation_item.sample_count or 1
                    total_count = frequency * sample_count
                    max_sample_count = max(max_sample_count, total_count)

            if max_sample_count == 0:
                raise ServiceException(message="无需生成样品记录")

            # 选择第一个非现场直读的周期条目作为代表性条目
            representative_item = None
            for cycle_item in cycle_items:
                quotation_item = cycle_item.project_quotation_item
                if quotation_item and not await self._is_on_site_check_type(quotation_item):
                    representative_item = cycle_item
                    break

            if not representative_item:
                raise ServiceException(message="未找到有效的周期条目")

            # 生成样品记录
            sample_records = []
            for i in range(max_sample_count):
                sample_number = i + 1

                # 生成一条样品记录
                sample_record = SampleRecord(
                    sampling_task_group_id=group_id,  # 使用分组ID
                    sample_number=sample_number,
                    sample_type=representative_item.project_quotation_item.category,  # 样品类型对应检测类别
                    sample_source=representative_item.project_quotation_item.sample_source,  # 样品来源
                    point_name=representative_item.project_quotation_item.point_name,
                    cycle_number=representative_item.cycle_number,
                    cycle_type=representative_item.project_quotation_item.cycle_type,
                    detection_category=representative_item.project_quotation_item.category,
                    detection_parameter=self._combine_parameters(cycle_items),
                    detection_method=self._combine_methods(cycle_items),
                    create_by=create_by,
                    update_by=create_by
                )
                sample_records.append(sample_record)

            # 批量创建样品记录
            created_records = await self.sample_record_dao.batch_create_sample_records(sample_records)
            await self.db.commit()

            # 自动生成瓶组记录
            try:
                bottle_group_service = SamplingBottleGroupService(self.db)
                # 获取任务ID（从分组信息中获取）
                task_id = group.sampling_task_id
                bottle_result = await bottle_group_service.generate_bottle_groups_for_task(task_id, create_by)
                logger.info(f"为任务 {task_id} 自动生成了 {bottle_result.total_groups} 个瓶组")
            except Exception as e:
                # 瓶组生成失败不影响样品记录的创建，只记录日志
                logger.warning(f"自动生成瓶组失败: {str(e)}")

            # 转换为DTO
            result_dtos = []
            for record in created_records:
                dto = await self._convert_to_dto(record)
                result_dtos.append(dto)

            return result_dtos

        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"生成样品记录失败: {str(e)}")
    
    async def _calculate_max_sample_count(self, cycle_items: List[DetectionCycleItem]) -> int:
        """计算最大样品数量"""
        max_count = 0
        
        for cycle_item in cycle_items:
            quotation_item = cycle_item.project_quotation_item
            
            # 检查是否为现场直读类型，如果是则跳过
            if await self._is_on_site_check_item(quotation_item):
                continue
            
            # 计算该条目的样品数：频次 * 样品数
            frequency = quotation_item.frequency or 1
            sample_count = quotation_item.sample_count or 1
            item_sample_count = frequency * sample_count
            
            max_count = max(max_count, item_sample_count)
        
        return max_count
    
    async def _is_on_site_check_item(self, quotation_item: ProjectQuotationItem) -> bool:
        """检查项目报价明细是否为现场直读类型"""
        try:
            # 通过资质唯一编号查询技术手册
            if quotation_item.qualification_code:
                stmt = select(TechnicalManual.analysis_type).where(
                    TechnicalManual.qualification_code == quotation_item.qualification_code
                )
                result = await self.db.execute(stmt)
                analysis_type = result.scalar_one_or_none()
                return analysis_type == "ON_SITE_CHECK"
            
            # 如果没有资质编号，通过参数和方法查询
            stmt = select(TechnicalManual.analysis_type).where(
                and_(
                    TechnicalManual.parameter == quotation_item.parameter,
                    TechnicalManual.method == quotation_item.method
                )
            )
            result = await self.db.execute(stmt)
            analysis_type = result.scalar_one_or_none()
            return analysis_type == "ON_SITE_CHECK"
            
        except Exception:
            return False
    




    def _combine_parameters(self, cycle_items: List) -> str:
        """合并多个周期条目的检测参数"""
        parameters = []
        for item in cycle_items:
            param = item.project_quotation_item.parameter
            if param and param not in parameters:
                parameters.append(param)
        return ", ".join(parameters)

    def _combine_methods(self, cycle_items: List) -> str:
        """合并多个周期条目的检测方法"""
        methods = []
        for item in cycle_items:
            method = item.project_quotation_item.method
            if method and method not in methods:
                methods.append(method)
        return ", ".join(methods)

    async def _is_on_site_check_type(self, quotation_item) -> bool:
        """检查是否为现场直读类型"""
        # 这里需要根据实际业务逻辑判断
        # 可能需要查询技术手册的analysis_type字段
        # 暂时返回False，表示不跳过任何项目
        return False


    
    async def update_sample_record_status(self, record_id: int, status: int, update_by: int) -> bool:
        """更新样品记录状态"""
        try:
            result = await self.sample_record_dao.update_sample_record_status(record_id, status, update_by)
            if result:
                await self.db.commit()
            return result
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"更新样品记录状态失败: {str(e)}")
    
    async def _convert_to_dto(self, sample_record: SampleRecord) -> SampleRecordDTO:
        """转换为DTO"""
        # 只使用基础属性，避免任何可能的懒加载
        dto_data = {
            'id': sample_record.id,
            'sampling_task_group_id': sample_record.sampling_task_group_id,
            'sample_number': sample_record.sample_number,
            'sample_type': sample_record.sample_type,
            'sample_source': sample_record.sample_source,
            'point_name': sample_record.point_name,
            'cycle_number': sample_record.cycle_number,
            'cycle_type': sample_record.cycle_type,
            'detection_category': sample_record.detection_category,
            'detection_parameter': sample_record.detection_parameter,
            'detection_method': sample_record.detection_method,
            'status': sample_record.status,
            'collection_time': sample_record.collection_time,
            'submission_time': sample_record.submission_time,
            'completion_time': sample_record.completion_time,
            'remark': sample_record.remark,
            'create_by': sample_record.create_by,
            'create_time': sample_record.create_time,
            'update_by': sample_record.update_by,
            'update_time': sample_record.update_time
        }

        return SampleRecordDTO(**dto_data)

    async def _get_group_with_details(self, group_id: int):
        """获取任务分组详情"""
        from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
        from sqlalchemy import select

        stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.id == group_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_cycle_items_for_group(self, group):
        """获取分组关联的周期条目"""
        import json
        from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
        from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload

        # 解析周期条目ID列表
        cycle_item_ids = []
        if group.cycle_item_ids:
            try:
                cycle_item_ids = json.loads(group.cycle_item_ids)
            except:
                cycle_item_ids = []

        if not cycle_item_ids:
            return []

        # 查询周期条目及其关联的项目报价明细
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation_item)
        ).where(DetectionCycleItem.id.in_(cycle_item_ids))

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_sample_records_by_group(self, group_id: int) -> List[SampleRecordDTO]:
        """获取任务分组的样品记录列表"""
        try:
            records = await self.sample_record_dao.get_sample_records_by_group_id(group_id)

            result_dtos = []
            for record in records:
                dto = await self._convert_to_dto(record)
                result_dtos.append(dto)

            return result_dtos

        except Exception as e:
            raise ServiceException(message=f"获取样品记录列表失败: {str(e)}")
