#!/usr/bin/env python3
"""
创建正确的照片文件
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from sqlalchemy import text


async def create_correct_photos():
    async for db in get_db():
        try:
            # 查询数据库中的照片文件名
            query = text('''
                SELECT point_photo_name, east_photo_name, south_photo_name, 
                       west_photo_name, north_photo_name
                FROM sampling_point_info 
                WHERE sampling_task_group_id IN (35, 36)
            ''')
            result = await db.execute(query)
            photos = result.fetchall()
            
            print(f'找到 {len(photos)} 条点位信息记录')
            
            # 创建上传目录
            upload_dir = Path('vf_admin/upload_path/upload/2024/12/29')
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            # JPEG文件的二进制数据
            jpeg_data = bytes([
                0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
                0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
                0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
                0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
                0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
                0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
                0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
                0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
                0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
                0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
                0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
                0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
            ])
            
            # 为每个记录创建对应的照片文件
            created_count = 0
            for photo_record in photos:
                for photo_name in photo_record:
                    if photo_name:
                        filepath = upload_dir / photo_name
                        if not filepath.exists():
                            with open(filepath, 'wb') as f:
                                f.write(jpeg_data)
                            print(f'创建照片文件: {photo_name}')
                            created_count += 1
                        else:
                            print(f'照片文件已存在: {photo_name}')
            
            print(f'✅ 创建了 {created_count} 个照片文件！')
            
        except Exception as e:
            print(f'❌ 创建失败: {e}')
            import traceback
            traceback.print_exc()
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(create_correct_photos())
