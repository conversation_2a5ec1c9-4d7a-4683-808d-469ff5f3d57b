-- 创建部门负责人关联表
CREATE TABLE IF NOT EXISTS sys_dept_leader (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    dept_id BIGINT(20) NOT NULL COMMENT '部门ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_dept_id (dept_id),
    INDEX idx_user_id (user_id),
    UNIQUE KEY uk_dept_user (dept_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门负责人关联表';

-- 迁移现有的部门负责人数据
INSERT INTO sys_dept_leader (dept_id, user_id, create_by, create_time, update_by, update_time)
SELECT 
    dept_id,
    leader,
    COALESCE(create_by, 'system'),
    COALESCE(create_time, NOW()),
    COALESCE(update_by, 'system'),
    COALESCE(update_time, NOW())
FROM sys_dept 
WHERE leader IS NOT NULL AND leader > 0 AND del_flag = '0';

-- 注意：暂时保留原有的leader字段，待确认迁移成功后再删除
-- ALTER TABLE sys_dept DROP COLUMN leader;