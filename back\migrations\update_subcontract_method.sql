-- 更新项目报价明细表的分包字段
-- 将 is_subcontract 字段改为 subcontract_method 字段

-- 1. 添加新的分包方式字段
ALTER TABLE project_quotation_item 
ADD COLUMN subcontract_method varchar(20) DEFAULT '不分包' COMMENT '分包方式(分包、不分包、分包检测)';

-- 2. 迁移现有数据
UPDATE project_quotation_item
SET subcontract_method = CASE
    WHEN is_subcontract = '1' THEN '分包'
    WHEN is_subcontract = '2' THEN '分包检测'
    ELSE '不分包'
END
WHERE is_subcontract IS NOT NULL;

-- 3. 删除旧字段（可选，建议先备份数据）
-- ALTER TABLE project_quotation_item DROP COLUMN is_subcontract;

-- 注意：如果需要保留旧数据，可以先不删除 is_subcontract 字段
-- 等确认新字段工作正常后再删除
