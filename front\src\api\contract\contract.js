import request from '@/utils/request'

// 获取合同列表
export function listContract(query) {
  return request({
    url: '/contract/list',
    method: 'get',
    params: query
  })
}

// 获取我的合同列表
export function myContract(query) {
  return request({
    url: '/contract/my',
    method: 'get',
    params: query
  })
}

// 获取合同详细信息
export function getContract(id) {
  return request({
    url: `/contract/${id}`,
    method: 'get'
  })
}

// 新增合同
export function addContract(data) {
  return request({
    url: '/contract/add',
    method: 'post',
    data: data
  })
}

// 修改合同
export function updateContract(data) {
  return request({
    url: '/contract/update',
    method: 'put',
    data: data
  })
}

// 删除合同
export function delContract(id) {
  return request({
    url: `/contract/${id}`,
    method: 'delete'
  })
}

// 批量删除合同
export function batchDelContract(ids) {
  return request({
    url: '/contract/batch',
    method: 'delete',
    data: ids
  })
}

// 获取用户列表（用于项目负责人和客服选择）
export function getUserList(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: { status: 0, pageSize: 100000, ...query }
  })
}

// 获取合同商务信息
export function getContractBusiness(contractId) {
  return request({
    url: `/contract/contract_business/${contractId}`,
    method: 'get'
  })
}

// 保存合同商务信息
export function saveContractBusiness(data) {
  return request({
    url: '/contract/contract_business/save',
    method: 'post',
    data: data
  })
}

// 计算并更新合同的报价单总金额
export function calculateQuotationTotalAmount(contractId) {
  return request({
    url: `/contract/${contractId}/calculate-quotation-total`,
    method: 'post'
  })
}
