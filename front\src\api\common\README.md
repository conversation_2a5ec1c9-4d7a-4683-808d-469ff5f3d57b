# 通用API函数

本目录包含项目中可复用的通用API函数。

## 审核员API (approver.js)

### 功能说明

提供获取审核员列表的通用函数，避免在多个组件中重复编写相同的逻辑。

### 主要函数

#### getApproverOptions(roleName)

获取格式化的审核员选项列表，适用于下拉选择框等UI组件。

**参数：**
- `roleName` (string, 可选): 角色名称，默认为'审核员'

**返回值：**
- Promise<Array>: 格式化的选项列表，每个选项包含 `value` 和 `label` 字段

**使用示例：**
```javascript
import { getApproverOptions } from '@/api/common/userRole'

// 获取审核员选项
getApproverOptions('审核员').then(options => {
  approverOptions.value = options
}).catch(error => {
  console.error('获取审核人列表失败:', error)
})
```

#### getUsersByRole(roleName)

获取指定角色的用户列表原始数据。

**参数：**
- `roleName` (string): 角色名称

**返回值：**
- Promise: 用户列表原始数据

#### getAllUsers(query)

获取所有用户列表。

**参数：**
- `query` (Object, 可选): 查询参数，默认为空对象

**返回值：**
- Promise: 用户列表数据

### 错误处理

`getApproverOptions` 函数内置了错误处理机制：
1. 首先尝试根据角色名称获取用户列表
2. 如果失败，则获取所有用户作为备选
3. 如果仍然失败，则抛出错误

### 已优化的组件

以下组件已经使用了新的通用API函数：
- `AddProjectQuotationDialog.vue`
- `EditProjectInfoDialog.vue`
- `EditQuotationDialog.vue` (清理了不必要的导入)

### 迁移指南

如果你的组件中有类似的获取审核员逻辑，可以按以下步骤迁移：

1. 导入通用函数：
```javascript
import { getApproverOptions } from '@/api/common/userRole'
```

2. 替换原有的获取逻辑：
```javascript
// 原有代码
function getApproverOptions() {
  getUsersByRoleName('审核员').then(response => {
    approverOptions.value = response.data.map(user => ({
      value: user.userId,
      label: `${user.userName}(${user.nickName || '无昵称'})`
    }))
  }).catch(error => {
    // 错误处理...
  })
}

// 新代码
function loadApproverOptions() {
  getApproverOptions('审核员').then(options => {
    approverOptions.value = options
  }).catch(error => {
    console.error('获取审核人列表失败:', error)
  })
}
```

3. 移除不必要的导入：
```javascript
// 移除这些导入
// import { getUsersByRoleName } from '@/api/system/role'
// import { listUser } from '@/api/system/user'
```