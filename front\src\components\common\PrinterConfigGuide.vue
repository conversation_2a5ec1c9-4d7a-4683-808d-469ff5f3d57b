<template>
  <el-dialog
    v-model="visible"
    title="打印机配置指南"
    width="600px"
    :before-close="handleClose"
  >
    <div class="printer-config-guide">
      <div class="config-section">
        <h3>📄 纸张设置</h3>
        <div class="config-item">
          <div class="config-label">纸张尺寸：</div>
          <div class="config-value">75mm × 60mm</div>
        </div>
        <div class="config-item">
          <div class="config-label">纸张类型：</div>
          <div class="config-value">标签纸 / 不干胶纸</div>
        </div>
        <div class="config-item">
          <div class="config-label">纸张方向：</div>
          <div class="config-value">纵向 (Portrait)</div>
        </div>
      </div>

      <div class="config-section">
        <h3>📐 边距设置</h3>
        <div class="config-item">
          <div class="config-label">上边距：</div>
          <div class="config-value">0mm</div>
        </div>
        <div class="config-item">
          <div class="config-label">下边距：</div>
          <div class="config-value">0mm</div>
        </div>
        <div class="config-item">
          <div class="config-label">左边距：</div>
          <div class="config-value">0mm</div>
        </div>
        <div class="config-item">
          <div class="config-label">右边距：</div>
          <div class="config-value">0mm</div>
        </div>
      </div>

      <div class="config-section">
        <h3>🖨️ 打印设置</h3>
        <div class="config-item">
          <div class="config-label">缩放比例：</div>
          <div class="config-value">100% (无缩放)</div>
        </div>
        <div class="config-item">
          <div class="config-label">背景图形：</div>
          <div class="config-value">启用 (打印背景色和图像)</div>
        </div>
        <div class="config-item">
          <div class="config-label">页眉页脚：</div>
          <div class="config-value">禁用</div>
        </div>
      </div>

      <div class="config-section">
        <h3>🔧 常见打印机设置步骤</h3>
        <div class="steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">Chrome浏览器设置</div>
              <div class="step-desc">
                <div class="sub-steps">
                  <div class="sub-step">① 按 Ctrl+P 打开打印对话框</div>
                  <div class="sub-step">② 点击左侧"更多设置"展开选项</div>
                  <div class="sub-step">③ 在"纸张尺寸"中选择"更多尺寸..."</div>
                  <div class="sub-step">④ 输入宽度75mm，高度60mm</div>
                  <div class="sub-step">⑤ 边距选择"无"，缩放设为100%</div>
                  <div class="sub-step">⑥ 勾选"背景图形"，取消"页眉和页脚"</div>
                </div>
              </div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">标签打印机</div>
              <div class="step-desc">在打印机驱动中设置纸张为75mm×60mm标签纸</div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">热敏打印机</div>
              <div class="step-desc">设置标签尺寸为75×60mm，关闭自动切纸功能</div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h3>🌐 Chrome浏览器详细设置</h3>
        <div class="chrome-settings">
          <div class="setting-group">
            <div class="setting-title">📄 纸张设置</div>
            <div class="setting-items">
              <div class="setting-item">纸张尺寸：更多尺寸... → 自定义 → 75mm × 60mm</div>
              <div class="setting-item">方向：纵向 (Portrait)</div>
            </div>
          </div>
          <div class="setting-group">
            <div class="setting-title">📐 布局设置</div>
            <div class="setting-items">
              <div class="setting-item">边距：无 (None) 或 最小 (Minimum)</div>
              <div class="setting-item">缩放：100% (不缩放)</div>
            </div>
          </div>
          <div class="setting-group">
            <div class="setting-title">🎨 选项设置</div>
            <div class="setting-items">
              <div class="setting-item">✅ 背景图形 (Background graphics)</div>
              <div class="setting-item">❌ 页眉和页脚 (Headers and footers)</div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h3>⚠️ 注意事项</h3>
        <ul class="notice-list">
          <li>确保打印机已正确安装驱动程序</li>
          <li>使用75mm×60mm规格的标签纸</li>
          <li>Chrome浏览器版本需要88+以上支持自定义纸张</li>
          <li>如果找不到"更多尺寸"选项，请更新Chrome浏览器</li>
          <li>打印前请先测试打印一张确认效果</li>
          <li>如果打印内容被截断，请检查纸张设置和边距</li>
          <li>建议使用专业的标签打印机获得最佳效果</li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">我知道了</el-button>
        <el-button type="primary" @click="handlePrintTest">测试打印</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'test-print'])

// 响应式数据
const visible = ref(false)

// 方法
const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
}

const handlePrintTest = () => {
  emit('test-print')
  ElMessage.success('测试打印已发送，请检查打印效果')
  handleClose()
}

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.printer-config-guide {
  max-height: 60vh;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.config-section:last-child {
  border-bottom: none;
}

.config-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-label {
  min-width: 100px;
  color: #666;
  font-weight: 500;
}

.config-value {
  color: #333;
  font-weight: 600;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.sub-steps {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sub-step {
  font-size: 12px;
  color: #666;
  padding-left: 8px;
  position: relative;
}

.sub-step::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8px;
  width: 3px;
  height: 3px;
  background: #409eff;
  border-radius: 50%;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.notice-list li {
  margin-bottom: 8px;
}

.notice-list li:last-child {
  margin-bottom: 0;
}

.chrome-settings {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.setting-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-item {
  font-size: 13px;
  color: #666;
  padding-left: 12px;
  position: relative;
}

.setting-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #409eff;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
