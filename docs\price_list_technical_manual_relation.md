# 价目表与技术手册关联关系说明

## 概述

为了优化系统结构，减少数据冗余，我们对价目表模块进行了重构，将价目表与技术手册通过检测编号（test_code）字段进行关联。这样做的好处是：

1. 避免数据冗余：不再在价目表中直接存储检测类别、检测参数、检测方法等信息
2. 保持数据一致性：当技术手册中的信息更新时，价目表中的相关信息也会自动更新
3. 简化维护：只需在一处维护检测类别、检测参数、检测方法等信息

## 数据结构变更

### 技术手册表（technical_manual）

添加了`test_code`字段，用于唯一标识一条技术手册记录：

```sql
test_code VARCHAR(20) COMMENT '检测编号'
```

检测编号格式为：JC + 6位数字，例如：JC000001

### 价目表（price_list）

1. 添加了`test_code`字段，用于关联技术手册表：

```sql
test_code VARCHAR(20) NOT NULL COMMENT '检测编号'
```

2. 保留了原有的`category`、`parameter`、`method`字段，但这些字段不再直接存储数据，而是在查询时从技术手册表中获取

## 代码变更

### 后端变更

1. 技术手册服务（TechnicalManualService）
   - 添加了`generate_test_code`方法，用于生成唯一的检测编号
   - 添加了`get_test_codes`方法，用于获取所有检测编号
   - 添加了`get_technical_manual_by_test_code`方法，用于根据检测编号获取技术手册详情

2. 价目表服务（PriceListService）
   - 修改了查询方法，支持通过`test_code`字段查询
   - 修改了添加和编辑方法，使用`test_code`字段关联技术手册
   - 修改了导出方法，从技术手册表中获取检测类别、检测参数、检测方法等信息

### 前端变更

1. 价目表列表页面
   - 添加了检测编号列，显示关联的技术手册编号
   - 保留了检测类别、检测参数、检测方法列，但这些数据是从技术手册表中获取的

2. 价目表表单
   - 添加了检测编号选择框，用于选择关联的技术手册
   - 将检测类别、检测参数、检测方法字段设置为只读，根据选择的检测编号自动填充

## 数据迁移

为了确保现有数据的正确迁移，我们提供了一个迁移脚本`price_list_test_code_migration.py`，该脚本会：

1. 为技术手册表中没有`test_code`的记录生成检测编号
2. 更新价目表中的`test_code`字段，关联到对应的技术手册记录

## 使用说明

### 添加价目表

1. 点击"新增"按钮
2. 在表单中选择一个检测编号（从技术手册中获取）
3. 检测类别、检测参数、检测方法字段会自动填充
4. 填写其他必要信息（采样单价、检测单价等）
5. 点击"确定"按钮保存

### 查询价目表

可以通过以下条件查询价目表：

1. 检测编号：直接匹配价目表中的`test_code`字段
2. 检测类别、检测参数、检测方法：通过关联技术手册表进行查询
3. 关键词：匹配检测编号或报价编号

## 注意事项

1. 在添加技术手册时，系统会自动生成检测编号
2. 价目表必须关联到一个有效的技术手册记录
3. 同一个检测编号在同一个生效日期只能有一条价目表记录
