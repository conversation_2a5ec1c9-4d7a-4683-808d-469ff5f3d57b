-- ----------------------------
-- 客户跟进记录表结构更新迁移脚本
-- 删除 create_by, update_by 字段，新增 update_user_id 字段
-- ----------------------------

-- 1. 添加新的 update_user_id 字段
ALTER TABLE customer_follow_record 
ADD COLUMN update_user_id BIGINT(20) DEFAULT NULL COMMENT '更新人用户ID' 
AFTER create_user_id;

-- 2. 添加外键索引
CREATE INDEX idx_update_user_id ON customer_follow_record(update_user_id);

-- 3. 删除旧的字段
ALTER TABLE customer_follow_record DROP COLUMN create_by;
ALTER TABLE customer_follow_record DROP COLUMN update_by;

-- 注意：执行此脚本前请备份数据库
-- 此脚本会删除 create_by 和 update_by 字段中的数据
