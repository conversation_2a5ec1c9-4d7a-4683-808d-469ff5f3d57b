from datetime import datetime
from sqlalchemy import <PERSON><PERSON>nteger, Column, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class CustomerFollowRecord(Base):
    """
    客户跟进记录表
    """
    __tablename__ = 'customer_follow_record'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='跟进记录ID')
    customer_id = Column(BigInteger, ForeignKey('customer.customer_id'), nullable=False, comment='客户ID')
    follow_content = Column(Text, nullable=False, comment='跟进内容')
    follow_time = Column(DateTime, nullable=False, comment='跟进时间')
    follow_user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment='跟进人用户ID')
    create_user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment='创建人用户ID')
    update_user_id = Column(BigInteger, Foreign<PERSON><PERSON>('sys_user.user_id'), nullable=True, comment='更新人用户ID')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 关联关系
    customer = relationship("Customer", back_populates="follow_records")
    follow_user = relationship("SysUser", foreign_keys=[follow_user_id])
    create_user = relationship("SysUser", foreign_keys=[create_user_id])
    update_user = relationship("SysUser", foreign_keys=[update_user_id])
