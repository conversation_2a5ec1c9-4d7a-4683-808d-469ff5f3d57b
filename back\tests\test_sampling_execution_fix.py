#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样执行页面修复测试
测试修复后的前端页面是否能正常工作
"""

import pytest
import requests
import json
from typing import Dict, Any


class TestSamplingExecutionFix:
    """采样执行页面修复测试类"""
    
    BASE_URL = "http://127.0.0.1:9099"
    HEADERS = {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json"
    }
    
    def test_user_executor_assignments_api(self):
        """测试获取用户执行任务分配接口"""
        # 使用测试用户ID
        test_user_id = 1
        url = f"{self.BASE_URL}/sampling/executor-assignment/user/{test_user_id}"
        
        response = requests.get(url, headers=self.HEADERS)
        
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        # 检查响应状态码
        assert response.status_code == 200, f"API调用失败，状态码: {response.status_code}"
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data, "响应中缺少code字段"
        assert "data" in response_data, "响应中缺少data字段"
        assert "msg" in response_data, "响应中缺少msg字段"
        
        # 检查成功状态
        assert response_data["code"] == 200, f"API返回错误: {response_data.get('msg', '未知错误')}"
        
        # 检查数据格式
        data = response_data["data"]
        assert isinstance(data, list), "返回的数据应该是列表格式"
        
        print(f"成功获取到 {len(data)} 条执行任务分配记录")
        
        # 如果有数据，检查数据结构
        if data:
            first_item = data[0]
            required_fields = ["id", "samplingTaskId", "userId", "status"]
            for field in required_fields:
                assert field in first_item, f"执行任务分配记录中缺少字段: {field}"
            
            print(f"第一条记录: {first_item}")
    
    def test_sampling_task_detail_api(self):
        """测试获取采样任务详情接口"""
        # 首先获取一个任务ID
        url = f"{self.BASE_URL}/sampling/task/page?pageNum=1&pageSize=1"
        response = requests.get(url, headers=self.HEADERS)
        
        assert response.status_code == 200, "获取任务列表失败"
        
        response_data = response.json()
        assert response_data["code"] == 200, "获取任务列表API返回错误"
        
        records = response_data["data"]["records"]
        if not records:
            print("没有找到采样任务，跳过详情测试")
            return
        
        task_id = records[0]["id"]
        
        # 测试获取任务详情
        detail_url = f"{self.BASE_URL}/sampling/task/get/{task_id}"
        detail_response = requests.get(detail_url, headers=self.HEADERS)
        
        print(f"任务详情请求URL: {detail_url}")
        print(f"任务详情响应状态码: {detail_response.status_code}")
        print(f"任务详情响应内容: {detail_response.text}")
        
        assert detail_response.status_code == 200, "获取任务详情失败"
        
        detail_data = detail_response.json()
        assert detail_data["code"] == 200, "获取任务详情API返回错误"
        
        task_detail = detail_data["data"]
        assert task_detail is not None, "任务详情数据为空"
        
        # 检查任务详情字段
        detail_fields = ["id", "taskName", "status"]
        for field in detail_fields:
            assert field in task_detail, f"任务详情中缺少字段: {field}"
        
        print(f"任务详情: {task_detail}")
    
    def test_dict_data_api(self):
        """测试字典数据接口"""
        url = f"{self.BASE_URL}/system/dict/data/type/sampling_task_status"
        response = requests.get(url, headers=self.HEADERS)
        
        print(f"字典数据请求URL: {url}")
        print(f"字典数据响应状态码: {response.status_code}")
        print(f"字典数据响应内容: {response.text}")
        
        assert response.status_code == 200, "获取字典数据失败"
        
        response_data = response.json()
        assert response_data["code"] == 200, "获取字典数据API返回错误"
        
        dict_data = response_data["data"]
        assert isinstance(dict_data, list), "字典数据应该是列表格式"
        
        print(f"获取到 {len(dict_data)} 条字典数据")
        
        # 如果有数据，检查数据结构
        if dict_data:
            first_item = dict_data[0]
            dict_fields = ["dictCode", "dictValue", "dictLabel"]
            for field in dict_fields:
                assert field in first_item, f"字典数据中缺少字段: {field}"
            
            print(f"第一条字典数据: {first_item}")
    
    def test_update_task_status_api(self):
        """测试更新任务状态接口"""
        # 首先获取一个任务
        url = f"{self.BASE_URL}/sampling/task/page?pageNum=1&pageSize=1"
        response = requests.get(url, headers=self.HEADERS)
        
        assert response.status_code == 200, "获取任务列表失败"
        
        response_data = response.json()
        assert response_data["code"] == 200, "获取任务列表API返回错误"
        
        records = response_data["data"]["records"]
        if not records:
            print("没有找到采样任务，跳过状态更新测试")
            return
        
        task = records[0]
        task_id = task["id"]
        original_status = task["status"]
        
        print(f"原始任务状态: {original_status}")
        
        # 测试更新任务状态（这里只是测试API是否可用，不实际修改）
        update_url = f"{self.BASE_URL}/sampling/task/update/{task_id}"
        update_data = {
            "id": task_id,
            "taskName": task["taskName"],
            "status": original_status  # 保持原状态不变
        }
        
        print(f"更新任务请求URL: {update_url}")
        print(f"更新任务请求数据: {update_data}")
        
        # 注意：这里只是测试API格式，实际不修改数据
        # update_response = requests.put(update_url, headers=self.HEADERS, json=update_data)
        # print(f"更新任务响应状态码: {update_response.status_code}")
        # print(f"更新任务响应内容: {update_response.text}")
        
        print("任务状态更新API格式测试完成")


if __name__ == "__main__":
    # 运行测试
    test_instance = TestSamplingExecutionFix()
    
    print("=== 开始测试采样执行页面修复 ===")
    
    try:
        print("\n1. 测试用户执行任务分配接口...")
        test_instance.test_user_executor_assignments_api()
        print("✓ 用户执行任务分配接口测试通过")
        
        print("\n2. 测试采样任务详情接口...")
        test_instance.test_sampling_task_detail_api()
        print("✓ 采样任务详情接口测试通过")
        
        print("\n3. 测试字典数据接口...")
        test_instance.test_dict_data_api()
        print("✓ 字典数据接口测试通过")
        
        print("\n4. 测试更新任务状态接口...")
        test_instance.test_update_task_status_api()
        print("✓ 更新任务状态接口测试通过")
        
        print("\n=== 所有测试通过！采样执行页面修复成功 ===")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        raise