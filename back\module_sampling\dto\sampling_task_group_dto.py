from typing import List, Optional
from pydantic import BaseModel, ConfigDict
from datetime import datetime
from pydantic.alias_generators import to_camel


class SamplingTaskGroupCreateDTO(BaseModel):
    """创建采样任务分组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: int
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    cycle_item_ids: List[int]
    assigned_user_ids: Optional[List[int]] = None


class SamplingTaskGroupUpdateDTO(BaseModel):
    """更新采样任务分组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    id: int
    assigned_user_ids: Optional[List[int]] = None
    status: Optional[int] = None


class SamplingTaskGroupDTO(BaseModel):
    """采样任务分组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    id: int
    sampling_task_id: int
    group_code: Optional[str] = None  # 分组编号
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    cycle_item_ids: List[int]
    assigned_user_ids: List[int]
    assigned_user_names: Optional[List[str]] = None
    status: Optional[int] = 0  # 分组状态：0-待执行，1-执行中，2-已完成
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None

    # 任务相关信息
    task_code: Optional[str] = None
    task_name: Optional[str] = None
    task_status: Optional[int] = None  # 重命名为task_status以区分分组状态
    project_name: Optional[str] = None
    customer_name: Optional[str] = None


class TaskGroupAssignmentRequestDTO(BaseModel):
    """任务分组执行人指派请求DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    task_id: int
    groups: List[SamplingTaskGroupUpdateDTO]
