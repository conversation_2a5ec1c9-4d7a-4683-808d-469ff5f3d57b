"""
测试采样任务分组状态修复功能
验证分组独立状态管理是否正常工作
"""
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from config.database import async_engine, AsyncSessionLocal
from module_sampling.service.sampling_task_group_service import SamplingTaskGroupService
from module_sampling.service.sample_record_service import SampleRecordService


class TestSamplingGroupStatusFix:
    """测试分组状态修复功能"""
    
    async def test_group_status_independence(self, db_session: AsyncSession):
        """测试分组状态独立性"""
        print("测试分组状态独立性...")
        
        # 创建分组服务
        group_service = SamplingTaskGroupService(db_session)
        
        # 获取现有的分组数据
        groups = await group_service.get_all_groups()
        if not groups:
            print("没有找到分组数据，跳过测试")
            return
        
        # 选择第一个分组进行测试
        test_group = groups[0]
        original_status = test_group.status
        print(f"测试分组ID: {test_group.id}, 原始状态: {original_status}")
        
        # 更新分组状态为执行中
        updated_group = await group_service.update_group_status(
            test_group.id, 1, 1  # 状态1=执行中，更新人ID=1
        )
        print(f"更新后状态: {updated_group.status}")
        assert updated_group.status == 1
        
        # 验证其他分组状态未受影响
        other_groups = await group_service.get_all_groups()
        for group in other_groups:
            if group.id != test_group.id:
                # 其他分组状态应该保持不变
                print(f"其他分组ID: {group.id}, 状态: {group.status}")
        
        # 恢复原始状态
        await group_service.update_group_status(
            test_group.id, original_status, 1
        )
        print("状态已恢复")
    
    async def test_sample_record_generation_for_specific_group(self, db_session: AsyncSession):
        """测试为特定分组生成样品记录"""
        print("测试为特定分组生成样品记录...")
        
        # 创建服务
        group_service = SamplingTaskGroupService(db_session)
        sample_service = SampleRecordService(db_session)
        
        # 获取一个待执行状态的分组
        groups = await group_service.get_all_groups()
        test_group = None
        for group in groups:
            if group.status == 0:  # 待执行状态
                test_group = group
                break
        
        if not test_group:
            print("没有找到待执行状态的分组，跳过测试")
            return
        
        print(f"测试分组ID: {test_group.id}")
        
        # 更新分组状态为执行中
        await group_service.update_group_status(test_group.id, 1, 1)
        
        # 为该分组生成样品记录
        try:
            sample_records = await sample_service.generate_sample_records_for_group(
                test_group.id, 1
            )
            print(f"为分组 {test_group.id} 生成了 {len(sample_records)} 条样品记录")
            
            # 验证样品记录确实生成了
            existing_records = await sample_service.get_sample_records_by_group_id(test_group.id)
            assert len(existing_records) > 0, "样品记录应该已经生成"
            print(f"验证成功：分组 {test_group.id} 有 {len(existing_records)} 条样品记录")
            
        except Exception as e:
            print(f"生成样品记录时出错: {e}")
            # 如果是因为已存在记录而跳过，这也是正常的
            if "已经存在样品记录" in str(e):
                print("样品记录已存在，这是正常的")
            else:
                raise
        
        # 恢复分组状态
        await group_service.update_group_status(test_group.id, 0, 1)
    
    async def test_api_update_group_status(self):
        """测试API更新分组状态"""
        print("测试API更新分组状态...")
        print("API测试需要httpx库，暂时跳过")


if __name__ == "__main__":
    async def run_tests():
        """运行测试"""
        test_instance = TestSamplingGroupStatusFix()
        
        # 创建数据库会话
        async with AsyncSessionLocal() as session:
            print("=== 开始测试分组状态独立性 ===")
            await test_instance.test_group_status_independence(session)
            
            print("\n=== 开始测试样品记录生成 ===")
            await test_instance.test_sample_record_generation_for_specific_group(session)
        
        # 测试API
        print("\n=== 开始测试API ===")
        await test_instance.test_api_update_group_status()
        
        print("\n=== 所有测试完成 ===")
    
    # 运行测试
    asyncio.run(run_tests())
