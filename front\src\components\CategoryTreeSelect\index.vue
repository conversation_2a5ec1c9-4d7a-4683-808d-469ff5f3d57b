<template>
  <div class="category-tree-select">
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="600px"
      append-to-body
      @close="handleClose"
    >
      <div class="tree-select-content">
        <!-- 搜索框 -->
        <el-input
          prefix-icon="Search"
          v-model="searchText"
          placeholder="请输入检测类别进行搜索"
          clearable
          @input="handleSearch"
          style="margin-bottom: 15px;"
        />        

        <!-- 树形选择器 -->
        <el-tree
          ref="treeRef"
          :data="filteredTreeData"
          :props="treeProps"
          show-checkbox
          node-key="key"
          :check-strictly="false"
          :default-expand-all="true"
          :check-on-click-node="true"
          :default-checked-keys="checkedKeys"
          :filter-node-method="filterNode"
          @check="handleCheck"
          style="max-height: 450px; overflow-y: auto;"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <span :class="{'tree-node-label': true, 'tree-node-disabled': !data.selectable}">
                {{ node.label }}
              </span>
            </span>
          </template>
        </el-tree>

        <!-- 已选择的类别显示 -->
        <div v-if="selectedCategories.length > 0" class="selected-categories">
          <el-divider content-position="left">已选择的检测类别</el-divider>
          <el-tag
            v-for="category in selectedCategories"
            :key="category"
            closable
            @close="removeCategory(category)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ category }}
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button icon="Refresh" @click="clearSelectedCategories">
          清空所有已选类别
          </el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { getCategoryTree } from '@/api/basedata/technicalManual'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择检测类别'
  },
  selectedValues: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 对话框可见性
const dialogVisible = ref(false)
// 搜索文本
const searchText = ref('')
// 树形数据
const treeData = ref([])
// 过滤后的树形数据
const filteredTreeData = ref([])
// 树形组件引用
const treeRef = ref(null)
// 已选中的键
const checkedKeys = ref([])
// 已选择的类别
const selectedCategories = ref([])

// 树形属性配置
const treeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听selectedValues变化
watch(() => props.selectedValues, (newVal) => {
  selectedCategories.value = [...newVal]
  updateCheckedKeys()
}, { immediate: true })

// 初始化数据
async function initData() {
  try {
    // 获取树形数据
    const response = await getCategoryTree()
    treeData.value = response.data || []
    filteredTreeData.value = [...treeData.value]

    // 设置已选中的类别
    selectedCategories.value = [...props.selectedValues]
    updateCheckedKeys()

    // 等待DOM更新后设置选中状态
    await nextTick()
    if (treeRef.value) {
      treeRef.value.setCheckedKeys(checkedKeys.value)
    }
  } catch (error) {
    console.error('获取检测类别树形数据失败:', error)
    ElMessage.error('获取检测类别数据失败')
  }
}

// 更新选中的键
function updateCheckedKeys() {
  checkedKeys.value = []

  // 遍历树形数据，找到对应的键
  function findKeys(nodes) {
    for (const node of nodes) {
      if (node.selectable && selectedCategories.value.includes(node.value)) {
        checkedKeys.value.push(node.key)
      }
      if (node.children && node.children.length > 0) {
        findKeys(node.children)
      }
    }
  }

  findKeys(treeData.value)
}

// 处理搜索
function handleSearch() {
  if (treeRef.value) {
    treeRef.value.filter(searchText.value)
  }
}

// 过滤节点方法
function filterNode(value, data) {
  if (!value) return true
  return data.label.includes(value)
}

// 处理选中状态变化
function handleCheck(data, checked) {
  // 只处理可选择的节点（检测类别）
  if (!data.selectable) return

  const category = data.value

  if (checked.checkedKeys.includes(data.key)) {
    // 添加到已选择列表
    if (!selectedCategories.value.includes(category)) {
      selectedCategories.value.push(category)
    }
  } else {
    // 从已选择列表中移除
    const index = selectedCategories.value.indexOf(category)
    if (index > -1) {
      selectedCategories.value.splice(index, 1)
    }
  }
}
// 清空已选类别
function clearSelectedCategories() {
  selectedCategories.value = []
  checkedKeys.value = []
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([])
  }
}

// 移除类别
function removeCategory(category) {
  const index = selectedCategories.value.indexOf(category)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)

    // 更新树形选择器的选中状态
    updateCheckedKeys()
    if (treeRef.value) {
      treeRef.value.setCheckedKeys(checkedKeys.value)
    }
  }
}

// 处理确认
function handleConfirm() {
  emit('confirm', [...selectedCategories.value])
  handleClose()
}

// 处理关闭
function handleClose() {
  dialogVisible.value = false
  emit('update:visible', false)

  // 重置搜索
  searchText.value = ''
  if (treeRef.value) {
    treeRef.value.filter('')
  }
}
</script>

<style scoped>
.category-tree-select {
  width: 100%;
}

.tree-select-content {
  padding: 10px 0;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-node-label {
  flex: 1;
  font-size: 14px;
}

.tree-node-disabled {
  color: #909399;
  font-weight: bold;
}

.selected-categories {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 40px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 自定义树形组件样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}

/* 不可选择节点的样式 */
:deep(.el-tree-node.is-disabled > .el-tree-node__content .el-checkbox) {
  display: none !important;
}

:deep(.el-tree-node.is-disabled > .el-tree-node__content) {
  cursor: default;
}

:deep(.el-tree-node.is-disabled > .el-tree-node__content:hover) {
  background-color: transparent;
}
</style>
