-- 创建样品记录表
-- 执行时间：2025-01-XX
-- 说明：创建样品记录表，用于管理采样任务执行过程中的样品记录

-- 创建样品记录表
CREATE TABLE `sample_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_assignment_id` bigint NOT NULL COMMENT '采样任务执行指派ID',
  `detection_cycle_item_id` bigint NOT NULL COMMENT '检测周期条目ID',
  `project_quotation_item_id` int NOT NULL COMMENT '项目报价明细ID',
  `sample_number` int NOT NULL COMMENT '样品序号（从1开始）',
  `sample_type` varchar(50) DEFAULT NULL COMMENT '样品类型',
  `sample_source` varchar(50) DEFAULT NULL COMMENT '样品来源',
  `point_name` varchar(200) DEFAULT NULL COMMENT '点位名称',
  `cycle_number` int NOT NULL COMMENT '周期序号',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '周期类型',
  `detection_category` varchar(100) DEFAULT NULL COMMENT '检测类别',
  `detection_parameter` varchar(100) DEFAULT NULL COMMENT '检测参数',
  `detection_method` varchar(200) DEFAULT NULL COMMENT '检测方法',
  `status` int DEFAULT '0' COMMENT '样品状态：0-待采集，1-已采集，2-已送检，3-检测中，4-已完成',
  `collection_time` datetime DEFAULT NULL COMMENT '采集时间',
  `submission_time` datetime DEFAULT NULL COMMENT '送检时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` text COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sample_record_assignment_id` (`sampling_task_assignment_id`),
  KEY `idx_sample_record_cycle_item_id` (`detection_cycle_item_id`),
  KEY `idx_sample_record_quotation_item_id` (`project_quotation_item_id`),
  KEY `idx_sample_record_status` (`status`),
  KEY `idx_sample_record_sample_number` (`sample_number`),
  CONSTRAINT `fk_sample_record_assignment` FOREIGN KEY (`sampling_task_assignment_id`) REFERENCES `sampling_task_executor_assignment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_record_cycle_item` FOREIGN KEY (`detection_cycle_item_id`) REFERENCES `detection_cycle_item` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_record_quotation_item` FOREIGN KEY (`project_quotation_item_id`) REFERENCES `project_quotation_item` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_record_create_by` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`),
  CONSTRAINT `fk_sample_record_update_by` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品记录表';

-- 验证表创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'sample_record';

-- 验证字段创建成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'sample_record'
ORDER BY ORDINAL_POSITION;

-- 验证索引创建成功
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'sample_record'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 验证外键约束创建成功
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'sample_record'
    AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 插入样品状态字典数据（如果字典表存在）
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (NULL, '样品状态', 'sample_record_status', '0', 'admin', NOW(), '样品记录状态字典');

-- 获取字典类型ID
SET @dict_type_id = (SELECT dict_id FROM sys_dict_type WHERE dict_type = 'sample_record_status');

-- 插入字典数据项
INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark)
VALUES 
(NULL, 1, '待采集', '0', 'sample_record_status', '', 'warning', 'Y', '0', 'admin', NOW(), '样品待采集状态'),
(NULL, 2, '已采集', '1', 'sample_record_status', '', 'success', 'N', '0', 'admin', NOW(), '样品已采集状态'),
(NULL, 3, '已送检', '2', 'sample_record_status', '', 'info', 'N', '0', 'admin', NOW(), '样品已送检状态'),
(NULL, 4, '检测中', '3', 'sample_record_status', '', 'primary', 'N', '0', 'admin', NOW(), '样品检测中状态'),
(NULL, 5, '已完成', '4', 'sample_record_status', '', 'success', 'N', '0', 'admin', NOW(), '样品已完成状态');

-- 验证字典数据插入成功
SELECT 
    dt.dict_name,
    dd.dict_label,
    dd.dict_value,
    dd.list_class
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'sample_record_status'
ORDER BY dd.dict_sort;
