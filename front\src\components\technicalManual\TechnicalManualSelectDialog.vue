<template>
  <el-dialog
    title="选择技术手册"
    v-model="dialogVisible"
    width="1000px"
    :before-close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryForm" :inline="true" label-width="80px" style="margin-bottom: 20px">
      <el-form-item label="检测类别">
        <el-input
          v-model="queryForm.category"
          placeholder="请输入检测类别"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="检测参数">
        <el-input
          v-model="queryForm.parameter"
          placeholder="请输入检测参数"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="检测方法">
        <el-input
          v-model="queryForm.method"
          placeholder="请输入检测方法"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="category" label="检测类别" width="150" />
      <el-table-column prop="parameter" label="检测参数" width="200" />
      <el-table-column prop="method" label="检测方法" width="200" />
      <el-table-column prop="qualificationCode" label="资质编号" width="150" />
      <el-table-column prop="limitationScope" label="限制范围" min-width="200" />
    </el-table>

    <!-- 分页 -->
    <el-pagination
      :current-page="pagination.pageNum"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <template #footer>
      <div class="dialog-footer">
        <span style="margin-right: 20px; color: #909399">
          已选择 {{ selectedRows.length }} 项
        </span>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
// import { Search, Refresh } from '@element-plus/icons-vue'
import { pageTechnicalManual } from '@/api/basedata/technicalManual'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const tableRef = ref()
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 查询表单
const queryForm = reactive({
  parameter: '',
  method: '',
  category: ''
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const response = await pageTechnicalManual(params)
    tableData.value = response.data.rows || []
    pagination.total = response.data.total || 0
    
    // 设置已选中的行
    await nextTick()
    setSelectedRows()
  } catch (error) {
    ElMessage.error('获取技术手册列表失败')
  } finally {
    loading.value = false
  }
}

const setSelectedRows = () => {
  if (tableRef.value && props.selectedIds.length > 0) {
    tableData.value.forEach(row => {
      if (props.selectedIds.includes(row.id)) {
        tableRef.value.toggleRowSelection(row, true)
      }
    })
  }
}

const handleQuery = () => {
  pagination.pageNum = 1
  getList()
}

const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = ''
  })
  handleQuery()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getList()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getList()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleClose = () => {
  selectedRows.value = []
  emit('update:visible', false)
}

const handleConfirm = () => {
  // 合并当前选中的和之前选中的（去重）
  const allSelected = [...selectedRows.value]
  
  // 添加之前选中但不在当前页的项
  const currentPageIds = tableData.value.map(item => item.id)
  const previousSelected = tableData.value.filter(item => 
    props.selectedIds.includes(item.id) && !currentPageIds.includes(item.id)
  )
  
  allSelected.push(...previousSelected)
  
  // 去重
  const uniqueSelected = allSelected.filter((item, index, self) => 
    index === self.findIndex(t => t.id === item.id)
  )
  
  emit('confirm', uniqueSelected)
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    getList()
  }
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getList()
  }
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
