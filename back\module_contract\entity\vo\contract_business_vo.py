from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class ContractBusinessDepartmentModel(BaseModel):
    """合同商务信息-部门分摊模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(None, description="主键ID")
    contract_id: Optional[int] = Field(None, description="合同ID")
    project_code: str = Field(..., description="项目报价编号", max_length=100)
    dept_id: int = Field(..., description="部门ID")
    dept_name: Optional[str] = Field(None, description="部门名称")
    allocation_amount: Decimal = Field(..., description="分摊金额")


class ContractBusinessTaskModel(BaseModel):
    """合同商务信息-任务拆解模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(None, description="主键ID")
    contract_id: Optional[int] = Field(None, description="合同ID")
    project_code: str = Field(..., description="项目报价编号", max_length=100)
    task_code: str = Field(..., description="任务编号", max_length=100)
    task_amount: Decimal = Field(..., description="任务金额")


class ContractBusinessInfoModel(BaseModel):
    """合同商务信息完整模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    departments: List[ContractBusinessDepartmentModel] = Field(default_factory=list, description="部门分摊信息")
    tasks: List[ContractBusinessTaskModel] = Field(default_factory=list, description="任务拆解信息")
