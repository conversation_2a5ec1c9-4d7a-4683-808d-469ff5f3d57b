#!/bin/bash
# 前端部署脚本
# 作用：构建前端代码，配置Nginx，将前端文件部署到/var/www/html

set -e

echo "===== 开始部署前端 ====="

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
read -p "应用部署路径 [默认: $PROJECT_ROOT]: " APP_PATH
APP_PATH=${APP_PATH:-$PROJECT_ROOT}

read -p "前端目录名称 [默认: front]: " FRONTEND_DIR
FRONTEND_DIR=${FRONTEND_DIR:-front}
FRONTEND_PATH="$APP_PATH/$FRONTEND_DIR"

read -p "环境配置 [默认: prod]: " ENV
ENV=${ENV:-prod}

read -p "域名 [默认: localhost]: " DOMAIN
DOMAIN=${DOMAIN:-localhost}

read -p "后端API端口 [默认: 9099]: " API_PORT
API_PORT=${API_PORT:-9099}

# 配置前端部署目录
read -p "前端部署目录 [默认: /var/www/html]: " DEPLOY_DIR
DEPLOY_DIR=${DEPLOY_DIR:-/var/www/html}

# 确保前端目录存在
if [ ! -d "$FRONTEND_PATH" ]; then
    echo "错误: 前端目录 $FRONTEND_PATH 不存在"
    exit 1
fi

# 进入前端目录
cd "$FRONTEND_PATH"
echo "当前工作目录: $(pwd)"

# 安装依赖
echo "安装Node.js依赖..."
npm install --registry=https://registry.npmmirror.com

# 构建前端
echo "构建前端代码..."
if [ "$ENV" = "prod" ]; then
    npm run build:prod
elif [ "$ENV" = "stage" ]; then
    npm run build:stage
else
    echo "警告: 未知环境 $ENV，使用生产环境构建"
    npm run build:prod
fi

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "错误: 前端构建失败，dist目录不存在"
    exit 1
fi

echo "前端构建完成"

# 检查部署目录是否存在，如果不存在则创建
if [ ! -d "$DEPLOY_DIR" ]; then
    echo "部署目录 $DEPLOY_DIR 不存在，尝试创建..."
    if sudo mkdir -p "$DEPLOY_DIR"; then
        echo "成功创建部署目录 $DEPLOY_DIR"
    else
        echo "错误: 无法创建部署目录 $DEPLOY_DIR，请确保有足够的权限"
        exit 1
    fi
fi

# 复制前端文件到部署目录
echo "正在将前端文件复制到 $DEPLOY_DIR..."
if sudo cp -r dist/* "$DEPLOY_DIR/"; then
    echo "前端文件已成功复制到 $DEPLOY_DIR"

    # 设置正确的权限
    sudo chown -R www-data:www-data "$DEPLOY_DIR"
    sudo chmod -R 755 "$DEPLOY_DIR"
    echo "已设置正确的文件权限"
else
    echo "错误: 无法复制前端文件到 $DEPLOY_DIR，请确保有足够的权限"
    exit 1
fi

# 创建Nginx配置文件
NGINX_CONF="$SCRIPT_DIR/lims-nginx.conf"
cat > "$NGINX_CONF" << EOF
server {
    listen 4000;
    server_name $DOMAIN;

    # 前端静态文件
    location / {
        root $DEPLOY_DIR;
        try_files \$uri \$uri/ /index.html;
        index index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
    }

    # 后端API代理
    location /$ENV-api/ {
        proxy_pass http://127.0.0.1:$API_PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 上传文件访问
    location /profile/ {
        alias $APP_PATH/vf_admin/upload_path/;

        # 限制文件类型
        location ~* \.(php|php5|sh|pl|py)$ {
            deny all;
        }
    }

    # 日志配置
    access_log /var/log/nginx/lims-access.log;
    error_log /var/log/nginx/lims-error.log;
}
EOF

echo "Nginx配置文件已生成: $NGINX_CONF"
echo "请以root权限执行以下命令安装Nginx配置:"

# 检测Nginx配置目录
if [ -d "/etc/nginx/sites-available" ]; then
    # Debian/Ubuntu
    echo "sudo cp $NGINX_CONF /etc/nginx/sites-available/lims"
    echo "sudo ln -sf /etc/nginx/sites-available/lims /etc/nginx/sites-enabled/"
    echo "sudo rm -f /etc/nginx/sites-enabled/default" # 移除默认配置
else
    # CentOS/RHEL
    echo "sudo cp $NGINX_CONF /etc/nginx/conf.d/lims.conf"
fi

echo "sudo nginx -t"
echo "sudo systemctl restart nginx"

echo "===== 前端部署摘要 ====="
echo "1. 前端代码已构建并部署到: $DEPLOY_DIR"
echo "2. Nginx配置文件已生成: $NGINX_CONF"
echo "3. 请确保Nginx已安装并运行"
echo "4. 请确保防火墙已开放80端口"
echo "5. 访问地址: http://$DOMAIN"
echo "===== 前端部署完成 ====="
