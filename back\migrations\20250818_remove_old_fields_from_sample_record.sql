-- 删除采样记录表中的旧字段
-- 执行时间: 2025-08-18

-- 1. 删除外键约束（使用正确的约束名称）
ALTER TABLE sample_record
DROP FOREIGN KEY sample_record_ibfk_1;

ALTER TABLE sample_record
DROP FOREIGN KEY sample_record_ibfk_2;

ALTER TABLE sample_record
DROP FOREIGN KEY sample_record_ibfk_3;

-- 2. 删除检查约束
ALTER TABLE sample_record
DROP CONSTRAINT chk_sample_record_association;

-- 3. 删除字段
ALTER TABLE sample_record
DROP COLUMN sampling_task_assignment_id;

ALTER TABLE sample_record
DROP COLUMN detection_cycle_item_id;

ALTER TABLE sample_record
DROP COLUMN project_quotation_item_id;

-- 4. 更新现有记录，将NULL的分组ID设置为一个默认值（如果需要）
-- UPDATE sample_record SET sampling_task_group_id = 1 WHERE sampling_task_group_id IS NULL;

-- 5. 添加新的检查约束，确保分组ID不为空
ALTER TABLE sample_record
ADD CONSTRAINT chk_sample_record_group_required
CHECK (sampling_task_group_id IS NOT NULL);

-- 注意：
-- 1. 执行前请确保已经将所有数据迁移到新的分组系统
-- 2. 删除这些字段后，旧的执行任务指派系统将无法使用
-- 3. 建议在执行前备份数据库
