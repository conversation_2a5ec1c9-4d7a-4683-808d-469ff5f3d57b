-- 修改sys_user表的user_id字段类型（如果需要）
-- 注意：如果数据库中user_id已经是bigint类型，则不需要执行此操作
-- ALTER TABLE sys_user MODIFY user_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID';

-- 修改sys_user_role表的user_id字段类型
ALTER TABLE sys_user_role MODIFY user_id bigint(20) NOT NULL COMMENT '用户ID';

-- 修改sys_user_post表的user_id字段类型
ALTER TABLE sys_user_post MODIFY user_id bigint(20) NOT NULL COMMENT '用户ID';

-- 重新创建customer_internal_manager表的外键约束
-- 先删除现有的外键约束（如果存在）
ALTER TABLE customer_internal_manager DROP FOREIGN KEY IF EXISTS customer_internal_manager_ibfk_1;
ALTER TABLE customer_internal_manager DROP FOREIGN KEY IF EXISTS customer_internal_manager_ibfk_2;

-- 添加正确的外键约束
ALTER TABLE customer_internal_manager 
ADD CONSTRAINT customer_internal_manager_ibfk_1 
FOREIGN KEY (customer_id) REFERENCES customer (customer_id);

ALTER TABLE customer_internal_manager 
ADD CONSTRAINT customer_internal_manager_ibfk_2 
FOREIGN KEY (user_id) REFERENCES sys_user (user_id);