-- 技术手册表字段更新脚本
-- 删除不需要的字段，添加新字段

-- 删除不需要的字段
ALTER TABLE technical_manual DROP COLUMN IF EXISTS point_name;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS point_count;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS cycle_type;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS cycle_count;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS frequency;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS sample_count;
ALTER TABLE technical_manual DROP COLUMN IF EXISTS service_type;

-- 添加新字段
ALTER TABLE technical_manual ADD COLUMN IF NOT EXISTS classification VARCHAR(50) COMMENT '分类';
ALTER TABLE technical_manual ADD COLUMN IF NOT EXISTS qualification_code VARCHAR(50) UNIQUE COMMENT '资质编号';
ALTER TABLE technical_manual ADD COLUMN IF NOT EXISTS limitation_scope TEXT COMMENT '限制范围';
ALTER TABLE technical_manual ADD COLUMN IF NOT EXISTS common_alias VARCHAR(200) COMMENT '常用别名';
ALTER TABLE technical_manual ADD COLUMN IF NOT EXISTS qualification_date DATE COMMENT '取得资质时间';

-- 创建资质编号唯一索引（如果不存在）
CREATE UNIQUE INDEX IF NOT EXISTS idx_technical_manual_qualification_code 
ON technical_manual(qualification_code) 
WHERE qualification_code IS NOT NULL AND qualification_code != '';
