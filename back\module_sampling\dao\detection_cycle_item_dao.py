from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import and_, desc, select, update, delete, func

from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem


class DetectionCycleItemDao:
    """检测周期条目数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_detection_cycle_item(self, cycle_item: DetectionCycleItem) -> DetectionCycleItem:
        """创建检测周期条目"""
        self.db.add(cycle_item)
        await self.db.flush()
        return cycle_item
    
    async def batch_create_detection_cycle_items(self, cycle_items: List[DetectionCycleItem]) -> List[DetectionCycleItem]:
        """批量创建检测周期条目"""
        self.db.add_all(cycle_items)
        await self.db.flush()
        return cycle_items
    
    async def get_detection_cycle_item_by_id(self, item_id: int) -> Optional[DetectionCycleItem]:
        """根据ID获取检测周期条目"""
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation),
            selectinload(DetectionCycleItem.project_quotation_item)
        ).where(DetectionCycleItem.id == item_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_detection_cycle_items_by_ids(self, item_ids: List[int]) -> List[DetectionCycleItem]:
        """根据ID列表获取检测周期条目"""
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation),
            selectinload(DetectionCycleItem.project_quotation_item)
        ).where(DetectionCycleItem.id.in_(item_ids))
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def update_detection_cycle_item(self, cycle_item: DetectionCycleItem) -> DetectionCycleItem:
        """更新检测周期条目"""
        await self.db.merge(cycle_item)
        await self.db.flush()
        return cycle_item
    
    async def batch_update_status(self, item_ids: List[int], status: int, update_by: int) -> bool:
        """批量更新状态"""
        stmt = update(DetectionCycleItem).where(
            DetectionCycleItem.id.in_(item_ids)
        ).values(
            status=status,
            update_by=update_by
        )
        await self.db.execute(stmt)
        await self.db.flush()
        return True
    
    async def delete_detection_cycle_item(self, item_id: int) -> bool:
        """删除检测周期条目"""
        stmt = select(DetectionCycleItem).where(DetectionCycleItem.id == item_id)
        result = await self.db.execute(stmt)
        item = result.scalar_one_or_none()
        if item:
            await self.db.delete(item)
            await self.db.flush()
            return True
        return False
    
    async def get_items_by_project_quotation_id(self, project_quotation_id: int) -> List[DetectionCycleItem]:
        """根据项目报价ID获取检测周期条目列表"""
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation_item),
            selectinload(DetectionCycleItem.project_quotation)
        ).where(
            DetectionCycleItem.project_quotation_id == project_quotation_id
        ).order_by(
            DetectionCycleItem.project_quotation_item_id,
            DetectionCycleItem.cycle_number
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_items_by_project_quotation_item_id(self, project_quotation_item_id: int) -> List[DetectionCycleItem]:
        """根据项目报价明细ID获取检测周期条目列表"""
        stmt = select(DetectionCycleItem).where(
            DetectionCycleItem.project_quotation_item_id == project_quotation_item_id
        ).order_by(DetectionCycleItem.cycle_number)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_unassigned_items_by_project_quotation_id(self, project_quotation_id: int) -> List[DetectionCycleItem]:
        """根据项目报价ID获取未分配的检测周期条目列表"""
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation_item),
            selectinload(DetectionCycleItem.project_quotation)
        ).where(
            and_(
                DetectionCycleItem.project_quotation_id == project_quotation_id,
                DetectionCycleItem.status == 0  # 未分配状态
            )
        ).order_by(
            DetectionCycleItem.project_quotation_item_id,
            DetectionCycleItem.cycle_number
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_items_by_status(self, status: int) -> List[DetectionCycleItem]:
        """根据状态获取检测周期条目列表"""
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation),
            selectinload(DetectionCycleItem.project_quotation_item)
        ).where(DetectionCycleItem.status == status)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def page_detection_cycle_items(self, 
                                  page: int = 1, 
                                  size: int = 10,
                                  project_quotation_id: Optional[int] = None,
                                  project_quotation_item_id: Optional[int] = None,
                                  status: Optional[int] = None) -> Tuple[List[DetectionCycleItem], int]:
        """分页查询检测周期条目"""
        # 构建查询条件
        conditions = []
        
        if project_quotation_id:
            conditions.append(DetectionCycleItem.project_quotation_id == project_quotation_id)
        
        if project_quotation_item_id:
            conditions.append(DetectionCycleItem.project_quotation_item_id == project_quotation_item_id)
        
        if status is not None:
            conditions.append(DetectionCycleItem.status == status)
        
        # 构建基础查询条件
        where_clause = and_(*conditions) if conditions else True
        
        # 获取总数
        count_stmt = select(func.count(DetectionCycleItem.id)).where(where_clause)
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        # 分页查询
        stmt = select(DetectionCycleItem).options(
            selectinload(DetectionCycleItem.project_quotation),
            selectinload(DetectionCycleItem.project_quotation_item)
        ).where(where_clause).order_by(
            DetectionCycleItem.project_quotation_id,
            DetectionCycleItem.project_quotation_item_id,
            DetectionCycleItem.cycle_number
        ).offset((page - 1) * size).limit(size)
        
        result = await self.db.execute(stmt)
        items = result.scalars().all()
        
        return items, total
    
    async def check_item_exists(self, project_quotation_item_id: int, cycle_number: int) -> bool:
        """检查检测周期条目是否存在"""
        stmt = select(DetectionCycleItem).where(
            and_(
                DetectionCycleItem.project_quotation_item_id == project_quotation_item_id,
                DetectionCycleItem.cycle_number == cycle_number
            )
        )
        result = await self.db.execute(stmt)
        item = result.scalar_one_or_none()
        return item is not None
    
    async def get_max_cycle_number(self, project_quotation_item_id: int) -> int:
        """获取指定项目报价明细的最大周期序号"""
        stmt = select(DetectionCycleItem.cycle_number).where(
            DetectionCycleItem.project_quotation_item_id == project_quotation_item_id
        ).order_by(desc(DetectionCycleItem.cycle_number))
        result = await self.db.execute(stmt)
        max_cycle = result.scalar_one_or_none()
        
        return max_cycle if max_cycle else 0
    
    async def delete_items_by_project_quotation_id(self, project_quotation_id: int) -> bool:
        """根据项目报价ID删除所有检测周期条目"""
        stmt = delete(DetectionCycleItem).where(
            DetectionCycleItem.project_quotation_id == project_quotation_id
        )
        await self.db.execute(stmt)
        await self.db.flush()
        return True